var zf=Object.defineProperty;var Wf=(e,t,n)=>t in e?zf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var nt=(e,t,n)=>(Wf(e,typeof t!="symbol"?t+"":t,n),n);const Kf=function(){const t=document.createElement("link").relList;return t&&t.supports&&t.supports("modulepreload")?"modulepreload":"preload"}(),Ki={},Qf="/",ke=function(t,n){return!n||n.length===0?t():Promise.all(n.map(r=>{if(r=`${Qf}${r}`,r in Ki)return;Ki[r]=!0;const o=r.endsWith(".css"),s=o?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${r}"]${s}`))return;const i=document.createElement("link");if(i.rel=o?"stylesheet":Kf,o||(i.as="script",i.crossOrigin=""),i.href=r,document.head.appendChild(i),o)return new Promise((l,a)=>{i.addEventListener("load",l),i.addEventListener("error",()=>a(new Error(`Unable to preload CSS for ${r}`)))})})).then(()=>t())};/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ci(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ye={},Ln=[],mt=()=>{},Gf=()=>!1,ko=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),fi=e=>e.startsWith("onUpdate:"),Te=Object.assign,di=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Jf=Object.prototype.hasOwnProperty,me=(e,t)=>Jf.call(e,t),re=Array.isArray,Dn=e=>Ar(e)==="[object Map]",Ka=e=>Ar(e)==="[object Set]",Xf=e=>Ar(e)==="[object RegExp]",ie=e=>typeof e=="function",Ce=e=>typeof e=="string",Yt=e=>typeof e=="symbol",xe=e=>e!==null&&typeof e=="object",Qa=e=>(xe(e)||ie(e))&&ie(e.then)&&ie(e.catch),Ga=Object.prototype.toString,Ar=e=>Ga.call(e),Yf=e=>Ar(e).slice(8,-1),Ja=e=>Ar(e)==="[object Object]",hi=e=>Ce(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,nr=ci(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Ao=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Zf=/-(\w)/g,ut=Ao(e=>e.replace(Zf,(t,n)=>n?n.toUpperCase():"")),ed=/\B([A-Z])/g,vn=Ao(e=>e.replace(ed,"-$1").toLowerCase()),To=Ao(e=>e.charAt(0).toUpperCase()+e.slice(1)),Jo=Ao(e=>e?`on${To(e)}`:""),Qt=(e,t)=>!Object.is(e,t),rr=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Xa=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},td=e=>{const t=parseFloat(e);return isNaN(t)?e:t},nd=e=>{const t=Ce(e)?Number(e):NaN;return isNaN(t)?e:t};let Qi;const Ro=()=>Qi||(Qi=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{});function pi(e){if(re(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=Ce(r)?id(r):pi(r);if(o)for(const s in o)t[s]=o[s]}return t}else if(Ce(e)||xe(e))return e}const rd=/;(?![^(]*\))/g,od=/:([^]+)/,sd=/\/\*[^]*?\*\//g;function id(e){const t={};return e.replace(sd,"").split(rd).forEach(n=>{if(n){const r=n.split(od);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function mi(e){let t="";if(Ce(e))t=e;else if(re(e))for(let n=0;n<e.length;n++){const r=mi(e[n]);r&&(t+=r+" ")}else if(xe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ld="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ad=ci(ld);function Ya(e){return!!e||e===""}const Za=e=>!!(e&&e.__v_isRef===!0),ud=e=>Ce(e)?e:e==null?"":re(e)||xe(e)&&(e.toString===Ga||!ie(e.toString))?Za(e)?ud(e.value):JSON.stringify(e,eu,2):String(e),eu=(e,t)=>Za(t)?eu(e,t.value):Dn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],s)=>(n[Xo(r,s)+" =>"]=o,n),{})}:Ka(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Xo(n))}:Yt(t)?Xo(t):xe(t)&&!re(t)&&!Ja(t)?String(t):t,Xo=(e,t="")=>{var n;return Yt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ue;class tu{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ue,!t&&Ue&&(this.index=(Ue.scopes||(Ue.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ue;try{return Ue=this,t()}finally{Ue=n}}}on(){Ue=this}off(){Ue=this.parent}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function nu(e){return new tu(e)}function ru(){return Ue}function cd(e,t=!1){Ue&&Ue.cleanups.push(e)}let we;const Yo=new WeakSet;class ou{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ue&&Ue.active&&Ue.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yo.has(this)&&(Yo.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||iu(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gi(this),lu(this);const t=we,n=gt;we=this,gt=!0;try{return this.fn()}finally{au(this),we=t,gt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)yi(t);this.deps=this.depsTail=void 0,Gi(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yo.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Rs(this)&&this.run()}get dirty(){return Rs(this)}}let su=0,or,sr;function iu(e,t=!1){if(e.flags|=8,t){e.next=sr,sr=e;return}e.next=or,or=e}function gi(){su++}function vi(){if(--su>0)return;if(sr){let t=sr;for(sr=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;or;){let t=or;for(or=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function lu(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function au(e){let t,n=e.depsTail,r=n;for(;r;){const o=r.prevDep;r.version===-1?(r===n&&(n=o),yi(r),fd(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function Rs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(uu(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function uu(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===dr))return;e.globalVersion=dr;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Rs(e)){e.flags&=-3;return}const n=we,r=gt;we=e,gt=!0;try{lu(e);const o=e.fn(e._value);(t.version===0||Qt(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{we=n,gt=r,au(e),e.flags&=-3}}function yi(e,t=!1){const{dep:n,prevSub:r,nextSub:o}=e;if(r&&(r.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let s=n.computed.deps;s;s=s.nextDep)yi(s,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function fd(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let gt=!0;const cu=[];function Zt(){cu.push(gt),gt=!1}function en(){const e=cu.pop();gt=e===void 0?!0:e}function Gi(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=we;we=void 0;try{t()}finally{we=n}}}let dr=0;class dd{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class bi{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!we||!gt||we===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==we)n=this.activeLink=new dd(we,this),we.deps?(n.prevDep=we.depsTail,we.depsTail.nextDep=n,we.depsTail=n):we.deps=we.depsTail=n,fu(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=we.depsTail,n.nextDep=void 0,we.depsTail.nextDep=n,we.depsTail=n,we.deps===n&&(we.deps=r)}return n}trigger(t){this.version++,dr++,this.notify(t)}notify(t){gi();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{vi()}}}function fu(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)fu(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const io=new WeakMap,cn=Symbol(""),Ps=Symbol(""),hr=Symbol("");function Ne(e,t,n){if(gt&&we){let r=io.get(e);r||io.set(e,r=new Map);let o=r.get(n);o||(r.set(n,o=new bi),o.map=r,o.key=n),o.track()}}function Pt(e,t,n,r,o,s){const i=io.get(e);if(!i){dr++;return}const l=a=>{a&&a.trigger()};if(gi(),t==="clear")i.forEach(l);else{const a=re(e),c=a&&hi(n);if(a&&n==="length"){const u=Number(r);i.forEach((f,d)=>{(d==="length"||d===hr||!Yt(d)&&d>=u)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),c&&l(i.get(hr)),t){case"add":a?c&&l(i.get("length")):(l(i.get(cn)),Dn(e)&&l(i.get(Ps)));break;case"delete":a||(l(i.get(cn)),Dn(e)&&l(i.get(Ps)));break;case"set":Dn(e)&&l(i.get(cn));break}}vi()}function hd(e,t){const n=io.get(e);return n&&n.get(t)}function En(e){const t=le(e);return t===e?t:(Ne(t,"iterate",hr),at(e)?t:t.map(Ve))}function Po(e){return Ne(e=le(e),"iterate",hr),e}const pd={__proto__:null,[Symbol.iterator](){return Zo(this,Symbol.iterator,Ve)},concat(...e){return En(this).concat(...e.map(t=>re(t)?En(t):t))},entries(){return Zo(this,"entries",e=>(e[1]=Ve(e[1]),e))},every(e,t){return At(this,"every",e,t,void 0,arguments)},filter(e,t){return At(this,"filter",e,t,n=>n.map(Ve),arguments)},find(e,t){return At(this,"find",e,t,Ve,arguments)},findIndex(e,t){return At(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return At(this,"findLast",e,t,Ve,arguments)},findLastIndex(e,t){return At(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return At(this,"forEach",e,t,void 0,arguments)},includes(...e){return es(this,"includes",e)},indexOf(...e){return es(this,"indexOf",e)},join(e){return En(this).join(e)},lastIndexOf(...e){return es(this,"lastIndexOf",e)},map(e,t){return At(this,"map",e,t,void 0,arguments)},pop(){return zn(this,"pop")},push(...e){return zn(this,"push",e)},reduce(e,...t){return Ji(this,"reduce",e,t)},reduceRight(e,...t){return Ji(this,"reduceRight",e,t)},shift(){return zn(this,"shift")},some(e,t){return At(this,"some",e,t,void 0,arguments)},splice(...e){return zn(this,"splice",e)},toReversed(){return En(this).toReversed()},toSorted(e){return En(this).toSorted(e)},toSpliced(...e){return En(this).toSpliced(...e)},unshift(...e){return zn(this,"unshift",e)},values(){return Zo(this,"values",Ve)}};function Zo(e,t,n){const r=Po(e),o=r[t]();return r!==e&&!at(e)&&(o._next=o.next,o.next=()=>{const s=o._next();return s.value&&(s.value=n(s.value)),s}),o}const md=Array.prototype;function At(e,t,n,r,o,s){const i=Po(e),l=i!==e&&!at(e),a=i[t];if(a!==md[t]){const f=a.apply(e,s);return l?Ve(f):f}let c=n;i!==e&&(l?c=function(f,d){return n.call(this,Ve(f),d,e)}:n.length>2&&(c=function(f,d){return n.call(this,f,d,e)}));const u=a.call(i,c,r);return l&&o?o(u):u}function Ji(e,t,n,r){const o=Po(e);let s=n;return o!==e&&(at(e)?n.length>3&&(s=function(i,l,a){return n.call(this,i,l,a,e)}):s=function(i,l,a){return n.call(this,i,Ve(l),a,e)}),o[t](s,...r)}function es(e,t,n){const r=le(e);Ne(r,"iterate",hr);const o=r[t](...n);return(o===-1||o===!1)&&xi(n[0])?(n[0]=le(n[0]),r[t](...n)):o}function zn(e,t,n=[]){Zt(),gi();const r=le(e)[t].apply(e,n);return vi(),en(),r}const gd=ci("__proto__,__v_isRef,__isVue"),du=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Yt));function vd(e){Yt(e)||(e=String(e));const t=le(this);return Ne(t,"has",e),t.hasOwnProperty(e)}class hu{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return s;if(n==="__v_raw")return r===(o?s?Ad:vu:s?gu:mu).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=re(t);if(!o){let a;if(i&&(a=pd[n]))return a;if(n==="hasOwnProperty")return vd}const l=Reflect.get(t,n,Ae(t)?t:r);return(Yt(n)?du.has(n):gd(n))||(o||Ne(t,"get",n),s)?l:Ae(l)?i&&hi(n)?l:l.value:xe(l)?o?bu(l):yn(l):l}}class pu extends hu{constructor(t=!1){super(!1,t)}set(t,n,r,o){let s=t[n];if(!this._isShallow){const a=mn(s);if(!at(r)&&!mn(r)&&(s=le(s),r=le(r)),!re(t)&&Ae(s)&&!Ae(r))return a?!1:(s.value=r,!0)}const i=re(t)&&hi(n)?Number(n)<t.length:me(t,n),l=Reflect.set(t,n,r,Ae(t)?t:o);return t===le(o)&&(i?Qt(r,s)&&Pt(t,"set",n,r):Pt(t,"add",n,r)),l}deleteProperty(t,n){const r=me(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&r&&Pt(t,"delete",n,void 0),o}has(t,n){const r=Reflect.has(t,n);return(!Yt(n)||!du.has(n))&&Ne(t,"has",n),r}ownKeys(t){return Ne(t,"iterate",re(t)?"length":cn),Reflect.ownKeys(t)}}class yd extends hu{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const bd=new pu,_d=new yd,wd=new pu(!0);const Os=e=>e,Br=e=>Reflect.getPrototypeOf(e);function xd(e,t,n){return function(...r){const o=this.__v_raw,s=le(o),i=Dn(s),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,c=o[e](...r),u=n?Os:t?Fs:Ve;return!t&&Ne(s,"iterate",a?Ps:cn),{next(){const{value:f,done:d}=c.next();return d?{value:f,done:d}:{value:l?[u(f[0]),u(f[1])]:u(f),done:d}},[Symbol.iterator](){return this}}}}function Lr(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ed(e,t){const n={get(o){const s=this.__v_raw,i=le(s),l=le(o);e||(Qt(o,l)&&Ne(i,"get",o),Ne(i,"get",l));const{has:a}=Br(i),c=t?Os:e?Fs:Ve;if(a.call(i,o))return c(s.get(o));if(a.call(i,l))return c(s.get(l));s!==i&&s.get(o)},get size(){const o=this.__v_raw;return!e&&Ne(le(o),"iterate",cn),Reflect.get(o,"size",o)},has(o){const s=this.__v_raw,i=le(s),l=le(o);return e||(Qt(o,l)&&Ne(i,"has",o),Ne(i,"has",l)),o===l?s.has(o):s.has(o)||s.has(l)},forEach(o,s){const i=this,l=i.__v_raw,a=le(l),c=t?Os:e?Fs:Ve;return!e&&Ne(a,"iterate",cn),l.forEach((u,f)=>o.call(s,c(u),c(f),i))}};return Te(n,e?{add:Lr("add"),set:Lr("set"),delete:Lr("delete"),clear:Lr("clear")}:{add(o){!t&&!at(o)&&!mn(o)&&(o=le(o));const s=le(this);return Br(s).has.call(s,o)||(s.add(o),Pt(s,"add",o,o)),this},set(o,s){!t&&!at(s)&&!mn(s)&&(s=le(s));const i=le(this),{has:l,get:a}=Br(i);let c=l.call(i,o);c||(o=le(o),c=l.call(i,o));const u=a.call(i,o);return i.set(o,s),c?Qt(s,u)&&Pt(i,"set",o,s):Pt(i,"add",o,s),this},delete(o){const s=le(this),{has:i,get:l}=Br(s);let a=i.call(s,o);a||(o=le(o),a=i.call(s,o)),l&&l.call(s,o);const c=s.delete(o);return a&&Pt(s,"delete",o,void 0),c},clear(){const o=le(this),s=o.size!==0,i=o.clear();return s&&Pt(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=xd(o,e,t)}),n}function _i(e,t){const n=Ed(e,t);return(r,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(me(n,o)&&o in r?n:r,o,s)}const Cd={get:_i(!1,!1)},Sd={get:_i(!1,!0)},kd={get:_i(!0,!1)};const mu=new WeakMap,gu=new WeakMap,vu=new WeakMap,Ad=new WeakMap;function Td(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Rd(e){return e.__v_skip||!Object.isExtensible(e)?0:Td(Yf(e))}function yn(e){return mn(e)?e:wi(e,!1,bd,Cd,mu)}function yu(e){return wi(e,!1,wd,Sd,gu)}function bu(e){return wi(e,!0,_d,kd,vu)}function wi(e,t,n,r,o){if(!xe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const i=Rd(e);if(i===0)return e;const l=new Proxy(e,i===2?r:n);return o.set(e,l),l}function Gt(e){return mn(e)?Gt(e.__v_raw):!!(e&&e.__v_isReactive)}function mn(e){return!!(e&&e.__v_isReadonly)}function at(e){return!!(e&&e.__v_isShallow)}function xi(e){return e?!!e.__v_raw:!1}function le(e){const t=e&&e.__v_raw;return t?le(t):e}function bn(e){return!me(e,"__v_skip")&&Object.isExtensible(e)&&Xa(e,"__v_skip",!0),e}const Ve=e=>xe(e)?yn(e):e,Fs=e=>xe(e)?bu(e):e;function Ae(e){return e?e.__v_isRef===!0:!1}function de(e){return _u(e,!1)}function Pd(e){return _u(e,!0)}function _u(e,t){return Ae(e)?e:new Od(e,t)}class Od{constructor(t,n){this.dep=new bi,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:le(t),this._value=n?t:Ve(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||at(t)||mn(t);t=r?t:le(t),Qt(t,n)&&(this._rawValue=t,this._value=r?t:Ve(t),this.dep.trigger())}}function fn(e){return Ae(e)?e.value:e}const Fd={get:(e,t,n)=>t==="__v_raw"?e:fn(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Ae(o)&&!Ae(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function wu(e){return Gt(e)?e:new Proxy(e,Fd)}function Bd(e){const t=re(e)?new Array(e.length):{};for(const n in e)t[n]=Dd(e,n);return t}class Ld{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return hd(le(this._object),this._key)}}function Dd(e,t,n){const r=e[t];return Ae(r)?r:new Ld(e,t,n)}class Id{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new bi(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=dr-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&we!==this)return iu(this,!0),!0}get value(){const t=this.dep.track();return uu(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Md(e,t,n=!1){let r,o;return ie(e)?r=e:(r=e.get,o=e.set),new Id(r,o,n)}const Dr={},lo=new WeakMap;let sn;function $d(e,t=!1,n=sn){if(n){let r=lo.get(n);r||lo.set(n,r=[]),r.push(e)}}function qd(e,t,n=ye){const{immediate:r,deep:o,once:s,scheduler:i,augmentJob:l,call:a}=n,c=E=>o?E:at(E)||o===!1||o===0?Ot(E,1):Ot(E);let u,f,d,p,g=!1,x=!1;if(Ae(e)?(f=()=>e.value,g=at(e)):Gt(e)?(f=()=>c(e),g=!0):re(e)?(x=!0,g=e.some(E=>Gt(E)||at(E)),f=()=>e.map(E=>{if(Ae(E))return E.value;if(Gt(E))return c(E);if(ie(E))return a?a(E,2):E()})):ie(e)?t?f=a?()=>a(e,2):e:f=()=>{if(d){Zt();try{d()}finally{en()}}const E=sn;sn=u;try{return a?a(e,3,[p]):e(p)}finally{sn=E}}:f=mt,t&&o){const E=f,F=o===!0?1/0:o;f=()=>Ot(E(),F)}const w=ru(),O=()=>{u.stop(),w&&w.active&&di(w.effects,u)};if(s&&t){const E=t;t=(...F)=>{E(...F),O()}}let v=x?new Array(e.length).fill(Dr):Dr;const y=E=>{if(!(!(u.flags&1)||!u.dirty&&!E))if(t){const F=u.run();if(o||g||(x?F.some((I,D)=>Qt(I,v[D])):Qt(F,v))){d&&d();const I=sn;sn=u;try{const D=[F,v===Dr?void 0:x&&v[0]===Dr?[]:v,p];a?a(t,3,D):t(...D),v=F}finally{sn=I}}}else u.run()};return l&&l(y),u=new ou(f),u.scheduler=i?()=>i(y,!1):y,p=E=>$d(E,!1,u),d=u.onStop=()=>{const E=lo.get(u);if(E){if(a)a(E,4);else for(const F of E)F();lo.delete(u)}},t?r?y(!0):v=u.run():i?i(y.bind(null,!0),!0):u.run(),O.pause=u.pause.bind(u),O.resume=u.resume.bind(u),O.stop=O,O}function Ot(e,t=1/0,n){if(t<=0||!xe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Ae(e))Ot(e.value,t,n);else if(re(e))for(let r=0;r<e.length;r++)Ot(e[r],t,n);else if(Ka(e)||Dn(e))e.forEach(r=>{Ot(r,t,n)});else if(Ja(e)){for(const r in e)Ot(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ot(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Tr(e,t,n,r){try{return r?e(...r):e()}catch(o){Oo(o,t,n)}}function bt(e,t,n,r){if(ie(e)){const o=Tr(e,t,n,r);return o&&Qa(o)&&o.catch(s=>{Oo(s,t,n)}),o}if(re(e)){const o=[];for(let s=0;s<e.length;s++)o.push(bt(e[s],t,n,r));return o}}function Oo(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ye;if(t){let l=t.parent;const a=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,a,c)===!1)return}l=l.parent}if(s){Zt(),Tr(s,null,10,[e,a,c]),en();return}}Nd(e,n,o,r,i)}function Nd(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}const He=[];let Ct=-1;const In=[];let Vt=null,Tn=0;const xu=Promise.resolve();let ao=null;function Qe(e){const t=ao||xu;return e?t.then(this?e.bind(this):e):t}function Vd(e){let t=Ct+1,n=He.length;for(;t<n;){const r=t+n>>>1,o=He[r],s=pr(o);s<e||s===e&&o.flags&2?t=r+1:n=r}return t}function Ei(e){if(!(e.flags&1)){const t=pr(e),n=He[He.length-1];!n||!(e.flags&2)&&t>=pr(n)?He.push(e):He.splice(Vd(t),0,e),e.flags|=1,Eu()}}function Eu(){ao||(ao=xu.then(Su))}function jd(e){re(e)?In.push(...e):Vt&&e.id===-1?Vt.splice(Tn+1,0,e):e.flags&1||(In.push(e),e.flags|=1),Eu()}function Xi(e,t,n=Ct+1){for(;n<He.length;n++){const r=He[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;He.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function Cu(e){if(In.length){const t=[...new Set(In)].sort((n,r)=>pr(n)-pr(r));if(In.length=0,Vt){Vt.push(...t);return}for(Vt=t,Tn=0;Tn<Vt.length;Tn++){const n=Vt[Tn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Vt=null,Tn=0}}const pr=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Su(e){const t=mt;try{for(Ct=0;Ct<He.length;Ct++){const n=He[Ct];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),Tr(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Ct<He.length;Ct++){const n=He[Ct];n&&(n.flags&=-2)}Ct=-1,He.length=0,Cu(),ao=null,(He.length||In.length)&&Su()}}let We=null,ku=null;function uo(e){const t=We;return We=e,ku=e&&e.type.__scopeId||null,t}function Ud(e,t=We,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&ul(-1);const s=uo(t);let i;try{i=e(...o)}finally{uo(s),r._d&&ul(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function Au(e,t){if(We===null)return e;const n=qo(We),r=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[s,i,l,a=ye]=t[o];s&&(ie(s)&&(s={mounted:s,updated:s}),s.deep&&Ot(i),r.push({dir:s,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function tn(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];s&&(l.oldValue=s[i].value);let a=l.dir[r];a&&(Zt(),bt(a,n,8,[e.el,l,e,t]),en())}}const Tu=Symbol("_vte"),Ru=e=>e.__isTeleport,ir=e=>e&&(e.disabled||e.disabled===""),Yi=e=>e&&(e.defer||e.defer===""),Zi=e=>typeof SVGElement!="undefined"&&e instanceof SVGElement,el=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Bs=(e,t)=>{const n=e&&e.to;return Ce(n)?t?t(n):null:n},Pu={name:"Teleport",__isTeleport:!0,process(e,t,n,r,o,s,i,l,a,c){const{mc:u,pc:f,pbc:d,o:{insert:p,querySelector:g,createText:x,createComment:w}}=c,O=ir(t.props);let{shapeFlag:v,children:y,dynamicChildren:E}=t;if(e==null){const F=t.el=x(""),I=t.anchor=x("");p(F,n,r),p(I,n,r);const D=(_,C)=>{v&16&&(o&&o.isCE&&(o.ce._teleportTarget=_),u(y,_,C,o,s,i,l,a))},M=()=>{const _=t.target=Bs(t.props,g),C=Ou(_,t,x,p);_&&(i!=="svg"&&Zi(_)?i="svg":i!=="mathml"&&el(_)&&(i="mathml"),O||(D(_,C),Wr(t,!1)))};O&&(D(n,I),Wr(t,!0)),Yi(t.props)?Fe(()=>{M(),t.el.__isMounted=!0},s):M()}else{if(Yi(t.props)&&!e.el.__isMounted){Fe(()=>{Pu.process(e,t,n,r,o,s,i,l,a,c),delete e.el.__isMounted},s);return}t.el=e.el,t.targetStart=e.targetStart;const F=t.anchor=e.anchor,I=t.target=e.target,D=t.targetAnchor=e.targetAnchor,M=ir(e.props),_=M?n:I,C=M?F:D;if(i==="svg"||Zi(I)?i="svg":(i==="mathml"||el(I))&&(i="mathml"),E?(d(e.dynamicChildren,E,_,o,s,i,l),Ti(e,t,!0)):a||f(e,t,_,C,o,s,i,l,!1),O)M?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Ir(t,n,F,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const L=t.target=Bs(t.props,g);L&&Ir(t,L,null,c,0)}else M&&Ir(t,I,D,c,1);Wr(t,O)}},remove(e,t,n,{um:r,o:{remove:o}},s){const{shapeFlag:i,children:l,anchor:a,targetStart:c,targetAnchor:u,target:f,props:d}=e;if(f&&(o(c),o(u)),s&&o(a),i&16){const p=s||!ir(d);for(let g=0;g<l.length;g++){const x=l[g];r(x,t,n,p,!!x.dynamicChildren)}}},move:Ir,hydrate:Hd};function Ir(e,t,n,{o:{insert:r},m:o},s=2){s===0&&r(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:a,children:c,props:u}=e,f=s===2;if(f&&r(i,t,n),(!f||ir(u))&&a&16)for(let d=0;d<c.length;d++)o(c[d],t,n,2);f&&r(l,t,n)}function Hd(e,t,n,r,o,s,{o:{nextSibling:i,parentNode:l,querySelector:a,insert:c,createText:u}},f){const d=t.target=Bs(t.props,a);if(d){const p=ir(t.props),g=d._lpa||d.firstChild;if(t.shapeFlag&16)if(p)t.anchor=f(i(e),t,l(e),n,r,o,s),t.targetStart=g,t.targetAnchor=g&&i(g);else{t.anchor=i(e);let x=g;for(;x;){if(x&&x.nodeType===8){if(x.data==="teleport start anchor")t.targetStart=x;else if(x.data==="teleport anchor"){t.targetAnchor=x,d._lpa=t.targetAnchor&&i(t.targetAnchor);break}}x=i(x)}t.targetAnchor||Ou(d,t,u,c),f(g&&i(g),t,d,n,r,o,s)}Wr(t,p)}return t.anchor&&i(t.anchor)}const zd=Pu;function Wr(e,t){const n=e.ctx;if(n&&n.ut){let r,o;for(t?(r=e.el,o=e.anchor):(r=e.targetStart,o=e.targetAnchor);r&&r!==o;)r.nodeType===1&&r.setAttribute("data-v-owner",n.uid),r=r.nextSibling;n.ut()}}function Ou(e,t,n,r){const o=t.targetStart=n(""),s=t.targetAnchor=n("");return o[Tu]=s,e&&(r(o,e),r(s,e)),s}const jt=Symbol("_leaveCb"),Mr=Symbol("_enterCb");function Fu(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Dt(()=>{e.isMounted=!0}),ct(()=>{e.isUnmounting=!0}),e}const rt=[Function,Array],Bu={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:rt,onEnter:rt,onAfterEnter:rt,onEnterCancelled:rt,onBeforeLeave:rt,onLeave:rt,onAfterLeave:rt,onLeaveCancelled:rt,onBeforeAppear:rt,onAppear:rt,onAfterAppear:rt,onAppearCancelled:rt},Lu=e=>{const t=e.subTree;return t.component?Lu(t.component):t},Wd={name:"BaseTransition",props:Bu,setup(e,{slots:t}){const n=Se(),r=Fu();return()=>{const o=t.default&&Ci(t.default(),!0);if(!o||!o.length)return;const s=Du(o),i=le(e),{mode:l}=i;if(r.isLeaving)return ts(s);const a=tl(s);if(!a)return ts(s);let c=mr(a,i,r,n,f=>c=f);a.type!==ze&&Jt(a,c);let u=n.subTree&&tl(n.subTree);if(u&&u.type!==ze&&!Wt(a,u)&&Lu(n).type!==ze){let f=mr(u,i,r,n);if(Jt(u,f),l==="out-in"&&a.type!==ze)return r.isLeaving=!0,f.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,u=void 0},ts(s);l==="in-out"&&a.type!==ze?f.delayLeave=(d,p,g)=>{const x=Iu(r,u);x[String(u.key)]=u,d[jt]=()=>{p(),d[jt]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{g(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return s}}};function Du(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==ze){t=n;break}}return t}const Kd=Wd;function Iu(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function mr(e,t,n,r,o){const{appear:s,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:c,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:d,onLeave:p,onAfterLeave:g,onLeaveCancelled:x,onBeforeAppear:w,onAppear:O,onAfterAppear:v,onAppearCancelled:y}=t,E=String(e.key),F=Iu(n,e),I=(_,C)=>{_&&bt(_,r,9,C)},D=(_,C)=>{const L=C[1];I(_,C),re(_)?_.every(b=>b.length<=1)&&L():_.length<=1&&L()},M={mode:i,persisted:l,beforeEnter(_){let C=a;if(!n.isMounted)if(s)C=w||a;else return;_[jt]&&_[jt](!0);const L=F[E];L&&Wt(e,L)&&L.el[jt]&&L.el[jt](),I(C,[_])},enter(_){let C=c,L=u,b=f;if(!n.isMounted)if(s)C=O||c,L=v||u,b=y||f;else return;let U=!1;const T=_[Mr]=Y=>{U||(U=!0,Y?I(b,[_]):I(L,[_]),M.delayedLeave&&M.delayedLeave(),_[Mr]=void 0)};C?D(C,[_,T]):T()},leave(_,C){const L=String(e.key);if(_[Mr]&&_[Mr](!0),n.isUnmounting)return C();I(d,[_]);let b=!1;const U=_[jt]=T=>{b||(b=!0,C(),T?I(x,[_]):I(g,[_]),_[jt]=void 0,F[L]===e&&delete F[L])};F[L]=e,p?D(p,[_,U]):U()},clone(_){const C=mr(_,t,n,r,o);return o&&o(C),C}};return M}function ts(e){if(Bo(e))return e=Ft(e),e.children=null,e}function tl(e){if(!Bo(e))return Ru(e.type)&&e.children?Du(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&ie(n.default))return n.default()}}function Jt(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Jt(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ci(e,t=!1,n){let r=[],o=0;for(let s=0;s<e.length;s++){let i=e[s];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:s);i.type===ht?(i.patchFlag&128&&o++,r=r.concat(Ci(i.children,t,l))):(t||i.type!==ze)&&r.push(l!=null?Ft(i,{key:l}):i)}if(o>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function Fo(e,t){return ie(e)?(()=>Te({name:e.name},t,{setup:e}))():e}function Mu(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function co(e,t,n,r,o=!1){if(re(e)){e.forEach((g,x)=>co(g,t&&(re(t)?t[x]:t),n,r,o));return}if(Mn(r)&&!o){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&co(e,t,n,r.component.subTree);return}const s=r.shapeFlag&4?qo(r.component):r.el,i=o?null:s,{i:l,r:a}=e,c=t&&t.r,u=l.refs===ye?l.refs={}:l.refs,f=l.setupState,d=le(f),p=f===ye?()=>!1:g=>me(d,g);if(c!=null&&c!==a&&(Ce(c)?(u[c]=null,p(c)&&(f[c]=null)):Ae(c)&&(c.value=null)),ie(a))Tr(a,l,12,[i,u]);else{const g=Ce(a),x=Ae(a);if(g||x){const w=()=>{if(e.f){const O=g?p(a)?f[a]:u[a]:a.value;o?re(O)&&di(O,s):re(O)?O.includes(s)||O.push(s):g?(u[a]=[s],p(a)&&(f[a]=u[a])):(a.value=[s],e.k&&(u[e.k]=a.value))}else g?(u[a]=i,p(a)&&(f[a]=i)):x&&(a.value=i,e.k&&(u[e.k]=i))};i?(w.id=-1,Fe(w,n)):w()}}}Ro().requestIdleCallback;Ro().cancelIdleCallback;const Mn=e=>!!e.type.__asyncLoader,Bo=e=>e.type.__isKeepAlive,Qd={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=Se(),r=n.ctx;if(!r.renderer)return()=>{const v=t.default&&t.default();return v&&v.length===1?v[0]:v};const o=new Map,s=new Set;let i=null;const l=n.suspense,{renderer:{p:a,m:c,um:u,o:{createElement:f}}}=r,d=f("div");r.activate=(v,y,E,F,I)=>{const D=v.component;c(v,y,E,0,l),a(D.vnode,v,y,E,D,l,F,v.slotScopeIds,I),Fe(()=>{D.isDeactivated=!1,D.a&&rr(D.a);const M=v.props&&v.props.onVnodeMounted;M&&st(M,D.parent,v)},l)},r.deactivate=v=>{const y=v.component;ho(y.m),ho(y.a),c(v,d,null,1,l),Fe(()=>{y.da&&rr(y.da);const E=v.props&&v.props.onVnodeUnmounted;E&&st(E,y.parent,v),y.isDeactivated=!0},l)};function p(v){ns(v),u(v,n,l,!0)}function g(v){o.forEach((y,E)=>{const F=qs(y.type);F&&!v(F)&&x(E)})}function x(v){const y=o.get(v);y&&(!i||!Wt(y,i))?p(y):i&&ns(i),o.delete(v),s.delete(v)}be(()=>[e.include,e.exclude],([v,y])=>{v&&g(E=>Zn(v,E)),y&&g(E=>!Zn(y,E))},{flush:"post",deep:!0});let w=null;const O=()=>{w!=null&&(po(n.subTree.type)?Fe(()=>{o.set(w,$r(n.subTree))},n.subTree.suspense):o.set(w,$r(n.subTree)))};return Dt(O),Si(O),ct(()=>{o.forEach(v=>{const{subTree:y,suspense:E}=n,F=$r(y);if(v.type===F.type&&v.key===F.key){ns(F);const I=F.component.da;I&&Fe(I,E);return}p(v)})}),()=>{if(w=null,!t.default)return i=null;const v=t.default(),y=v[0];if(v.length>1)return i=null,v;if(!vr(y)||!(y.shapeFlag&4)&&!(y.shapeFlag&128))return i=null,y;let E=$r(y);if(E.type===ze)return i=null,E;const F=E.type,I=qs(Mn(E)?E.type.__asyncResolved||{}:F),{include:D,exclude:M,max:_}=e;if(D&&(!I||!Zn(D,I))||M&&I&&Zn(M,I))return E.shapeFlag&=-257,i=E,y;const C=E.key==null?F:E.key,L=o.get(C);return E.el&&(E=Ft(E),y.shapeFlag&128&&(y.ssContent=E)),w=C,L?(E.el=L.el,E.component=L.component,E.transition&&Jt(E,E.transition),E.shapeFlag|=512,s.delete(C),s.add(C)):(s.add(C),_&&s.size>parseInt(_,10)&&x(s.values().next().value)),E.shapeFlag|=256,i=E,po(y.type)?y:E}}},a0=Qd;function Zn(e,t){return re(e)?e.some(n=>Zn(n,t)):Ce(e)?e.split(",").includes(t):Xf(e)?(e.lastIndex=0,e.test(t)):!1}function $u(e,t){qu(e,"a",t)}function Lo(e,t){qu(e,"da",t)}function qu(e,t,n=De){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Do(t,r,n),n){let o=n.parent;for(;o&&o.parent;)Bo(o.parent.vnode)&&Gd(r,t,n,o),o=o.parent}}function Gd(e,t,n,r){const o=Do(t,e,r,!0);Io(()=>{di(r[t],o)},n)}function ns(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function $r(e){return e.shapeFlag&128?e.ssContent:e}function Do(e,t,n=De,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{Zt();const l=Rr(n),a=bt(t,n,e,i);return l(),en(),a});return r?o.unshift(s):o.push(s),s}}const Lt=e=>(t,n=De)=>{(!yr||e==="sp")&&Do(e,(...r)=>t(...r),n)},Jd=Lt("bm"),Dt=Lt("m"),Nu=Lt("bu"),Si=Lt("u"),ct=Lt("bum"),Io=Lt("um"),Xd=Lt("sp"),Yd=Lt("rtg"),Zd=Lt("rtc");function eh(e,t=De){Do("ec",e,t)}const Vu="components";function th(e,t){return rh(Vu,e,!0,t)||e}const nh=Symbol.for("v-ndc");function rh(e,t,n=!0,r=!1){const o=We||De;if(o){const s=o.type;if(e===Vu){const l=qs(s,!1);if(l&&(l===t||l===ut(t)||l===To(ut(t))))return s}const i=nl(o[e]||s[e],t)||nl(o.appContext[e],t);return!i&&r?s:i}}function nl(e,t){return e&&(e[t]||e[ut(t)]||e[To(ut(t))])}function u0(e,t,n,r){let o;const s=n&&n[r],i=re(e);if(i||Ce(e)){const l=i&&Gt(e);let a=!1;l&&(a=!at(e),e=Po(e)),o=new Array(e.length);for(let c=0,u=e.length;c<u;c++)o[c]=t(a?Ve(e[c]):e[c],c,void 0,s&&s[c])}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,s&&s[l])}else if(xe(e))if(e[Symbol.iterator])o=Array.from(e,(l,a)=>t(l,a,void 0,s&&s[a]));else{const l=Object.keys(e);o=new Array(l.length);for(let a=0,c=l.length;a<c;a++){const u=l[a];o[a]=t(e[u],u,a,s&&s[a])}}else o=[];return n&&(n[r]=o),o}function c0(e,t){for(let n=0;n<t.length;n++){const r=t[n];if(re(r))for(let o=0;o<r.length;o++)e[r[o].name]=r[o].fn;else r&&(e[r.name]=r.key?(...o)=>{const s=r.fn(...o);return s&&(s.key=r.key),s}:r.fn)}return e}const Ls=e=>e?ac(e)?qo(e):Ls(e.parent):null,lr=Te(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ls(e.parent),$root:e=>Ls(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ki(e),$forceUpdate:e=>e.f||(e.f=()=>{Ei(e.update)}),$nextTick:e=>e.n||(e.n=Qe.bind(e.proxy)),$watch:e=>Sh.bind(e)}),rs=(e,t)=>e!==ye&&!e.__isScriptSetup&&me(e,t),oh={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:s,accessCache:i,type:l,appContext:a}=e;let c;if(t[0]!=="$"){const p=i[t];if(p!==void 0)switch(p){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(rs(r,t))return i[t]=1,r[t];if(o!==ye&&me(o,t))return i[t]=2,o[t];if((c=e.propsOptions[0])&&me(c,t))return i[t]=3,s[t];if(n!==ye&&me(n,t))return i[t]=4,n[t];Ds&&(i[t]=0)}}const u=lr[t];let f,d;if(u)return t==="$attrs"&&Ne(e.attrs,"get",""),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==ye&&me(n,t))return i[t]=4,n[t];if(d=a.config.globalProperties,me(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:s}=e;return rs(o,t)?(o[t]=n,!0):r!==ye&&me(r,t)?(r[t]=n,!0):me(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:s}},i){let l;return!!n[i]||e!==ye&&me(e,i)||rs(t,i)||(l=s[0])&&me(l,i)||me(r,i)||me(lr,i)||me(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:me(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function rl(e){return re(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ds=!0;function sh(e){const t=ki(e),n=e.proxy,r=e.ctx;Ds=!1,t.beforeCreate&&ol(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:i,watch:l,provide:a,inject:c,created:u,beforeMount:f,mounted:d,beforeUpdate:p,updated:g,activated:x,deactivated:w,beforeDestroy:O,beforeUnmount:v,destroyed:y,unmounted:E,render:F,renderTracked:I,renderTriggered:D,errorCaptured:M,serverPrefetch:_,expose:C,inheritAttrs:L,components:b,directives:U,filters:T}=t;if(c&&ih(c,r,null),i)for(const H in i){const N=i[H];ie(N)&&(r[H]=N.bind(n))}if(o){const H=o.call(n,n);xe(H)&&(e.data=yn(H))}if(Ds=!0,s)for(const H in s){const N=s[H],ne=ie(N)?N.bind(n,n):ie(N.get)?N.get.bind(n,n):mt,ue=!ie(N)&&ie(N.set)?N.set.bind(n):mt,ce=R({get:ne,set:ue});Object.defineProperty(r,H,{enumerable:!0,configurable:!0,get:()=>ce.value,set:q=>ce.value=q})}if(l)for(const H in l)ju(l[H],r,n,H);if(a){const H=ie(a)?a.call(n):a;Reflect.ownKeys(H).forEach(N=>{Kr(N,H[N])})}u&&ol(u,e,"c");function J(H,N){re(N)?N.forEach(ne=>H(ne.bind(n))):N&&H(N.bind(n))}if(J(Jd,f),J(Dt,d),J(Nu,p),J(Si,g),J($u,x),J(Lo,w),J(eh,M),J(Zd,I),J(Yd,D),J(ct,v),J(Io,E),J(Xd,_),re(C))if(C.length){const H=e.exposed||(e.exposed={});C.forEach(N=>{Object.defineProperty(H,N,{get:()=>n[N],set:ne=>n[N]=ne})})}else e.exposed||(e.exposed={});F&&e.render===mt&&(e.render=F),L!=null&&(e.inheritAttrs=L),b&&(e.components=b),U&&(e.directives=U),_&&Mu(e)}function ih(e,t,n=mt){re(e)&&(e=Is(e));for(const r in e){const o=e[r];let s;xe(o)?"default"in o?s=et(o.from||r,o.default,!0):s=et(o.from||r):s=et(o),Ae(s)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[r]=s}}function ol(e,t,n){bt(re(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function ju(e,t,n,r){let o=r.includes(".")?tc(n,r):()=>n[r];if(Ce(e)){const s=t[e];ie(s)&&be(o,s)}else if(ie(e))be(o,e.bind(n));else if(xe(e))if(re(e))e.forEach(s=>ju(s,t,n,r));else{const s=ie(e.handler)?e.handler.bind(n):t[e.handler];ie(s)&&be(o,s,e)}}function ki(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,l=s.get(t);let a;return l?a=l:!o.length&&!n&&!r?a=t:(a={},o.length&&o.forEach(c=>fo(a,c,i,!0)),fo(a,t,i)),xe(t)&&s.set(t,a),a}function fo(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&fo(e,s,n,!0),o&&o.forEach(i=>fo(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=lh[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const lh={data:sl,props:il,emits:il,methods:er,computed:er,beforeCreate:je,created:je,beforeMount:je,mounted:je,beforeUpdate:je,updated:je,beforeDestroy:je,beforeUnmount:je,destroyed:je,unmounted:je,activated:je,deactivated:je,errorCaptured:je,serverPrefetch:je,components:er,directives:er,watch:uh,provide:sl,inject:ah};function sl(e,t){return t?e?function(){return Te(ie(e)?e.call(this,this):e,ie(t)?t.call(this,this):t)}:t:e}function ah(e,t){return er(Is(e),Is(t))}function Is(e){if(re(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function je(e,t){return e?[...new Set([].concat(e,t))]:t}function er(e,t){return e?Te(Object.create(null),e,t):t}function il(e,t){return e?re(e)&&re(t)?[...new Set([...e,...t])]:Te(Object.create(null),rl(e),rl(t!=null?t:{})):t}function uh(e,t){if(!e)return t;if(!t)return e;const n=Te(Object.create(null),e);for(const r in t)n[r]=je(e[r],t[r]);return n}function Uu(){return{app:null,config:{isNativeTag:Gf,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ch=0;function fh(e,t){return function(r,o=null){ie(r)||(r=Te({},r)),o!=null&&!xe(o)&&(o=null);const s=Uu(),i=new WeakSet,l=[];let a=!1;const c=s.app={_uid:ch++,_component:r,_props:o,_container:null,_context:s,_instance:null,version:Wh,get config(){return s.config},set config(u){},use(u,...f){return i.has(u)||(u&&ie(u.install)?(i.add(u),u.install(c,...f)):ie(u)&&(i.add(u),u(c,...f))),c},mixin(u){return s.mixins.includes(u)||s.mixins.push(u),c},component(u,f){return f?(s.components[u]=f,c):s.components[u]},directive(u,f){return f?(s.directives[u]=f,c):s.directives[u]},mount(u,f,d){if(!a){const p=c._ceVNode||Ge(r,o);return p.appContext=s,d===!0?d="svg":d===!1&&(d=void 0),f&&t?t(p,u):e(p,u,d),a=!0,c._container=u,u.__vue_app__=c,qo(p.component)}},onUnmount(u){l.push(u)},unmount(){a&&(bt(l,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,f){return s.provides[u]=f,c},runWithContext(u){const f=dn;dn=c;try{return u()}finally{dn=f}}};return c}}let dn=null;function Kr(e,t){if(De){let n=De.provides;const r=De.parent&&De.parent.provides;r===n&&(n=De.provides=Object.create(r)),n[e]=t}}function et(e,t,n=!1){const r=De||We;if(r||dn){const o=dn?dn._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&ie(t)?t.call(r&&r.proxy):t}}function dh(){return!!(De||We||dn)}const Hu={},zu=()=>Object.create(Hu),Wu=e=>Object.getPrototypeOf(e)===Hu;function hh(e,t,n,r=!1){const o={},s=zu();e.propsDefaults=Object.create(null),Ku(e,t,o,s);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=r?o:yu(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function ph(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:i}}=e,l=le(o),[a]=e.propsOptions;let c=!1;if((r||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let d=u[f];if(Mo(e.emitsOptions,d))continue;const p=t[d];if(a)if(me(s,d))p!==s[d]&&(s[d]=p,c=!0);else{const g=ut(d);o[g]=Ms(a,l,g,p,e,!1)}else p!==s[d]&&(s[d]=p,c=!0)}}}else{Ku(e,t,o,s)&&(c=!0);let u;for(const f in l)(!t||!me(t,f)&&((u=vn(f))===f||!me(t,u)))&&(a?n&&(n[f]!==void 0||n[u]!==void 0)&&(o[f]=Ms(a,l,f,void 0,e,!0)):delete o[f]);if(s!==l)for(const f in s)(!t||!me(t,f)&&!0)&&(delete s[f],c=!0)}c&&Pt(e.attrs,"set","")}function Ku(e,t,n,r){const[o,s]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(nr(a))continue;const c=t[a];let u;o&&me(o,u=ut(a))?!s||!s.includes(u)?n[u]=c:(l||(l={}))[u]=c:Mo(e.emitsOptions,a)||(!(a in r)||c!==r[a])&&(r[a]=c,i=!0)}if(s){const a=le(n),c=l||ye;for(let u=0;u<s.length;u++){const f=s[u];n[f]=Ms(o,a,f,c[f],e,!me(c,f))}}return i}function Ms(e,t,n,r,o,s){const i=e[n];if(i!=null){const l=me(i,"default");if(l&&r===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&ie(a)){const{propsDefaults:c}=o;if(n in c)r=c[n];else{const u=Rr(o);r=c[n]=a.call(null,t),u()}}else r=a;o.ce&&o.ce._setProp(n,r)}i[0]&&(s&&!l?r=!1:i[1]&&(r===""||r===vn(n))&&(r=!0))}return r}const mh=new WeakMap;function Qu(e,t,n=!1){const r=n?mh:t.propsCache,o=r.get(e);if(o)return o;const s=e.props,i={},l=[];let a=!1;if(!ie(e)){const u=f=>{a=!0;const[d,p]=Qu(f,t,!0);Te(i,d),p&&l.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!s&&!a)return xe(e)&&r.set(e,Ln),Ln;if(re(s))for(let u=0;u<s.length;u++){const f=ut(s[u]);ll(f)&&(i[f]=ye)}else if(s)for(const u in s){const f=ut(u);if(ll(f)){const d=s[u],p=i[f]=re(d)||ie(d)?{type:d}:Te({},d),g=p.type;let x=!1,w=!0;if(re(g))for(let O=0;O<g.length;++O){const v=g[O],y=ie(v)&&v.name;if(y==="Boolean"){x=!0;break}else y==="String"&&(w=!1)}else x=ie(g)&&g.name==="Boolean";p[0]=x,p[1]=w,(x||me(p,"default"))&&l.push(f)}}const c=[i,l];return xe(e)&&r.set(e,c),c}function ll(e){return e[0]!=="$"&&!nr(e)}const Gu=e=>e[0]==="_"||e==="$stable",Ai=e=>re(e)?e.map(St):[St(e)],gh=(e,t,n)=>{if(t._n)return t;const r=Ud((...o)=>Ai(t(...o)),n);return r._c=!1,r},Ju=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Gu(o))continue;const s=e[o];if(ie(s))t[o]=gh(o,s,r);else if(s!=null){const i=Ai(s);t[o]=()=>i}}},Xu=(e,t)=>{const n=Ai(t);e.slots.default=()=>n},Yu=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},vh=(e,t,n)=>{const r=e.slots=zu();if(e.vnode.shapeFlag&32){const o=t._;o?(Yu(r,t,n),n&&Xa(r,"_",o,!0)):Ju(t,r)}else t&&Xu(e,t)},yh=(e,t,n)=>{const{vnode:r,slots:o}=e;let s=!0,i=ye;if(r.shapeFlag&32){const l=t._;l?n&&l===1?s=!1:Yu(o,t,n):(s=!t.$stable,Ju(t,o)),i=t}else t&&(Xu(e,t),i={default:1});if(s)for(const l in o)!Gu(l)&&i[l]==null&&delete o[l]},Fe=Fh;function bh(e){return _h(e)}function _h(e,t){const n=Ro();n.__VUE__=!0;const{insert:r,remove:o,patchProp:s,createElement:i,createText:l,createComment:a,setText:c,setElementText:u,parentNode:f,nextSibling:d,setScopeId:p=mt,insertStaticContent:g}=e,x=(h,m,S,$=null,B=null,V=null,G=void 0,W=null,z=!!m.dynamicChildren)=>{if(h===m)return;h&&!Wt(h,m)&&($=k(h),q(h,B,V,!0),h=null),m.patchFlag===-2&&(z=!1,m.dynamicChildren=null);const{type:j,ref:oe,shapeFlag:X}=m;switch(j){case $o:w(h,m,S,$);break;case ze:O(h,m,S,$);break;case is:h==null&&v(m,S,$,G);break;case ht:b(h,m,S,$,B,V,G,W,z);break;default:X&1?F(h,m,S,$,B,V,G,W,z):X&6?U(h,m,S,$,B,V,G,W,z):(X&64||X&128)&&j.process(h,m,S,$,B,V,G,W,z,Z)}oe!=null&&B&&co(oe,h&&h.ref,V,m||h,!m)},w=(h,m,S,$)=>{if(h==null)r(m.el=l(m.children),S,$);else{const B=m.el=h.el;m.children!==h.children&&c(B,m.children)}},O=(h,m,S,$)=>{h==null?r(m.el=a(m.children||""),S,$):m.el=h.el},v=(h,m,S,$)=>{[h.el,h.anchor]=g(h.children,m,S,$,h.el,h.anchor)},y=({el:h,anchor:m},S,$)=>{let B;for(;h&&h!==m;)B=d(h),r(h,S,$),h=B;r(m,S,$)},E=({el:h,anchor:m})=>{let S;for(;h&&h!==m;)S=d(h),o(h),h=S;o(m)},F=(h,m,S,$,B,V,G,W,z)=>{m.type==="svg"?G="svg":m.type==="math"&&(G="mathml"),h==null?I(m,S,$,B,V,G,W,z):_(h,m,B,V,G,W,z)},I=(h,m,S,$,B,V,G,W)=>{let z,j;const{props:oe,shapeFlag:X,transition:te,dirs:se}=h;if(z=h.el=i(h.type,V,oe&&oe.is,oe),X&8?u(z,h.children):X&16&&M(h.children,z,null,$,B,os(h,V),G,W),se&&tn(h,null,$,"created"),D(z,h,h.scopeId,G,$),oe){for(const _e in oe)_e!=="value"&&!nr(_e)&&s(z,_e,null,oe[_e],V,$);"value"in oe&&s(z,"value",null,oe.value,V),(j=oe.onVnodeBeforeMount)&&st(j,$,h)}se&&tn(h,null,$,"beforeMount");const fe=wh(B,te);fe&&te.beforeEnter(z),r(z,m,S),((j=oe&&oe.onVnodeMounted)||fe||se)&&Fe(()=>{j&&st(j,$,h),fe&&te.enter(z),se&&tn(h,null,$,"mounted")},B)},D=(h,m,S,$,B)=>{if(S&&p(h,S),$)for(let V=0;V<$.length;V++)p(h,$[V]);if(B){let V=B.subTree;if(m===V||po(V.type)&&(V.ssContent===m||V.ssFallback===m)){const G=B.vnode;D(h,G,G.scopeId,G.slotScopeIds,B.parent)}}},M=(h,m,S,$,B,V,G,W,z=0)=>{for(let j=z;j<h.length;j++){const oe=h[j]=W?Ut(h[j]):St(h[j]);x(null,oe,m,S,$,B,V,G,W)}},_=(h,m,S,$,B,V,G)=>{const W=m.el=h.el;let{patchFlag:z,dynamicChildren:j,dirs:oe}=m;z|=h.patchFlag&16;const X=h.props||ye,te=m.props||ye;let se;if(S&&nn(S,!1),(se=te.onVnodeBeforeUpdate)&&st(se,S,m,h),oe&&tn(m,h,S,"beforeUpdate"),S&&nn(S,!0),(X.innerHTML&&te.innerHTML==null||X.textContent&&te.textContent==null)&&u(W,""),j?C(h.dynamicChildren,j,W,S,$,os(m,B),V):G||N(h,m,W,null,S,$,os(m,B),V,!1),z>0){if(z&16)L(W,X,te,S,B);else if(z&2&&X.class!==te.class&&s(W,"class",null,te.class,B),z&4&&s(W,"style",X.style,te.style,B),z&8){const fe=m.dynamicProps;for(let _e=0;_e<fe.length;_e++){const ge=fe[_e],Je=X[ge],$e=te[ge];($e!==Je||ge==="value")&&s(W,ge,Je,$e,B,S)}}z&1&&h.children!==m.children&&u(W,m.children)}else!G&&j==null&&L(W,X,te,S,B);((se=te.onVnodeUpdated)||oe)&&Fe(()=>{se&&st(se,S,m,h),oe&&tn(m,h,S,"updated")},$)},C=(h,m,S,$,B,V,G)=>{for(let W=0;W<m.length;W++){const z=h[W],j=m[W],oe=z.el&&(z.type===ht||!Wt(z,j)||z.shapeFlag&70)?f(z.el):S;x(z,j,oe,null,$,B,V,G,!0)}},L=(h,m,S,$,B)=>{if(m!==S){if(m!==ye)for(const V in m)!nr(V)&&!(V in S)&&s(h,V,m[V],null,B,$);for(const V in S){if(nr(V))continue;const G=S[V],W=m[V];G!==W&&V!=="value"&&s(h,V,W,G,B,$)}"value"in S&&s(h,"value",m.value,S.value,B)}},b=(h,m,S,$,B,V,G,W,z)=>{const j=m.el=h?h.el:l(""),oe=m.anchor=h?h.anchor:l("");let{patchFlag:X,dynamicChildren:te,slotScopeIds:se}=m;se&&(W=W?W.concat(se):se),h==null?(r(j,S,$),r(oe,S,$),M(m.children||[],S,oe,B,V,G,W,z)):X>0&&X&64&&te&&h.dynamicChildren?(C(h.dynamicChildren,te,S,B,V,G,W),(m.key!=null||B&&m===B.subTree)&&Ti(h,m,!0)):N(h,m,S,oe,B,V,G,W,z)},U=(h,m,S,$,B,V,G,W,z)=>{m.slotScopeIds=W,h==null?m.shapeFlag&512?B.ctx.activate(m,S,$,G,z):T(m,S,$,B,V,G,z):Y(h,m,z)},T=(h,m,S,$,B,V,G)=>{const W=h.component=Nh(h,$,B);if(Bo(h)&&(W.ctx.renderer=Z),Vh(W,!1,G),W.asyncDep){if(B&&B.registerDep(W,J,G),!h.el){const z=W.subTree=Ge(ze);O(null,z,m,S)}}else J(W,h,m,S,B,V,G)},Y=(h,m,S)=>{const $=m.component=h.component;if(Ph(h,m,S))if($.asyncDep&&!$.asyncResolved){H($,m,S);return}else $.next=m,$.update();else m.el=h.el,$.vnode=m},J=(h,m,S,$,B,V,G)=>{const W=()=>{if(h.isMounted){let{next:X,bu:te,u:se,parent:fe,vnode:_e}=h;{const Xe=Zu(h);if(Xe){X&&(X.el=_e.el,H(h,X,G)),Xe.asyncDep.then(()=>{h.isUnmounted||W()});return}}let ge=X,Je;nn(h,!1),X?(X.el=_e.el,H(h,X,G)):X=_e,te&&rr(te),(Je=X.props&&X.props.onVnodeBeforeUpdate)&&st(Je,fe,X,_e),nn(h,!0);const $e=ss(h),ft=h.subTree;h.subTree=$e,x(ft,$e,f(ft.el),k(ft),h,B,V),X.el=$e.el,ge===null&&Oh(h,$e.el),se&&Fe(se,B),(Je=X.props&&X.props.onVnodeUpdated)&&Fe(()=>st(Je,fe,X,_e),B)}else{let X;const{el:te,props:se}=m,{bm:fe,m:_e,parent:ge,root:Je,type:$e}=h,ft=Mn(m);if(nn(h,!1),fe&&rr(fe),!ft&&(X=se&&se.onVnodeBeforeMount)&&st(X,ge,m),nn(h,!0),te&&Ee){const Xe=()=>{h.subTree=ss(h),Ee(te,h.subTree,h,B,null)};ft&&$e.__asyncHydrate?$e.__asyncHydrate(te,h,Xe):Xe()}else{Je.ce&&Je.ce._injectChildStyle($e);const Xe=h.subTree=ss(h);x(null,Xe,S,$,h,B,V),m.el=Xe.el}if(_e&&Fe(_e,B),!ft&&(X=se&&se.onVnodeMounted)){const Xe=m;Fe(()=>st(X,ge,Xe),B)}(m.shapeFlag&256||ge&&Mn(ge.vnode)&&ge.vnode.shapeFlag&256)&&h.a&&Fe(h.a,B),h.isMounted=!0,m=S=$=null}};h.scope.on();const z=h.effect=new ou(W);h.scope.off();const j=h.update=z.run.bind(z),oe=h.job=z.runIfDirty.bind(z);oe.i=h,oe.id=h.uid,z.scheduler=()=>Ei(oe),nn(h,!0),j()},H=(h,m,S)=>{m.component=h;const $=h.vnode.props;h.vnode=m,h.next=null,ph(h,m.props,$,S),yh(h,m.children,S),Zt(),Xi(h),en()},N=(h,m,S,$,B,V,G,W,z=!1)=>{const j=h&&h.children,oe=h?h.shapeFlag:0,X=m.children,{patchFlag:te,shapeFlag:se}=m;if(te>0){if(te&128){ue(j,X,S,$,B,V,G,W,z);return}else if(te&256){ne(j,X,S,$,B,V,G,W,z);return}}se&8?(oe&16&&ee(j,B,V),X!==j&&u(S,X)):oe&16?se&16?ue(j,X,S,$,B,V,G,W,z):ee(j,B,V,!0):(oe&8&&u(S,""),se&16&&M(X,S,$,B,V,G,W,z))},ne=(h,m,S,$,B,V,G,W,z)=>{h=h||Ln,m=m||Ln;const j=h.length,oe=m.length,X=Math.min(j,oe);let te;for(te=0;te<X;te++){const se=m[te]=z?Ut(m[te]):St(m[te]);x(h[te],se,S,null,B,V,G,W,z)}j>oe?ee(h,B,V,!0,!1,X):M(m,S,$,B,V,G,W,z,X)},ue=(h,m,S,$,B,V,G,W,z)=>{let j=0;const oe=m.length;let X=h.length-1,te=oe-1;for(;j<=X&&j<=te;){const se=h[j],fe=m[j]=z?Ut(m[j]):St(m[j]);if(Wt(se,fe))x(se,fe,S,null,B,V,G,W,z);else break;j++}for(;j<=X&&j<=te;){const se=h[X],fe=m[te]=z?Ut(m[te]):St(m[te]);if(Wt(se,fe))x(se,fe,S,null,B,V,G,W,z);else break;X--,te--}if(j>X){if(j<=te){const se=te+1,fe=se<oe?m[se].el:$;for(;j<=te;)x(null,m[j]=z?Ut(m[j]):St(m[j]),S,fe,B,V,G,W,z),j++}}else if(j>te)for(;j<=X;)q(h[j],B,V,!0),j++;else{const se=j,fe=j,_e=new Map;for(j=fe;j<=te;j++){const Ye=m[j]=z?Ut(m[j]):St(m[j]);Ye.key!=null&&_e.set(Ye.key,j)}let ge,Je=0;const $e=te-fe+1;let ft=!1,Xe=0;const Hn=new Array($e);for(j=0;j<$e;j++)Hn[j]=0;for(j=se;j<=X;j++){const Ye=h[j];if(Je>=$e){q(Ye,B,V,!0);continue}let xt;if(Ye.key!=null)xt=_e.get(Ye.key);else for(ge=fe;ge<=te;ge++)if(Hn[ge-fe]===0&&Wt(Ye,m[ge])){xt=ge;break}xt===void 0?q(Ye,B,V,!0):(Hn[xt-fe]=j+1,xt>=Xe?Xe=xt:ft=!0,x(Ye,m[xt],S,null,B,V,G,W,z),Je++)}const zi=ft?xh(Hn):Ln;for(ge=zi.length-1,j=$e-1;j>=0;j--){const Ye=fe+j,xt=m[Ye],Wi=Ye+1<oe?m[Ye+1].el:$;Hn[j]===0?x(null,xt,S,Wi,B,V,G,W,z):ft&&(ge<0||j!==zi[ge]?ce(xt,S,Wi,2):ge--)}}},ce=(h,m,S,$,B=null)=>{const{el:V,type:G,transition:W,children:z,shapeFlag:j}=h;if(j&6){ce(h.component.subTree,m,S,$);return}if(j&128){h.suspense.move(m,S,$);return}if(j&64){G.move(h,m,S,Z);return}if(G===ht){r(V,m,S);for(let X=0;X<z.length;X++)ce(z[X],m,S,$);r(h.anchor,m,S);return}if(G===is){y(h,m,S);return}if($!==2&&j&1&&W)if($===0)W.beforeEnter(V),r(V,m,S),Fe(()=>W.enter(V),B);else{const{leave:X,delayLeave:te,afterLeave:se}=W,fe=()=>r(V,m,S),_e=()=>{X(V,()=>{fe(),se&&se()})};te?te(V,fe,_e):_e()}else r(V,m,S)},q=(h,m,S,$=!1,B=!1)=>{const{type:V,props:G,ref:W,children:z,dynamicChildren:j,shapeFlag:oe,patchFlag:X,dirs:te,cacheIndex:se}=h;if(X===-2&&(B=!1),W!=null&&co(W,null,S,h,!0),se!=null&&(m.renderCache[se]=void 0),oe&256){m.ctx.deactivate(h);return}const fe=oe&1&&te,_e=!Mn(h);let ge;if(_e&&(ge=G&&G.onVnodeBeforeUnmount)&&st(ge,m,h),oe&6)Le(h.component,S,$);else{if(oe&128){h.suspense.unmount(S,$);return}fe&&tn(h,null,m,"beforeUnmount"),oe&64?h.type.remove(h,m,S,Z,$):j&&!j.hasOnce&&(V!==ht||X>0&&X&64)?ee(j,m,S,!1,!0):(V===ht&&X&384||!B&&oe&16)&&ee(z,m,S),$&&he(h)}(_e&&(ge=G&&G.onVnodeUnmounted)||fe)&&Fe(()=>{ge&&st(ge,m,h),fe&&tn(h,null,m,"unmounted")},S)},he=h=>{const{type:m,el:S,anchor:$,transition:B}=h;if(m===ht){Re(S,$);return}if(m===is){E(h);return}const V=()=>{o(S),B&&!B.persisted&&B.afterLeave&&B.afterLeave()};if(h.shapeFlag&1&&B&&!B.persisted){const{leave:G,delayLeave:W}=B,z=()=>G(S,V);W?W(h.el,V,z):z()}else V()},Re=(h,m)=>{let S;for(;h!==m;)S=d(h),o(h),h=S;o(m)},Le=(h,m,S)=>{const{bum:$,scope:B,job:V,subTree:G,um:W,m:z,a:j}=h;ho(z),ho(j),$&&rr($),B.stop(),V&&(V.flags|=8,q(G,h,m,S)),W&&Fe(W,m),Fe(()=>{h.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},ee=(h,m,S,$=!1,B=!1,V=0)=>{for(let G=V;G<h.length;G++)q(h[G],m,S,$,B)},k=h=>{if(h.shapeFlag&6)return k(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const m=d(h.anchor||h.el),S=m&&m[Tu];return S?d(S):m};let Q=!1;const K=(h,m,S)=>{h==null?m._vnode&&q(m._vnode,null,null,!0):x(m._vnode||null,h,m,null,null,null,S),m._vnode=h,Q||(Q=!0,Xi(),Cu(),Q=!1)},Z={p:x,um:q,m:ce,r:he,mt:T,mc:M,pc:N,pbc:C,n:k,o:e};let pe,Ee;return t&&([pe,Ee]=t(Z)),{render:K,hydrate:pe,createApp:fh(K,pe)}}function os({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function nn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function wh(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ti(e,t,n=!1){const r=e.children,o=t.children;if(re(r)&&re(o))for(let s=0;s<r.length;s++){const i=r[s];let l=o[s];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[s]=Ut(o[s]),l.el=i.el),!n&&l.patchFlag!==-2&&Ti(i,l)),l.type===$o&&(l.el=i.el)}}function xh(e){const t=e.slice(),n=[0];let r,o,s,i,l;const a=e.length;for(r=0;r<a;r++){const c=e[r];if(c!==0){if(o=n[n.length-1],e[o]<c){t[r]=o,n.push(r);continue}for(s=0,i=n.length-1;s<i;)l=s+i>>1,e[n[l]]<c?s=l+1:i=l;c<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}function Zu(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Zu(t)}function ho(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Eh=Symbol.for("v-scx"),Ch=()=>et(Eh);function be(e,t,n){return ec(e,t,n)}function ec(e,t,n=ye){const{immediate:r,deep:o,flush:s,once:i}=n,l=Te({},n),a=t&&r||!t&&s!=="post";let c;if(yr){if(s==="sync"){const p=Ch();c=p.__watcherHandles||(p.__watcherHandles=[])}else if(!a){const p=()=>{};return p.stop=mt,p.resume=mt,p.pause=mt,p}}const u=De;l.call=(p,g,x)=>bt(p,u,g,x);let f=!1;s==="post"?l.scheduler=p=>{Fe(p,u&&u.suspense)}:s!=="sync"&&(f=!0,l.scheduler=(p,g)=>{g?p():Ei(p)}),l.augmentJob=p=>{t&&(p.flags|=4),f&&(p.flags|=2,u&&(p.id=u.uid,p.i=u))};const d=qd(e,t,l);return yr&&(c?c.push(d):a&&d()),d}function Sh(e,t,n){const r=this.proxy,o=Ce(e)?e.includes(".")?tc(r,e):()=>r[e]:e.bind(r,r);let s;ie(t)?s=t:(s=t.handler,n=t);const i=Rr(this),l=ec(o,s.bind(r),n);return i(),l}function tc(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}const kh=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ut(t)}Modifiers`]||e[`${vn(t)}Modifiers`];function Ah(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ye;let o=n;const s=t.startsWith("update:"),i=s&&kh(r,t.slice(7));i&&(i.trim&&(o=n.map(u=>Ce(u)?u.trim():u)),i.number&&(o=n.map(td)));let l,a=r[l=Jo(t)]||r[l=Jo(ut(t))];!a&&s&&(a=r[l=Jo(vn(t))]),a&&bt(a,e,6,o);const c=r[l+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,bt(c,e,6,o)}}function nc(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const s=e.emits;let i={},l=!1;if(!ie(e)){const a=c=>{const u=nc(c,t,!0);u&&(l=!0,Te(i,u))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!s&&!l?(xe(e)&&r.set(e,null),null):(re(s)?s.forEach(a=>i[a]=null):Te(i,s),xe(e)&&r.set(e,i),i)}function Mo(e,t){return!e||!ko(t)?!1:(t=t.slice(2).replace(/Once$/,""),me(e,t[0].toLowerCase()+t.slice(1))||me(e,vn(t))||me(e,t))}function ss(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[s],slots:i,attrs:l,emit:a,render:c,renderCache:u,props:f,data:d,setupState:p,ctx:g,inheritAttrs:x}=e,w=uo(e);let O,v;try{if(n.shapeFlag&4){const E=o||r,F=E;O=St(c.call(F,E,u,f,p,d,g)),v=l}else{const E=t;O=St(E.length>1?E(f,{attrs:l,slots:i,emit:a}):E(f,null)),v=t.props?l:Th(l)}}catch(E){ar.length=0,Oo(E,e,1),O=Ge(ze)}let y=O;if(v&&x!==!1){const E=Object.keys(v),{shapeFlag:F}=y;E.length&&F&7&&(s&&E.some(fi)&&(v=Rh(v,s)),y=Ft(y,v,!1,!0))}return n.dirs&&(y=Ft(y,null,!1,!0),y.dirs=y.dirs?y.dirs.concat(n.dirs):n.dirs),n.transition&&Jt(y,n.transition),O=y,uo(w),O}const Th=e=>{let t;for(const n in e)(n==="class"||n==="style"||ko(n))&&((t||(t={}))[n]=e[n]);return t},Rh=(e,t)=>{const n={};for(const r in e)(!fi(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Ph(e,t,n){const{props:r,children:o,component:s}=e,{props:i,children:l,patchFlag:a}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?al(r,i,c):!!i;if(a&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const d=u[f];if(i[d]!==r[d]&&!Mo(c,d))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?al(r,i,c):!0:!!i;return!1}function al(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Mo(n,s))return!0}return!1}function Oh({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const po=e=>e.__isSuspense;function Fh(e,t){t&&t.pendingBranch?re(e)?t.effects.push(...e):t.effects.push(e):jd(e)}const ht=Symbol.for("v-fgt"),$o=Symbol.for("v-txt"),ze=Symbol.for("v-cmt"),is=Symbol.for("v-stc"),ar=[];let Ze=null;function rc(e=!1){ar.push(Ze=e?null:[])}function Bh(){ar.pop(),Ze=ar[ar.length-1]||null}let gr=1;function ul(e,t=!1){gr+=e,e<0&&Ze&&t&&(Ze.hasOnce=!0)}function oc(e){return e.dynamicChildren=gr>0?Ze||Ln:null,Bh(),gr>0&&Ze&&Ze.push(e),e}function f0(e,t,n,r,o,s){return oc(lc(e,t,n,r,o,s,!0))}function sc(e,t,n,r,o){return oc(Ge(e,t,n,r,o,!0))}function vr(e){return e?e.__v_isVNode===!0:!1}function Wt(e,t){return e.type===t.type&&e.key===t.key}const ic=({key:e})=>e!=null?e:null,Qr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ce(e)||Ae(e)||ie(e)?{i:We,r:e,k:t,f:!!n}:e:null);function lc(e,t=null,n=null,r=0,o=null,s=e===ht?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ic(t),ref:t&&Qr(t),scopeId:ku,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:We};return l?(Ri(a,n),s&128&&e.normalize(a)):n&&(a.shapeFlag|=Ce(n)?8:16),gr>0&&!i&&Ze&&(a.patchFlag>0||s&6)&&a.patchFlag!==32&&Ze.push(a),a}const Ge=Lh;function Lh(e,t=null,n=null,r=0,o=null,s=!1){if((!e||e===nh)&&(e=ze),vr(e)){const l=Ft(e,t,!0);return n&&Ri(l,n),gr>0&&!s&&Ze&&(l.shapeFlag&6?Ze[Ze.indexOf(e)]=l:Ze.push(l)),l.patchFlag=-2,l}if(zh(e)&&(e=e.__vccOpts),t){t=Dh(t);let{class:l,style:a}=t;l&&!Ce(l)&&(t.class=mi(l)),xe(a)&&(xi(a)&&!re(a)&&(a=Te({},a)),t.style=pi(a))}const i=Ce(e)?1:po(e)?128:Ru(e)?64:xe(e)?4:ie(e)?2:0;return lc(e,t,n,r,o,i,s,!0)}function Dh(e){return e?xi(e)||Wu(e)?Te({},e):e:null}function Ft(e,t,n=!1,r=!1){const{props:o,ref:s,patchFlag:i,children:l,transition:a}=e,c=t?Mh(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&ic(c),ref:t&&t.ref?n&&s?re(s)?s.concat(Qr(t)):[s,Qr(t)]:Qr(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ht?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ft(e.ssContent),ssFallback:e.ssFallback&&Ft(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&Jt(u,a.clone(u)),u}function Ih(e=" ",t=0){return Ge($o,null,e,t)}function d0(e="",t=!1){return t?(rc(),sc(ze,null,e)):Ge(ze,null,e)}function St(e){return e==null||typeof e=="boolean"?Ge(ze):re(e)?Ge(ht,null,e.slice()):vr(e)?Ut(e):Ge($o,null,String(e))}function Ut(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ft(e)}function Ri(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(re(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),Ri(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Wu(t)?t._ctx=We:o===3&&We&&(We.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ie(t)?(t={default:t,_ctx:We},n=32):(t=String(t),r&64?(n=16,t=[Ih(t)]):n=8);e.children=t,e.shapeFlag|=n}function Mh(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=mi([t.class,r.class]));else if(o==="style")t.style=pi([t.style,r.style]);else if(ko(o)){const s=t[o],i=r[o];i&&s!==i&&!(re(s)&&s.includes(i))&&(t[o]=s?[].concat(s,i):i)}else o!==""&&(t[o]=r[o])}return t}function st(e,t,n,r=null){bt(e,t,7,[n,r])}const $h=Uu();let qh=0;function Nh(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||$h,s={uid:qh++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new tu(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Qu(r,o),emitsOptions:nc(r,o),emit:null,emitted:null,propsDefaults:ye,inheritAttrs:r.inheritAttrs,ctx:ye,data:ye,props:ye,attrs:ye,slots:ye,refs:ye,setupState:ye,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=Ah.bind(null,s),e.ce&&e.ce(s),s}let De=null;const Se=()=>De||We;let mo,$s;{const e=Ro(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),s=>{o.length>1?o.forEach(i=>i(s)):o[0](s)}};mo=t("__VUE_INSTANCE_SETTERS__",n=>De=n),$s=t("__VUE_SSR_SETTERS__",n=>yr=n)}const Rr=e=>{const t=De;return mo(e),e.scope.on(),()=>{e.scope.off(),mo(t)}},cl=()=>{De&&De.scope.off(),mo(null)};function ac(e){return e.vnode.shapeFlag&4}let yr=!1;function Vh(e,t=!1,n=!1){t&&$s(t);const{props:r,children:o}=e.vnode,s=ac(e);hh(e,r,s,t),vh(e,o,n);const i=s?jh(e,t):void 0;return t&&$s(!1),i}function jh(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,oh);const{setup:r}=n;if(r){Zt();const o=e.setupContext=r.length>1?Hh(e):null,s=Rr(e),i=Tr(r,e,0,[e.props,o]),l=Qa(i);if(en(),s(),(l||e.sp)&&!Mn(e)&&Mu(e),l){if(i.then(cl,cl),t)return i.then(a=>{fl(e,a,t)}).catch(a=>{Oo(a,e,0)});e.asyncDep=i}else fl(e,i,t)}else uc(e,t)}function fl(e,t,n){ie(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:xe(t)&&(e.setupState=wu(t)),uc(e,n)}let dl;function uc(e,t,n){const r=e.type;if(!e.render){if(!t&&dl&&!r.render){const o=r.template||ki(e).template;if(o){const{isCustomElement:s,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:a}=r,c=Te(Te({isCustomElement:s,delimiters:l},i),a);r.render=dl(o,c)}}e.render=r.render||mt}{const o=Rr(e);Zt();try{sh(e)}finally{en(),o()}}}const Uh={get(e,t){return Ne(e,"get",""),e[t]}};function Hh(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Uh),slots:e.slots,emit:e.emit,expose:t}}function qo(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(wu(bn(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in lr)return lr[n](e)},has(t,n){return n in t||n in lr}})):e.proxy}function qs(e,t=!0){return ie(e)?e.displayName||e.name:e.name||t&&e.__name}function zh(e){return ie(e)&&"__vccOpts"in e}const R=(e,t)=>Md(e,t,yr);function P(e,t,n){const r=arguments.length;return r===2?xe(t)&&!re(t)?vr(t)?Ge(e,null,[t]):Ge(e,t):Ge(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&vr(n)&&(n=[n]),Ge(e,t,n))}const Wh="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ns;const hl=typeof window!="undefined"&&window.trustedTypes;if(hl)try{Ns=hl.createPolicy("vue",{createHTML:e=>e})}catch{}const cc=Ns?e=>Ns.createHTML(e):e=>e,Kh="http://www.w3.org/2000/svg",Qh="http://www.w3.org/1998/Math/MathML",Rt=typeof document!="undefined"?document:null,pl=Rt&&Rt.createElement("template"),Gh={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t==="svg"?Rt.createElementNS(Kh,e):t==="mathml"?Rt.createElementNS(Qh,e):n?Rt.createElement(e,{is:n}):Rt.createElement(e);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>Rt.createTextNode(e),createComment:e=>Rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const i=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{pl.innerHTML=cc(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=pl.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},It="transition",Wn="animation",$n=Symbol("_vtc"),fc={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},dc=Te({},Bu,fc),Jh=e=>(e.displayName="Transition",e.props=dc,e),go=Jh((e,{slots:t})=>P(Kd,hc(e),t)),rn=(e,t=[])=>{re(e)?e.forEach(n=>n(...t)):e&&e(...t)},ml=e=>e?re(e)?e.some(t=>t.length>1):e.length>1:!1;function hc(e){const t={};for(const b in e)b in fc||(t[b]=e[b]);if(e.css===!1)return t;const{name:n="v",type:r,duration:o,enterFromClass:s=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=s,appearActiveClass:c=i,appearToClass:u=l,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,g=Xh(o),x=g&&g[0],w=g&&g[1],{onBeforeEnter:O,onEnter:v,onEnterCancelled:y,onLeave:E,onLeaveCancelled:F,onBeforeAppear:I=O,onAppear:D=v,onAppearCancelled:M=y}=t,_=(b,U,T,Y)=>{b._enterCancelled=Y,qt(b,U?u:l),qt(b,U?c:i),T&&T()},C=(b,U)=>{b._isLeaving=!1,qt(b,f),qt(b,p),qt(b,d),U&&U()},L=b=>(U,T)=>{const Y=b?D:v,J=()=>_(U,b,T);rn(Y,[U,J]),gl(()=>{qt(U,b?a:s),Et(U,b?u:l),ml(Y)||vl(U,r,x,J)})};return Te(t,{onBeforeEnter(b){rn(O,[b]),Et(b,s),Et(b,i)},onBeforeAppear(b){rn(I,[b]),Et(b,a),Et(b,c)},onEnter:L(!1),onAppear:L(!0),onLeave(b,U){b._isLeaving=!0;const T=()=>C(b,U);Et(b,f),b._enterCancelled?(Et(b,d),Vs()):(Vs(),Et(b,d)),gl(()=>{!b._isLeaving||(qt(b,f),Et(b,p),ml(E)||vl(b,r,w,T))}),rn(E,[b,T])},onEnterCancelled(b){_(b,!1,void 0,!0),rn(y,[b])},onAppearCancelled(b){_(b,!0,void 0,!0),rn(M,[b])},onLeaveCancelled(b){C(b),rn(F,[b])}})}function Xh(e){if(e==null)return null;if(xe(e))return[ls(e.enter),ls(e.leave)];{const t=ls(e);return[t,t]}}function ls(e){return nd(e)}function Et(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[$n]||(e[$n]=new Set)).add(t)}function qt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[$n];n&&(n.delete(t),n.size||(e[$n]=void 0))}function gl(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Yh=0;function vl(e,t,n,r){const o=e._endId=++Yh,s=()=>{o===e._endId&&r()};if(n!=null)return setTimeout(s,n);const{type:i,timeout:l,propCount:a}=pc(e,t);if(!i)return r();const c=i+"end";let u=0;const f=()=>{e.removeEventListener(c,d),s()},d=p=>{p.target===e&&++u>=a&&f()};setTimeout(()=>{u<a&&f()},l+1),e.addEventListener(c,d)}function pc(e,t){const n=window.getComputedStyle(e),r=g=>(n[g]||"").split(", "),o=r(`${It}Delay`),s=r(`${It}Duration`),i=yl(o,s),l=r(`${Wn}Delay`),a=r(`${Wn}Duration`),c=yl(l,a);let u=null,f=0,d=0;t===It?i>0&&(u=It,f=i,d=s.length):t===Wn?c>0&&(u=Wn,f=c,d=a.length):(f=Math.max(i,c),u=f>0?i>c?It:Wn:null,d=u?u===It?s.length:a.length:0);const p=u===It&&/\b(transform|all)(,|$)/.test(r(`${It}Property`).toString());return{type:u,timeout:f,propCount:d,hasTransform:p}}function yl(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>bl(n)+bl(e[r])))}function bl(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Vs(){return document.body.offsetHeight}function Zh(e,t,n){const r=e[$n];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const vo=Symbol("_vod"),mc=Symbol("_vsh"),h0={beforeMount(e,{value:t},{transition:n}){e[vo]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Kn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Kn(e,!0),r.enter(e)):r.leave(e,()=>{Kn(e,!1)}):Kn(e,t))},beforeUnmount(e,{value:t}){Kn(e,t)}};function Kn(e,t){e.style.display=t?e[vo]:"none",e[mc]=!t}const ep=Symbol(""),tp=/(^|;)\s*display\s*:/;function np(e,t,n){const r=e.style,o=Ce(n);let s=!1;if(n&&!o){if(t)if(Ce(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&Gr(r,l,"")}else for(const i in t)n[i]==null&&Gr(r,i,"");for(const i in n)i==="display"&&(s=!0),Gr(r,i,n[i])}else if(o){if(t!==n){const i=r[ep];i&&(n+=";"+i),r.cssText=n,s=tp.test(n)}}else t&&e.removeAttribute("style");vo in e&&(e[vo]=s?r.display:"",e[mc]&&(r.display="none"))}const _l=/\s*!important$/;function Gr(e,t,n){if(re(n))n.forEach(r=>Gr(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=rp(e,t);_l.test(n)?e.setProperty(vn(r),n.replace(_l,""),"important"):e[r]=n}}const wl=["Webkit","Moz","ms"],as={};function rp(e,t){const n=as[t];if(n)return n;let r=ut(t);if(r!=="filter"&&r in e)return as[t]=r;r=To(r);for(let o=0;o<wl.length;o++){const s=wl[o]+r;if(s in e)return as[t]=s}return t}const xl="http://www.w3.org/1999/xlink";function El(e,t,n,r,o,s=ad(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(xl,t.slice(6,t.length)):e.setAttributeNS(xl,t,n):n==null||s&&!Ya(n)?e.removeAttribute(t):e.setAttribute(t,s?"":Yt(n)?String(n):n)}function Cl(e,t,n,r,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?cc(n):n);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const l=s==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Ya(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function op(e,t,n,r){e.addEventListener(t,n,r)}function sp(e,t,n,r){e.removeEventListener(t,n,r)}const Sl=Symbol("_vei");function ip(e,t,n,r,o=null){const s=e[Sl]||(e[Sl]={}),i=s[t];if(r&&i)i.value=r;else{const[l,a]=lp(t);if(r){const c=s[t]=cp(r,o);op(e,l,c,a)}else i&&(sp(e,l,i,a),s[t]=void 0)}}const kl=/(?:Once|Passive|Capture)$/;function lp(e){let t;if(kl.test(e)){t={};let r;for(;r=e.match(kl);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):vn(e.slice(2)),t]}let us=0;const ap=Promise.resolve(),up=()=>us||(ap.then(()=>us=0),us=Date.now());function cp(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;bt(fp(r,n.value),t,5,[r])};return n.value=e,n.attached=up(),n}function fp(e,t){if(re(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const Al=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,dp=(e,t,n,r,o,s)=>{const i=o==="svg";t==="class"?Zh(e,r,i):t==="style"?np(e,n,r):ko(t)?fi(t)||ip(e,t,n,r,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):hp(e,t,r,i))?(Cl(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&El(e,t,r,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ce(r))?Cl(e,ut(t),r,s,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),El(e,t,r,i))};function hp(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Al(t)&&ie(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Al(t)&&Ce(n)?!1:t in e}const gc=new WeakMap,vc=new WeakMap,yo=Symbol("_moveCb"),Tl=Symbol("_enterCb"),pp=e=>(delete e.props.mode,e),mp=pp({name:"TransitionGroup",props:Te({},dc,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Se(),r=Fu();let o,s;return Si(()=>{if(!o.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!_p(o[0].el,n.vnode.el,i))return;o.forEach(vp),o.forEach(yp);const l=o.filter(bp);Vs(),l.forEach(a=>{const c=a.el,u=c.style;Et(c,i),u.transform=u.webkitTransform=u.transitionDuration="";const f=c[yo]=d=>{d&&d.target!==c||(!d||/transform$/.test(d.propertyName))&&(c.removeEventListener("transitionend",f),c[yo]=null,qt(c,i))};c.addEventListener("transitionend",f)})}),()=>{const i=le(e),l=hc(i);let a=i.tag||ht;if(o=[],s)for(let c=0;c<s.length;c++){const u=s[c];u.el&&u.el instanceof Element&&(o.push(u),Jt(u,mr(u,l,r,n)),gc.set(u,u.el.getBoundingClientRect()))}s=t.default?Ci(t.default()):[];for(let c=0;c<s.length;c++){const u=s[c];u.key!=null&&Jt(u,mr(u,l,r,n))}return Ge(a,null,s)}}}),gp=mp;function vp(e){const t=e.el;t[yo]&&t[yo](),t[Tl]&&t[Tl]()}function yp(e){vc.set(e,e.el.getBoundingClientRect())}function bp(e){const t=gc.get(e),n=vc.get(e),r=t.left-n.left,o=t.top-n.top;if(r||o){const s=e.el.style;return s.transform=s.webkitTransform=`translate(${r}px,${o}px)`,s.transitionDuration="0s",e}}function _p(e,t,n){const r=e.cloneNode(),o=e[$n];o&&o.forEach(l=>{l.split(/\s+/).forEach(a=>a&&r.classList.remove(a))}),n.split(/\s+/).forEach(l=>l&&r.classList.add(l)),r.style.display="none";const s=t.nodeType===1?t:t.parentNode;s.appendChild(r);const{hasTransform:i}=pc(r);return s.removeChild(r),i}const wp=["ctrl","shift","alt","meta"],xp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>wp.some(n=>e[`${n}Key`]&&!t.includes(n))},p0=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(o,...s)=>{for(let i=0;i<t.length;i++){const l=xp[t[i]];if(l&&l(o,t))return}return e(o,...s)})},Ep=Te({patchProp:dp},Gh);let Rl;function Cp(){return Rl||(Rl=bh(Ep))}const yc=(...e)=>{const t=Cp().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=kp(r);if(!o)return;const s=t._component;!ie(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,Sp(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function Sp(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function kp(e){return Ce(e)?document.querySelector(e):e}function _n(e,t,n,r){return Object.defineProperty(e,t,{get:n,set:r,enumerable:!0}),e}function m0(e,t){for(const n in t)_n(e,n,t[n]);return e}const Xt=de(!1);let js;function Ap(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[4]||n[2]||"0",platform:t[0]||""}}function Tp(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const bc="ontouchstart"in window||window.navigator.maxTouchPoints>0;function Rp(e){const t=e.toLowerCase(),n=Tp(t),r=Ap(t,n),o={};r.browser&&(o[r.browser]=!0,o.version=r.version,o.versionNumber=parseInt(r.version,10)),r.platform&&(o[r.platform]=!0);const s=o.android||o.ios||o.bb||o.blackberry||o.ipad||o.iphone||o.ipod||o.kindle||o.playbook||o.silk||o["windows phone"];if(s===!0||t.indexOf("mobile")!==-1?o.mobile=!0:o.desktop=!0,o["windows phone"]&&(o.winphone=!0,delete o["windows phone"]),o.edga||o.edgios||o.edg?(o.edge=!0,r.browser="edge"):o.crios?(o.chrome=!0,r.browser="chrome"):o.fxios&&(o.firefox=!0,r.browser="firefox"),(o.ipod||o.ipad||o.iphone)&&(o.ios=!0),o.vivaldi&&(r.browser="vivaldi",o.vivaldi=!0),(o.chrome||o.opr||o.safari||o.vivaldi||o.mobile===!0&&o.ios!==!0&&s!==!0)&&(o.webkit=!0),o.opr&&(r.browser="opera",o.opera=!0),o.safari&&(o.blackberry||o.bb?(r.browser="blackberry",o.blackberry=!0):o.playbook?(r.browser="playbook",o.playbook=!0):o.android?(r.browser="android",o.android=!0):o.kindle?(r.browser="kindle",o.kindle=!0):o.silk&&(r.browser="silk",o.silk=!0)),o.name=r.browser,o.platform=r.platform,t.indexOf("electron")!==-1)o.electron=!0;else if(document.location.href.indexOf("-extension://")!==-1)o.bex=!0;else{if(window.Capacitor!==void 0?(o.capacitor=!0,o.nativeMobile=!0,o.nativeMobileWrapper="capacitor"):(window._cordovaNative!==void 0||window.cordova!==void 0)&&(o.cordova=!0,o.nativeMobile=!0,o.nativeMobileWrapper="cordova"),Xt.value===!0&&(js={is:{...o}}),bc===!0&&o.mac===!0&&(o.desktop===!0&&o.safari===!0||o.nativeMobile===!0&&o.android!==!0&&o.ios!==!0&&o.ipad!==!0)){delete o.mac,delete o.desktop;const i=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(o,{mobile:!0,ios:!0,platform:i,[i]:!0})}o.mobile!==!0&&window.navigator.userAgentData&&window.navigator.userAgentData.mobile&&(delete o.desktop,o.mobile=!0)}return o}const Pl=navigator.userAgent||navigator.vendor||window.opera,Pp={has:{touch:!1,webStorage:!1},within:{iframe:!1}},Be={userAgent:Pl,is:Rp(Pl),has:{touch:bc},within:{iframe:window.self!==window.top}},Us={install(e){const{$q:t}=e;Xt.value===!0?(e.onSSRHydrated.push(()=>{Object.assign(t.platform,Be),Xt.value=!1}),t.platform=yn(this)):t.platform=this}};{let e;_n(Be.has,"webStorage",()=>{if(e!==void 0)return e;try{if(window.localStorage)return e=!0,!0}catch{}return e=!1,!1}),Object.assign(Us,Be),Xt.value===!0&&(Object.assign(Us,js,Pp),js=null)}function Me(e){return bn(Fo(e))}function Op(e){return bn(e)}const No=(e,t)=>{const n=yn(e);for(const r in e)_n(t,r,()=>n[r],o=>{n[r]=o});return t},Ke={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(Ke,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch{}function br(){}function g0(e){return e.button===0}function Fp(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function Bp(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;for(;n;){if(t.push(n),n.tagName==="HTML")return t.push(document),t.push(window),t;n=n.parentElement}}function bo(e){e.stopPropagation()}function Kt(e){e.cancelable!==!1&&e.preventDefault()}function it(e){e.cancelable!==!1&&e.preventDefault(),e.stopPropagation()}function v0(e,t){if(e===void 0||t===!0&&e.__dragPrevented===!0)return;const n=t===!0?r=>{r.__dragPrevented=!0,r.addEventListener("dragstart",Kt,Ke.notPassiveCapture)}:r=>{delete r.__dragPrevented,r.removeEventListener("dragstart",Kt,Ke.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function Lp(e,t,n){const r=`__q_${t}_evt`;e[r]=e[r]!==void 0?e[r].concat(n):n,n.forEach(o=>{o[0].addEventListener(o[1],e[o[2]],Ke[o[3]])})}function Dp(e,t){const n=`__q_${t}_evt`;e[n]!==void 0&&(e[n].forEach(r=>{r[0].removeEventListener(r[1],e[r[2]],Ke[r[3]])}),e[n]=void 0)}function _c(e,t=250,n){let r=null;function o(){const s=arguments,i=()=>{r=null,n!==!0&&e.apply(this,s)};r!==null?clearTimeout(r):n===!0&&e.apply(this,s),r=setTimeout(i,t)}return o.cancel=()=>{r!==null&&clearTimeout(r)},o}const cs=["sm","md","lg","xl"],{passive:Ol}=Ke;var Ip=No({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:br,setDebounce:br,install({$q:e,onSSRHydrated:t}){if(e.screen=this,this.__installed===!0){e.config.screen!==void 0&&(e.config.screen.bodyClasses===!1?document.body.classList.remove(`screen--${this.name}`):this.__update(!0));return}const{visualViewport:n}=window,r=n||window,o=document.scrollingElement||document.documentElement,s=n===void 0||Be.is.mobile===!0?()=>[Math.max(window.innerWidth,o.clientWidth),Math.max(window.innerHeight,o.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-o.clientWidth,n.height*n.scale+window.innerHeight-o.clientHeight],i=e.config.screen!==void 0&&e.config.screen.bodyClasses===!0;this.__update=f=>{const[d,p]=s();if(p!==this.height&&(this.height=p),d!==this.width)this.width=d;else if(f!==!0)return;let g=this.sizes;this.gt.xs=d>=g.sm,this.gt.sm=d>=g.md,this.gt.md=d>=g.lg,this.gt.lg=d>=g.xl,this.lt.sm=d<g.sm,this.lt.md=d<g.md,this.lt.lg=d<g.lg,this.lt.xl=d<g.xl,this.xs=this.lt.sm,this.sm=this.gt.xs===!0&&this.lt.md===!0,this.md=this.gt.sm===!0&&this.lt.lg===!0,this.lg=this.gt.md===!0&&this.lt.xl===!0,this.xl=this.gt.lg,g=this.xs===!0&&"xs"||this.sm===!0&&"sm"||this.md===!0&&"md"||this.lg===!0&&"lg"||"xl",g!==this.name&&(i===!0&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${g}`)),this.name=g)};let l,a={},c=16;this.setSizes=f=>{cs.forEach(d=>{f[d]!==void 0&&(a[d]=f[d])})},this.setDebounce=f=>{c=f};const u=()=>{const f=getComputedStyle(document.body);f.getPropertyValue("--q-size-sm")&&cs.forEach(d=>{this.sizes[d]=parseInt(f.getPropertyValue(`--q-size-${d}`),10)}),this.setSizes=d=>{cs.forEach(p=>{d[p]&&(this.sizes[p]=d[p])}),this.__update(!0)},this.setDebounce=d=>{l!==void 0&&r.removeEventListener("resize",l,Ol),l=d>0?_c(this.__update,d):this.__update,r.addEventListener("resize",l,Ol)},this.setDebounce(c),Object.keys(a).length!==0?(this.setSizes(a),a=void 0):this.__update(),i===!0&&this.name==="xs"&&document.body.classList.add("screen--xs")};Xt.value===!0?t.push(u):u()}});const qe=No({isActive:!1,mode:!1},{__media:void 0,set(e){qe.mode=e,e==="auto"?(qe.__media===void 0&&(qe.__media=window.matchMedia("(prefers-color-scheme: dark)"),qe.__updateMedia=()=>{qe.set("auto")},qe.__media.addListener(qe.__updateMedia)),e=qe.__media.matches):qe.__media!==void 0&&(qe.__media.removeListener(qe.__updateMedia),qe.__media=void 0),qe.isActive=e===!0,document.body.classList.remove(`body--${e===!0?"light":"dark"}`),document.body.classList.add(`body--${e===!0?"dark":"light"}`)},toggle(){qe.set(qe.isActive===!1)},install({$q:e,ssrContext:t}){const{dark:n}=e.config;e.dark=this,this.__installed!==!0&&this.set(n!==void 0?n:!1)}});function Mp(e,t,n=document.body){if(typeof e!="string")throw new TypeError("Expected a string as propName");if(typeof t!="string")throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}let wc=!1;function $p(e){wc=e.isComposing===!0}function xc(e){return wc===!0||e!==Object(e)||e.isComposing===!0||e.qKeyEvent===!0}function _r(e,t){return xc(e)===!0?!1:[].concat(t).includes(e.keyCode)}function Ec(e){if(e.ios===!0)return"ios";if(e.android===!0)return"android"}function qp({is:e,has:t,within:n},r){const o=[e.desktop===!0?"desktop":"mobile",`${t.touch===!1?"no-":""}touch`];if(e.mobile===!0){const s=Ec(e);s!==void 0&&o.push("platform-"+s)}if(e.nativeMobile===!0){const s=e.nativeMobileWrapper;o.push(s),o.push("native-mobile"),e.ios===!0&&(r[s]===void 0||r[s].iosStatusBarPadding!==!1)&&o.push("q-ios-padding")}else e.electron===!0?o.push("electron"):e.bex===!0&&o.push("bex");return n.iframe===!0&&o.push("within-iframe"),o}function Np(){const{is:e}=Be,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(e.nativeMobile!==!0&&e.electron!==!0&&e.bex!==!0){if(e.desktop===!0)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(e.mobile===!0){n.delete("desktop"),n.add("mobile"),n.delete("platform-ios"),n.delete("platform-android");const o=Ec(e);o!==void 0&&n.add(`platform-${o}`)}}Be.has.touch===!0&&(n.delete("no-touch"),n.add("touch")),Be.within.iframe===!0&&n.add("within-iframe");const r=Array.from(n).join(" ");t!==r&&(document.body.className=r)}function Vp(e){for(const t in e)Mp(t,e[t])}var jp={install(e){if(this.__installed!==!0){if(Xt.value===!0)Np();else{const{$q:t}=e;t.config.brand!==void 0&&Vp(t.config.brand);const n=qp(Be,t.config);document.body.classList.add.apply(document.body.classList,n)}Be.is.ios===!0&&document.body.addEventListener("touchstart",br),window.addEventListener("keydown",$p,!0)}}};const Cc=()=>!0;function Up(e){return typeof e=="string"&&e!==""&&e!=="/"&&e!=="#/"}function Hp(e){return e.startsWith("#")===!0&&(e=e.substring(1)),e.startsWith("/")===!1&&(e="/"+e),e.endsWith("/")===!0&&(e=e.substring(0,e.length-1)),"#"+e}function zp(e){if(e.backButtonExit===!1)return()=>!1;if(e.backButtonExit==="*")return Cc;const t=["#/"];return Array.isArray(e.backButtonExit)===!0&&t.push(...e.backButtonExit.filter(Up).map(Hp)),()=>t.includes(window.location.hash)}var Hs={__history:[],add:br,remove:br,install({$q:e}){if(this.__installed===!0)return;const{cordova:t,capacitor:n}=Be.is;if(t!==!0&&n!==!0)return;const r=e.config[t===!0?"cordova":"capacitor"];if(r!==void 0&&r.backButton===!1||n===!0&&(window.Capacitor===void 0||window.Capacitor.Plugins.App===void 0))return;this.add=i=>{i.condition===void 0&&(i.condition=Cc),this.__history.push(i)},this.remove=i=>{const l=this.__history.indexOf(i);l>=0&&this.__history.splice(l,1)};const o=zp(Object.assign({backButtonExit:!0},r)),s=()=>{if(this.__history.length){const i=this.__history[this.__history.length-1];i.condition()===!0&&(this.__history.pop(),i.handler())}else o()===!0?navigator.app.exitApp():window.history.back()};t===!0?document.addEventListener("deviceready",()=>{document.addEventListener("backbutton",s,!1)}):window.Capacitor.Plugins.App.addListener("backButton",s)}},Fl={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>e===1?"1 record selected.":(e===0?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function Bl(){const e=Array.isArray(navigator.languages)===!0&&navigator.languages.length!==0?navigator.languages[0]:navigator.language;if(typeof e=="string")return e.split(/[-_]/).map((t,n)=>n===0?t.toLowerCase():n>1||t.length<4?t.toUpperCase():t[0].toUpperCase()+t.slice(1).toLowerCase()).join("-")}const Ht=No({__qLang:{}},{getLocale:Bl,set(e=Fl,t){const n={...e,rtl:e.rtl===!0,getLocale:Bl};{if(n.set=Ht.set,Ht.__langConfig===void 0||Ht.__langConfig.noHtmlAttrs!==!0){const r=document.documentElement;r.setAttribute("dir",n.rtl===!0?"rtl":"ltr"),r.setAttribute("lang",n.isoName)}Object.assign(Ht.__qLang,n)}},install({$q:e,lang:t,ssrContext:n}){e.lang=Ht.__qLang,Ht.__langConfig=e.config.lang,this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qLang,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(o=>o!=="set"&&o!=="getLocale")}}),this.set(t||Fl))}});var Wp={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}};const _o=No({iconMapFn:null,__qIconSet:{}},{set(e,t){const n={...e};n.set=_o.set,Object.assign(_o.__qIconSet,n)},install({$q:e,iconSet:t,ssrContext:n}){e.config.iconMapFn!==void 0&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__qIconSet,_n(e,"iconMapFn",()=>this.iconMapFn,r=>{this.iconMapFn=r}),this.__installed===!0?t!==void 0&&this.set(t):(this.props=new Proxy(this.__qIconSet,{get(){return Reflect.get(...arguments)},ownKeys(r){return Reflect.ownKeys(r).filter(o=>o!=="set")}}),this.set(t||Wp))}}),Kp="_q_",y0="_q_l_",b0="_q_pc_",Qp="_q_fo_",_0="_q_tabs_";function w0(){}const wo={};let Sc=!1;function Gp(){Sc=!0}function fs(e,t){if(e===t)return!0;if(e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;let n,r;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(fs(e[r],t[r])!==!0)return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let s=e.entries();for(r=s.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=s.next()}for(s=e.entries(),r=s.next();r.done!==!0;){if(fs(r.value[1],t.get(r.value[0]))!==!0)return!1;r=s.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const s=e.entries();for(r=s.next();r.done!==!0;){if(t.has(r.value[0])!==!0)return!1;r=s.next()}return!0}if(e.buffer!=null&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const o=Object.keys(e).filter(s=>e[s]!==void 0);if(n=o.length,n!==Object.keys(t).filter(s=>t[s]!==void 0).length)return!1;for(r=n;r--!==0;){const s=o[r];if(fs(e[s],t[s])!==!0)return!1}return!0}return e!==e&&t!==t}function kt(e){return e!==null&&typeof e=="object"&&Array.isArray(e)!==!0}function x0(e){return Object.prototype.toString.call(e)==="[object Date]"}function E0(e){return typeof e=="number"&&isFinite(e)}const Ll=[Us,jp,qe,Ip,Hs,Ht,_o];function kc(e,t){const n=yc(e);n.config.globalProperties=t.config.globalProperties;const{reload:r,...o}=t._context;return Object.assign(n._context,o),n}function Dl(e,t){t.forEach(n=>{n.install(e),n.__installed=!0})}function Jp(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(Kp,n.$q),Dl(n,Ll),t.components!==void 0&&Object.values(t.components).forEach(r=>{kt(r)===!0&&r.name!==void 0&&e.component(r.name,r)}),t.directives!==void 0&&Object.values(t.directives).forEach(r=>{kt(r)===!0&&r.name!==void 0&&e.directive(r.name,r)}),t.plugins!==void 0&&Dl(n,Object.values(t.plugins).filter(r=>typeof r.install=="function"&&Ll.includes(r)===!1)),Xt.value===!0&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach(r=>{r()}),n.$q.onSSRHydrated=()=>{}})}var Xp=function(e,t={}){const n={version:"2.16.8"};Sc===!1?(t.config!==void 0&&Object.assign(wo,t.config),n.config={...wo},Gp()):n.config=t.config||{},Jp(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},Yp={name:"Quasar",version:"2.16.8",install:Xp,lang:Ht,iconSet:_o};const On=class{constructor(){nt(this,"initialized",!1);nt(this,"deferredInstallPrompt");nt(this,"installPromptListeners",new Set);this.setupEventListener()}setupEventListener(){const t=n=>{console.log("\u5168\u57DF beforeinstallprompt \u4E8B\u4EF6\u5DF2\u89F8\u767C"),n.preventDefault();const r=n;this.deferredInstallPrompt=r,window.savedBeforeInstallPromptEvent=r,this.installPromptListeners.forEach(o=>o(r))};window.addEventListener("beforeinstallprompt",t)}static getInstance(){return On.instance||(On.instance=new On),On.instance}checkInstallability(){return window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://")?{canInstall:!1,message:"\u61C9\u7528\u7A0B\u5F0F\u5DF2\u7D93\u5B89\u88DD",isStandalone:!0,isIOS:this.isIOS()}:window.location.protocol==="https:"||window.location.hostname==="localhost"||window.location.hostname==="***********"?this.isIOS()?{canInstall:!0,message:"\u8ACB\u4F7F\u7528 Safari \u7684\u5206\u4EAB\u9078\u55AE\uFF0C\u9078\u64C7\u300C\u52A0\u5165\u4E3B\u756B\u9762\u300D",isStandalone:!1,isIOS:this.isIOS()}:"serviceWorker"in navigator?{canInstall:!0,message:window.savedBeforeInstallPromptEvent?"\u60A8\u53EF\u4EE5\u5B89\u88DD\u6B64\u61C9\u7528\u5230\u60A8\u7684\u88DD\u7F6E\u4E0A":"\u8ACB\u4F7F\u7528 Chrome \u700F\u89BD\u5668\u8A2A\u554F\u6B64\u9801\u9762\u4EE5\u5B89\u88DD\u61C9\u7528",isStandalone:!1,isIOS:this.isIOS()}:{canInstall:!1,message:"\u60A8\u7684\u700F\u89BD\u5668\u4E0D\u652F\u63F4 PWA \u5B89\u88DD",isStandalone:!1,isIOS:this.isIOS()}:{canInstall:!1,message:"\u5B89\u88DD PWA \u9700\u8981 HTTPS \u74B0\u5883",isStandalone:!1,isIOS:this.isIOS()}}isIOS(){return/iPhone|iPad|iPod/.test(navigator.userAgent)}initialize(){if(this.initialized){console.log("PWA \u5B89\u88DD\u670D\u52D9\u5DF2\u7D93\u521D\u59CB\u5316\u904E");return}console.log("\u6B63\u5728\u521D\u59CB\u5316 PWA \u5B89\u88DD\u670D\u52D9..."),this.initialized=!0,console.log("PWA \u5B89\u88DD\u670D\u52D9\u521D\u59CB\u5316\u5B8C\u6210")}onBeforeInstallPrompt(t){return this.installPromptListeners.add(t),this.deferredInstallPrompt&&t(this.deferredInstallPrompt),()=>{this.installPromptListeners.delete(t)}}hasInstallPrompt(){return!!this.deferredInstallPrompt||!!window.savedBeforeInstallPromptEvent}};let Jr=On;nt(Jr,"instance");const Zp=Jr.getInstance(),Fn=class{constructor(){nt(this,"checkInterval",null);nt(this,"registration",null);this.init()}static getInstance(){return Fn.instance||(Fn.instance=new Fn),Fn.instance}async init(){if("serviceWorker"in navigator)try{this.registration=await navigator.serviceWorker.getRegistration()||null,this.registration?(console.log("UpdateChecker: Service Worker \u5DF2\u8A3B\u518A"),this.startPeriodicCheck()):(console.log("UpdateChecker: \u7B49\u5F85 Service Worker \u8A3B\u518A"),navigator.serviceWorker.ready.then(t=>{this.registration=t,this.startPeriodicCheck()}))}catch(t){console.error("UpdateChecker: \u521D\u59CB\u5316\u5931\u6557",t)}}startPeriodicCheck(){this.checkInterval=window.setInterval(()=>{this.checkForUpdates()},3e4),setTimeout(()=>this.checkForUpdates(),5e3)}async checkForUpdates(){if(!this.registration){console.log("UpdateChecker: Service Worker \u672A\u8A3B\u518A\uFF0C\u8DF3\u904E\u6AA2\u67E5");return}try{console.log("UpdateChecker: \u6AA2\u67E5\u66F4\u65B0\u4E2D..."),await this.registration.update(),this.registration.waiting?(console.log("UpdateChecker: \u767C\u73FE\u65B0\u7248\u672C\uFF0C\u6E96\u5099\u66F4\u65B0"),this.handleUpdate()):this.registration.installing?(console.log("UpdateChecker: \u65B0\u7248\u672C\u6B63\u5728\u5B89\u88DD\u4E2D"),this.registration.installing.addEventListener("statechange",()=>{var t,n;((n=(t=this.registration)==null?void 0:t.installing)==null?void 0:n.state)==="installed"&&(console.log("UpdateChecker: \u65B0\u7248\u672C\u5B89\u88DD\u5B8C\u6210"),this.handleUpdate())})):console.log("UpdateChecker: \u6C92\u6709\u767C\u73FE\u65B0\u7248\u672C")}catch(t){if(console.error("UpdateChecker: \u6AA2\u67E5\u66F4\u65B0\u5931\u6557",t),t instanceof Error&&t.message.includes("ServiceWorker script evaluation failed")){console.log("UpdateChecker: Service Worker \u8173\u672C\u8A55\u4F30\u5931\u6557\uFF0C\u5617\u8A66\u91CD\u65B0\u8A3B\u518A");try{await navigator.serviceWorker.register("/sw.js"),console.log("UpdateChecker: Service Worker \u91CD\u65B0\u8A3B\u518A\u6210\u529F")}catch(n){console.error("UpdateChecker: Service Worker \u91CD\u65B0\u8A3B\u518A\u5931\u6557",n)}}}}handleUpdate(){var t;!((t=this.registration)!=null&&t.waiting)||(console.log("UpdateChecker: \u767C\u73FE Service Worker \u66F4\u65B0"),this.showUpdateDialog())}showUpdateDialog(){if(document.getElementById("sw-update-dialog"))return;const t=document.createElement("div");t.id="sw-update-dialog",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const n=document.createElement("div");n.style.cssText=`
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `,n.innerHTML=`
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          \u{1F504} \u61C9\u7528\u7A0B\u5F0F\u66F4\u65B0
        </div>
        <div style="color: #666; font-size: 14px;">
          \u6AA2\u6E2C\u5230\u65B0\u7684\u61C9\u7528\u7A0B\u5F0F\u7248\u672C\uFF0C\u662F\u5426\u8981\u7ACB\u5373\u66F4\u65B0\uFF1F
        </div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="sw-update-later" style="
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">\u7A0D\u5F8C\u66F4\u65B0</button>
        <button id="sw-update-now" style="
          padding: 8px 16px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">\u7ACB\u5373\u66F4\u65B0</button>
      </div>
    `,t.appendChild(n),document.body.appendChild(t);const r=t.querySelector("#sw-update-now"),o=t.querySelector("#sw-update-later");r==null||r.addEventListener("click",()=>{document.body.removeChild(t),this.performUpdate()}),o==null||o.addEventListener("click",()=>{document.body.removeChild(t),console.log("UpdateChecker: \u7528\u6236\u9078\u64C7\u7A0D\u5F8C\u66F4\u65B0"),setTimeout(()=>{this.showUpdateDialog()},30*60*1e3)})}performUpdate(){var t;!((t=this.registration)!=null&&t.waiting)||(console.log("UpdateChecker: \u958B\u59CB\u57F7\u884C Service Worker \u66F4\u65B0"),this.registration.waiting.postMessage({type:"SKIP_WAITING"}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("UpdateChecker: \u63A7\u5236\u5668\u5DF2\u8B8A\u66F4\uFF0C\u91CD\u65B0\u8F09\u5165\u9801\u9762"),window.location.reload()},{once:!0}))}destroy(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}async manualCheck(){if(!this.registration)throw console.log("UpdateChecker: Service Worker \u672A\u8A3B\u518A"),new Error("Service Worker \u672A\u8A3B\u518A");try{return await this.registration.update(),this.registration.waiting||this.registration.installing?(this.showUpdateDialog(),!0):(console.log("UpdateChecker: \u5DF2\u662F\u6700\u65B0\u7248\u672C"),!1)}catch(t){throw console.error("UpdateChecker: \u624B\u52D5\u6AA2\u67E5\u66F4\u65B0\u5931\u6557",t),t}}};let Xr=Fn;nt(Xr,"instance");const em=Xr.getInstance(),Bn=class{constructor(){nt(this,"currentVersion");nt(this,"checkInterval",null);nt(this,"buildVersion");this.buildVersion="v1.3.0_958141",this.currentVersion=this.extractBaseVersion(this.buildVersion),console.log("VersionChecker: \u69CB\u5EFA\u7248\u672C",this.buildVersion),console.log("VersionChecker: \u7576\u524D\u7248\u672C",this.currentVersion)}extractBaseVersion(t){const n=t.match(/^(v\d+\.\d+\.\d+)/);return n?n[1]:t}static getInstance(){return Bn.instance||(Bn.instance=new Bn),Bn.instance}startVersionCheck(){this.checkInterval=window.setInterval(()=>{this.checkVersion()},6e4),setTimeout(()=>this.checkVersion(),1e4),console.log("VersionChecker: \u7248\u672C\u6AA2\u67E5\u5DF2\u555F\u52D5\uFF0C\u6AA2\u67E5\u9593\u9694: 60\u79D2")}async checkVersion(){try{const t=await fetch("/index.html?"+Date.now(),{method:"HEAD",cache:"no-cache"});if(t.ok){const n=t.headers.get("etag"),r=t.headers.get("last-modified"),o=localStorage.getItem("app-etag"),s=localStorage.getItem("app-last-modified");if(n&&o&&n!==o){console.log("VersionChecker: \u6AA2\u6E2C\u5230\u65B0\u7248\u672C (ETag \u8B8A\u66F4)"),this.handleNewVersion();return}if(r&&s&&r!==s){console.log("VersionChecker: \u6AA2\u6E2C\u5230\u65B0\u7248\u672C (Last-Modified \u8B8A\u66F4)"),this.handleNewVersion();return}n&&localStorage.setItem("app-etag",n),r&&localStorage.setItem("app-last-modified",r)}await this.checkVersionFile()}catch(t){console.error("VersionChecker: \u7248\u672C\u6AA2\u67E5\u5931\u6557",t)}}async checkVersionFile(){try{const t=await fetch("/version.json?"+Date.now(),{cache:"no-cache"});if(t.ok){const r=(await t.json()).version;r&&r!==this.buildVersion&&(console.log("VersionChecker: \u6AA2\u6E2C\u5230\u65B0\u7248\u672C",{current:this.buildVersion,server:r}),this.handleNewVersion())}}catch(t){console.log("VersionChecker: \u7248\u672C\u6587\u4EF6\u6AA2\u67E5\u5931\u6557\uFF08\u53EF\u80FD\u4E0D\u5B58\u5728\uFF09",t instanceof Error?t.message:String(t))}}handleNewVersion(){console.log("VersionChecker: \u767C\u73FE\u65B0\u7248\u672C\uFF0C\u6E96\u5099\u66F4\u65B0"),this.showUpdateDialog()}showUpdateDialog(){const t=document.createElement("div");t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const n=document.createElement("div");n.style.cssText=`
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `,n.innerHTML=`
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          \u{1F389} \u767C\u73FE\u65B0\u7248\u672C\uFF01
        </div>
        <div style="color: #666; font-size: 14px;">
          \u61C9\u7528\u7A0B\u5F0F\u6709\u65B0\u7248\u672C\u53EF\u7528\uFF0C\u662F\u5426\u8981\u7ACB\u5373\u66F4\u65B0\uFF1F
        </div>
      </div>
      <div style="display: flex; gap: 12px; justify-content: center;">
        <button id="update-later" style="
          padding: 8px 16px;
          border: 1px solid #ddd;
          background: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">\u7A0D\u5F8C\u66F4\u65B0</button>
        <button id="update-now" style="
          padding: 8px 16px;
          border: none;
          background: #1976d2;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        ">\u7ACB\u5373\u66F4\u65B0</button>
      </div>
    `,t.appendChild(n),document.body.appendChild(t);const r=t.querySelector("#update-now"),o=t.querySelector("#update-later");r==null||r.addEventListener("click",()=>{document.body.removeChild(t),this.performUpdate()}),o==null||o.addEventListener("click",()=>{document.body.removeChild(t),console.log("VersionChecker: \u7528\u6236\u9078\u64C7\u7A0D\u5F8C\u66F4\u65B0"),setTimeout(()=>{this.showUpdateDialog()},30*60*1e3)})}async performUpdate(){console.log("VersionChecker: \u958B\u59CB\u57F7\u884C\u66F4\u65B0"),this.showUpdateProgress();try{await this.clearAllCaches(),setTimeout(()=>{window.location.reload()},1500)}catch(t){console.error("VersionChecker: \u66F4\u65B0\u5931\u6557",t),this.showUpdateError()}}async performDirectUpdate(){console.log("VersionChecker: \u958B\u767C\u74B0\u5883\u76F4\u63A5\u66F4\u65B0");try{await this.clearAllCaches(),window.location.reload()}catch(t){console.error("VersionChecker: \u76F4\u63A5\u66F4\u65B0\u5931\u6557",t),window.location.reload()}}showUpdateProgress(){const t=document.createElement("div");t.id="update-progress-dialog",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10001;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const n=document.createElement("div");n.style.cssText=`
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      text-align: center;
    `,n.innerHTML=`
      <div style="margin-bottom: 16px;">
        <div style="font-size: 16px; font-weight: 600; color: #1976d2; margin-bottom: 8px;">
          \u6B63\u5728\u66F4\u65B0...
        </div>
        <div style="color: #666; font-size: 14px;">
          \u8ACB\u7A0D\u5019\uFF0C\u6B63\u5728\u4E0B\u8F09\u6700\u65B0\u7248\u672C
        </div>
      </div>
      <div style="width: 40px; height: 40px; margin: 0 auto;">
        <div style="
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #1976d2;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    `,t.appendChild(n),document.body.appendChild(t)}showUpdateError(){const t=document.getElementById("update-progress-dialog");t&&document.body.removeChild(t);const n=document.createElement("div");n.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10002;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const r=document.createElement("div");r.style.cssText=`
      background: white;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      max-width: 400px;
      text-align: center;
    `,r.innerHTML=`
      <div style="margin-bottom: 16px;">
        <div style="font-size: 18px; font-weight: 600; color: #d32f2f; margin-bottom: 8px;">
          \u274C \u66F4\u65B0\u5931\u6557
        </div>
        <div style="color: #666; font-size: 14px;">
          \u66F4\u65B0\u904E\u7A0B\u4E2D\u767C\u751F\u932F\u8AA4\uFF0C\u8ACB\u624B\u52D5\u91CD\u65B0\u6574\u7406\u9801\u9762
        </div>
      </div>
      <button id="manual-reload" style="
        padding: 8px 16px;
        border: none;
        background: #1976d2;
        color: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      ">\u624B\u52D5\u91CD\u65B0\u6574\u7406</button>
    `,n.appendChild(r),document.body.appendChild(n);const o=n.querySelector("#manual-reload");o==null||o.addEventListener("click",()=>{window.location.reload()})}async clearAllCaches(){try{if("caches"in window){const t=await caches.keys();await Promise.all(t.map(n=>caches.delete(n))),console.log("VersionChecker: \u5DF2\u6E05\u9664\u6240\u6709\u7DE9\u5B58")}localStorage.removeItem("app-etag"),localStorage.removeItem("app-last-modified")}catch(t){console.error("VersionChecker: \u6E05\u9664\u7DE9\u5B58\u5931\u6557",t)}}destroy(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}getCurrentVersion(){return this.currentVersion}getBuildVersion(){return this.buildVersion}getVersionInfo(){return{baseVersion:this.currentVersion,buildVersion:this.buildVersion,timestamp:this.extractTimestamp(this.buildVersion)}}extractTimestamp(t){const n=t.match(/_(\d+)$/);return n?n[1]:""}async manualVersionCheck(){try{return console.log("VersionChecker: \u958B\u59CB\u624B\u52D5\u7248\u672C\u6AA2\u67E5"),await this.checkIndexChanges()?(console.log("VersionChecker: \u6AA2\u6E2C\u5230 index.html \u8B8A\u66F4"),!0):await this.checkVersionFileChanges()?(console.log("VersionChecker: \u6AA2\u6E2C\u5230\u7248\u672C\u6587\u4EF6\u8B8A\u66F4"),!0):(console.log("VersionChecker: \u6C92\u6709\u6AA2\u6E2C\u5230\u7248\u672C\u8B8A\u66F4"),!1)}catch(t){throw console.error("VersionChecker: \u624B\u52D5\u7248\u672C\u6AA2\u67E5\u5931\u6557",t),t}}async checkIndexChanges(){const t=await fetch("/index.html?"+Date.now(),{method:"HEAD",cache:"no-cache"});if(!t.ok)throw new Error("\u7121\u6CD5\u7372\u53D6 index.html");const n=t.headers.get("etag"),r=t.headers.get("last-modified"),o=localStorage.getItem("app-etag"),s=localStorage.getItem("app-last-modified");return!!(n&&o&&n!==o||r&&s&&r!==s)}async checkVersionFileChanges(){try{const t=await fetch("/version.json?"+Date.now(),{cache:"no-cache"});if(t.ok){const r=(await t.json()).version;if(r&&r!==this.buildVersion)return console.log("VersionChecker: \u670D\u52D9\u5668\u7248\u672C\u8207\u672C\u5730\u7248\u672C\u4E0D\u540C",{local:this.buildVersion,server:r}),!0}return!1}catch{return console.log("VersionChecker: \u7248\u672C\u6587\u4EF6\u4E0D\u5B58\u5728\u6216\u7121\u6CD5\u8A2A\u554F"),!1}}};let Yr=Bn;nt(Yr,"instance");const Il=Yr.getInstance(),tm=Fo({name:"App",__name:"App",setup(e){return Dt(()=>{console.log("\u521D\u59CB\u5316 PWA \u670D\u52D9"),Zp.initialize(),console.log("\u555F\u52D5\u81EA\u52D5\u66F4\u65B0\u6AA2\u67E5\u5668"),Il.startVersionCheck(),"serviceWorker"in navigator&&(navigator.serviceWorker.addEventListener("message",t=>{t.data&&(t.data.type==="RELOAD_PAGE"||t.data.type==="FORCE_RELOAD")&&(console.log("\u6536\u5230 Service Worker \u91CD\u65B0\u8F09\u5165\u9801\u9762\u7684\u8ACB\u6C42:",t.data.type),window.location.reload())}),navigator.serviceWorker.addEventListener("controllerchange",()=>{console.log("Service Worker \u63A7\u5236\u5668\u5DF2\u8B8A\u66F4\uFF0C\u91CD\u65B0\u8F09\u5165\u9801\u9762"),window.location.reload()}))}),Io(()=>{em.destroy(),Il.destroy()}),(t,n)=>{const r=th("router-view");return rc(),sc(r)}}});var nm=!1;/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Ac;const Vo=e=>Ac=e,Tc=Symbol();function zs(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var ur;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(ur||(ur={}));function rm(){const e=nu(!0),t=e.run(()=>de({}));let n=[],r=[];const o=bn({install(s){Vo(o),o._a=s,s.provide(Tc,o),s.config.globalProperties.$pinia=o,r.forEach(i=>n.push(i)),r=[]},use(s){return!this._a&&!nm?r.push(s):n.push(s),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}const Rc=()=>{};function Ml(e,t,n,r=Rc){e.push(t);const o=()=>{const s=e.indexOf(t);s>-1&&(e.splice(s,1),r())};return!n&&ru()&&cd(o),o}function Cn(e,...t){e.slice().forEach(n=>{n(...t)})}const om=e=>e(),$l=Symbol(),ds=Symbol();function Ws(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];zs(o)&&zs(r)&&e.hasOwnProperty(n)&&!Ae(r)&&!Gt(r)?e[n]=Ws(o,r):e[n]=r}return e}const sm=Symbol();function im(e){return!zs(e)||!e.hasOwnProperty(sm)}const{assign:Nt}=Object;function lm(e){return!!(Ae(e)&&e.effect)}function am(e,t,n,r){const{state:o,actions:s,getters:i}=t,l=n.state.value[e];let a;function c(){l||(n.state.value[e]=o?o():{});const u=Bd(n.state.value[e]);return Nt(u,s,Object.keys(i||{}).reduce((f,d)=>(f[d]=bn(R(()=>{Vo(n);const p=n._s.get(e);return i[d].call(p,p)})),f),{}))}return a=Pc(e,c,t,n,r,!0),a}function Pc(e,t,n={},r,o,s){let i;const l=Nt({actions:{}},n),a={deep:!0};let c,u,f=[],d=[],p;const g=r.state.value[e];!s&&!g&&(r.state.value[e]={}),de({});let x;function w(M){let _;c=u=!1,typeof M=="function"?(M(r.state.value[e]),_={type:ur.patchFunction,storeId:e,events:p}):(Ws(r.state.value[e],M),_={type:ur.patchObject,payload:M,storeId:e,events:p});const C=x=Symbol();Qe().then(()=>{x===C&&(c=!0)}),u=!0,Cn(f,_,r.state.value[e])}const O=s?function(){const{state:_}=n,C=_?_():{};this.$patch(L=>{Nt(L,C)})}:Rc;function v(){i.stop(),f=[],d=[],r._s.delete(e)}const y=(M,_="")=>{if($l in M)return M[ds]=_,M;const C=function(){Vo(r);const L=Array.from(arguments),b=[],U=[];function T(H){b.push(H)}function Y(H){U.push(H)}Cn(d,{args:L,name:C[ds],store:F,after:T,onError:Y});let J;try{J=M.apply(this&&this.$id===e?this:F,L)}catch(H){throw Cn(U,H),H}return J instanceof Promise?J.then(H=>(Cn(b,H),H)).catch(H=>(Cn(U,H),Promise.reject(H))):(Cn(b,J),J)};return C[$l]=!0,C[ds]=_,C},E={_p:r,$id:e,$onAction:Ml.bind(null,d),$patch:w,$reset:O,$subscribe(M,_={}){const C=Ml(f,M,_.detached,()=>L()),L=i.run(()=>be(()=>r.state.value[e],b=>{(_.flush==="sync"?u:c)&&M({storeId:e,type:ur.direct,events:p},b)},Nt({},a,_)));return C},$dispose:v},F=yn(E);r._s.set(e,F);const D=(r._a&&r._a.runWithContext||om)(()=>r._e.run(()=>(i=nu()).run(()=>t({action:y}))));for(const M in D){const _=D[M];if(Ae(_)&&!lm(_)||Gt(_))s||(g&&im(_)&&(Ae(_)?_.value=g[M]:Ws(_,g[M])),r.state.value[e][M]=_);else if(typeof _=="function"){const C=y(_,M);D[M]=C,l.actions[M]=_}}return Nt(F,D),Nt(le(F),D),Object.defineProperty(F,"$state",{get:()=>r.state.value[e],set:M=>{w(_=>{Nt(_,M)})}}),r._p.forEach(M=>{Nt(F,i.run(()=>M({store:F,app:r._a,pinia:r,options:l})))}),g&&s&&n.hydrate&&n.hydrate(F.$state,g),c=!0,u=!0,F}/*! #__NO_SIDE_EFFECTS__ */function um(e,t,n){let r,o;const s=typeof t=="function";typeof e=="string"?(r=e,o=s?n:t):(o=e,r=e.id);function i(l,a){const c=dh();return l=l||(c?et(Tc,null):null),l&&Vo(l),l=Ac,l._s.has(r)||(s?Pc(r,t,o,l):am(r,o,l)),l._s.get(r)}return i.$id=r,i}const cm=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,fm=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,dm=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function hm(e,t){if(e==="__proto__"||e==="constructor"&&t&&typeof t=="object"&&"prototype"in t){pm(e);return}return t}function pm(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}function mm(e,t={}){if(typeof e!="string")return e;const n=e.trim();if(e[0]==='"'&&e.endsWith('"')&&!e.includes("\\"))return n.slice(1,-1);if(n.length<=9){const r=n.toLowerCase();if(r==="true")return!0;if(r==="false")return!1;if(r==="undefined")return;if(r==="null")return null;if(r==="nan")return Number.NaN;if(r==="infinity")return Number.POSITIVE_INFINITY;if(r==="-infinity")return Number.NEGATIVE_INFINITY}if(!dm.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(cm.test(e)||fm.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,hm)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}function gm(e,t){if(e==null)return;let n=e;for(let r=0;r<t.length;r++){if(n==null||n[t[r]]==null)return;n=n[t[r]]}return n}function Pi(e,t,n){if(n.length===0)return t;const r=n[0];return n.length>1&&(t=Pi(typeof e!="object"||e===null||!Object.prototype.hasOwnProperty.call(e,r)?Number.isInteger(Number(n[1]))?[]:{}:e[r],t,Array.prototype.slice.call(n,1))),Number.isInteger(Number(r))&&Array.isArray(e)?e.slice()[r]:Object.assign({},e,{[r]:t})}function Oc(e,t){if(e==null||t.length===0)return e;if(t.length===1){if(e==null)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const n={};for(const r in e)n[r]=e[r];return delete n[t[0]],n}if(e[t[0]]==null){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const n={};for(const r in e)n[r]=e[r];return n}return Pi(e,Oc(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function Fc(e,t){return t.map(n=>n.split(".")).map(n=>[n,gm(e,n)]).filter(n=>n[1]!==void 0).reduce((n,r)=>Pi(n,r[1],r[0]),{})}function Bc(e,t){return t.map(n=>n.split(".")).reduce((n,r)=>Oc(n,r),e)}function ql(e,{storage:t,serializer:n,key:r,debug:o,pick:s,omit:i,beforeHydrate:l,afterHydrate:a},c,u=!0){try{u&&(l==null||l(c));const f=t.getItem(r);if(f){const d=n.deserialize(f),p=s?Fc(d,s):d,g=i?Bc(p,i):p;e.$patch(g)}u&&(a==null||a(c))}catch(f){o&&console.error("[pinia-plugin-persistedstate]",f)}}function Nl(e,{storage:t,serializer:n,key:r,debug:o,pick:s,omit:i}){try{const l=s?Fc(e,s):e,a=i?Bc(l,i):l,c=n.serialize(a);t.setItem(r,c)}catch(l){o&&console.error("[pinia-plugin-persistedstate]",l)}}function vm(e,t,n){const{pinia:r,store:o,options:{persist:s=n}}=e;if(!s)return;if(!(o.$id in r.state.value)){const a=r._s.get(o.$id.replace("__hot:",""));a&&Promise.resolve().then(()=>a.$persist());return}const l=(Array.isArray(s)?s:s===!0?[{}]:[s]).map(t);o.$hydrate=({runHooks:a=!0}={})=>{l.forEach(c=>{ql(o,c,e,a)})},o.$persist=()=>{l.forEach(a=>{Nl(o.$state,a)})},l.forEach(a=>{ql(o,a,e),o.$subscribe((c,u)=>Nl(u,a),{detached:!0})})}function ym(e={}){return function(t){var n;vm(t,r=>{var o,s,i,l,a,c,u;return{key:(e.key?e.key:f=>f)((o=r.key)!=null?o:t.store.$id),debug:(i=(s=r.debug)!=null?s:e.debug)!=null?i:!1,serializer:(a=(l=r.serializer)!=null?l:e.serializer)!=null?a:{serialize:f=>JSON.stringify(f),deserialize:f=>mm(f)},storage:(u=(c=r.storage)!=null?c:e.storage)!=null?u:window.localStorage,beforeHydrate:r.beforeHydrate,afterHydrate:r.afterHydrate,pick:r.pick,omit:r.omit}},(n=e.auto)!=null?n:!1)}}var bm=ym(),hs=()=>{const e=rm();return e.use(bm),e};/*!
  * vue-router v4.4.3
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Rn=typeof document!="undefined";function _m(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ve=Object.assign;function ps(e,t){const n={};for(const r in t){const o=t[r];n[r]=_t(o)?o.map(e):e(o)}return n}const cr=()=>{},_t=Array.isArray,Lc=/#/g,wm=/&/g,xm=/\//g,Em=/=/g,Cm=/\?/g,Dc=/\+/g,Sm=/%5B/g,km=/%5D/g,Ic=/%5E/g,Am=/%60/g,Mc=/%7B/g,Tm=/%7C/g,$c=/%7D/g,Rm=/%20/g;function Oi(e){return encodeURI(""+e).replace(Tm,"|").replace(Sm,"[").replace(km,"]")}function Pm(e){return Oi(e).replace(Mc,"{").replace($c,"}").replace(Ic,"^")}function Ks(e){return Oi(e).replace(Dc,"%2B").replace(Rm,"+").replace(Lc,"%23").replace(wm,"%26").replace(Am,"`").replace(Mc,"{").replace($c,"}").replace(Ic,"^")}function Om(e){return Ks(e).replace(Em,"%3D")}function Fm(e){return Oi(e).replace(Lc,"%23").replace(Cm,"%3F")}function Bm(e){return e==null?"":Fm(e).replace(xm,"%2F")}function wr(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Lm=/\/$/,Dm=e=>e.replace(Lm,"");function ms(e,t,n="/"){let r,o={},s="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),s=t.slice(a+1,l>-1?l:t.length),o=e(s)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=qm(r!=null?r:t,n),{fullPath:r+(s&&"?")+s+i,path:r,query:o,hash:wr(i)}}function Im(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Vl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Mm(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&qn(t.matched[r],n.matched[o])&&qc(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function qn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function qc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!$m(e[n],t[n]))return!1;return!0}function $m(e,t){return _t(e)?jl(e,t):_t(t)?jl(t,e):e===t}function jl(e,t){return _t(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function qm(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let s=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+r.slice(i).join("/")}const Mt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var xr;(function(e){e.pop="pop",e.push="push"})(xr||(xr={}));var fr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(fr||(fr={}));function Nm(e){if(!e)if(Rn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Dm(e)}const Vm=/^[^#]+#/;function jm(e,t){return e.replace(Vm,"#")+t}function Um(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const jo=()=>({left:window.scrollX,top:window.scrollY});function Hm(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Um(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ul(e,t){return(history.state?history.state.position-t:-1)+e}const Qs=new Map;function zm(e,t){Qs.set(e,t)}function Wm(e){const t=Qs.get(e);return Qs.delete(e),t}let Km=()=>location.protocol+"//"+location.host;function Nc(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let l=o.includes(e.slice(s))?e.slice(s).length:1,a=o.slice(l);return a[0]!=="/"&&(a="/"+a),Vl(a,"")}return Vl(n,e)+r+o}function Qm(e,t,n,r){let o=[],s=[],i=null;const l=({state:d})=>{const p=Nc(e,location),g=n.value,x=t.value;let w=0;if(d){if(n.value=p,t.value=d,i&&i===g){i=null;return}w=x?d.position-x.position:0}else r(p);o.forEach(O=>{O(n.value,g,{delta:w,type:xr.pop,direction:w?w>0?fr.forward:fr.back:fr.unknown})})};function a(){i=n.value}function c(d){o.push(d);const p=()=>{const g=o.indexOf(d);g>-1&&o.splice(g,1)};return s.push(p),p}function u(){const{history:d}=window;!d.state||d.replaceState(ve({},d.state,{scroll:jo()}),"")}function f(){for(const d of s)d();s=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:a,listen:c,destroy:f}}function Hl(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?jo():null}}function Gm(e){const{history:t,location:n}=window,r={value:Nc(e,n)},o={value:t.state};o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(a,c,u){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:Km()+e+a;try{t[u?"replaceState":"pushState"](c,"",d),o.value=c}catch(p){console.error(p),n[u?"replace":"assign"](d)}}function i(a,c){const u=ve({},t.state,Hl(o.value.back,a,o.value.forward,!0),c,{position:o.value.position});s(a,u,!0),r.value=a}function l(a,c){const u=ve({},o.value,t.state,{forward:a,scroll:jo()});s(u.current,u,!0);const f=ve({},Hl(r.value,a,null),{position:u.position+1},c);s(a,f,!1),r.value=a}return{location:r,state:o,push:l,replace:i}}function Jm(e){e=Nm(e);const t=Gm(e),n=Qm(e,t.state,t.location,t.replace);function r(s,i=!0){i||n.pauseListeners(),history.go(s)}const o=ve({location:"",base:e,go:r,createHref:jm.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Xm(e){return typeof e=="string"||e&&typeof e=="object"}function Vc(e){return typeof e=="string"||typeof e=="symbol"}const jc=Symbol("");var zl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(zl||(zl={}));function Nn(e,t){return ve(new Error,{type:e,[jc]:!0},t)}function Tt(e,t){return e instanceof Error&&jc in e&&(t==null||!!(e.type&t))}const Wl="[^/]+?",Ym={sensitive:!1,strict:!1,start:!0,end:!0},Zm=/[.+*?^${}()[\]/\\]/g;function eg(e,t){const n=ve({},Ym,t),r=[];let o=n.start?"^":"";const s=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(o+="/");for(let f=0;f<c.length;f++){const d=c[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(o+="/"),o+=d.value.replace(Zm,"\\$&"),p+=40;else if(d.type===1){const{value:g,repeatable:x,optional:w,regexp:O}=d;s.push({name:g,repeatable:x,optional:w});const v=O||Wl;if(v!==Wl){p+=10;try{new RegExp(`(${v})`)}catch(E){throw new Error(`Invalid custom RegExp for param "${g}" (${v}): `+E.message)}}let y=x?`((?:${v})(?:/(?:${v}))*)`:`(${v})`;f||(y=w&&c.length<2?`(?:/${y})`:"/"+y),w&&(y+="?"),o+=y,p+=20,w&&(p+=-8),x&&(p+=-20),v===".*"&&(p+=-50)}u.push(p)}r.push(u)}if(n.strict&&n.end){const c=r.length-1;r[c][r[c].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(c){const u=c.match(i),f={};if(!u)return null;for(let d=1;d<u.length;d++){const p=u[d]||"",g=s[d-1];f[g.name]=p&&g.repeatable?p.split("/"):p}return f}function a(c){let u="",f=!1;for(const d of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const p of d)if(p.type===0)u+=p.value;else if(p.type===1){const{value:g,repeatable:x,optional:w}=p,O=g in c?c[g]:"";if(_t(O)&&!x)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const v=_t(O)?O.join("/"):O;if(!v)if(w)d.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);u+=v}}return u||"/"}return{re:i,score:r,keys:s,parse:l,stringify:a}}function tg(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Uc(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const s=tg(r[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-r.length)===1){if(Kl(r))return 1;if(Kl(o))return-1}return o.length-r.length}function Kl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ng={type:0,value:""},rg=/[a-zA-Z0-9_]/;function og(e){if(!e)return[[]];if(e==="/")return[[ng]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${c}": ${p}`)}let n=0,r=n;const o=[];let s;function i(){s&&o.push(s),s=[]}let l=0,a,c="",u="";function f(){!c||(n===0?s.push({type:0,value:c}):n===1||n===2||n===3?(s.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(c&&f(),i()):a===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:a==="("?n=2:rg.test(a)?d():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),f(),i(),o}function sg(e,t,n){const r=eg(og(e.path),n),o=ve(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function ig(e,t){const n=[],r=new Map;t=Jl({strict:!1,end:!0,sensitive:!1},t);function o(f){return r.get(f)}function s(f,d,p){const g=!p,x=lg(f);x.aliasOf=p&&p.record;const w=Jl(t,f),O=[x];if("alias"in f){const E=typeof f.alias=="string"?[f.alias]:f.alias;for(const F of E)O.push(ve({},x,{components:p?p.record.components:x.components,path:F,aliasOf:p?p.record:x}))}let v,y;for(const E of O){const{path:F}=E;if(d&&F[0]!=="/"){const I=d.record.path,D=I[I.length-1]==="/"?"":"/";E.path=d.record.path+(F&&D+F)}if(v=sg(E,d,w),p?p.alias.push(v):(y=y||v,y!==v&&y.alias.push(v),g&&f.name&&!Gl(v)&&i(f.name)),Hc(v)&&a(v),x.children){const I=x.children;for(let D=0;D<I.length;D++)s(I[D],v,p&&p.children[D])}p=p||v}return y?()=>{i(y)}:cr}function i(f){if(Vc(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const d=cg(f,n);n.splice(d,0,f),f.record.name&&!Gl(f)&&r.set(f.record.name,f)}function c(f,d){let p,g={},x,w;if("name"in f&&f.name){if(p=r.get(f.name),!p)throw Nn(1,{location:f});w=p.record.name,g=ve(Ql(d.params,p.keys.filter(y=>!y.optional).concat(p.parent?p.parent.keys.filter(y=>y.optional):[]).map(y=>y.name)),f.params&&Ql(f.params,p.keys.map(y=>y.name))),x=p.stringify(g)}else if(f.path!=null)x=f.path,p=n.find(y=>y.re.test(x)),p&&(g=p.parse(x),w=p.record.name);else{if(p=d.name?r.get(d.name):n.find(y=>y.re.test(d.path)),!p)throw Nn(1,{location:f,currentLocation:d});w=p.record.name,g=ve({},d.params,f.params),x=p.stringify(g)}const O=[];let v=p;for(;v;)O.unshift(v.record),v=v.parent;return{name:w,path:x,params:g,matched:O,meta:ug(O)}}e.forEach(f=>s(f));function u(){n.length=0,r.clear()}return{addRoute:s,resolve:c,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:o}}function Ql(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function lg(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:ag(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function ag(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Gl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ug(e){return e.reduce((t,n)=>ve(t,n.meta),{})}function Jl(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function cg(e,t){let n=0,r=t.length;for(;n!==r;){const s=n+r>>1;Uc(e,t[s])<0?r=s:n=s+1}const o=fg(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function fg(e){let t=e;for(;t=t.parent;)if(Hc(t)&&Uc(e,t)===0)return t}function Hc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function dg(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const s=r[o].replace(Dc," "),i=s.indexOf("="),l=wr(i<0?s:s.slice(0,i)),a=i<0?null:wr(s.slice(i+1));if(l in t){let c=t[l];_t(c)||(c=t[l]=[c]),c.push(a)}else t[l]=a}return t}function Xl(e){let t="";for(let n in e){const r=e[n];if(n=Om(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(_t(r)?r.map(s=>s&&Ks(s)):[r&&Ks(r)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function hg(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=_t(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const pg=Symbol(""),Yl=Symbol(""),Uo=Symbol(""),Fi=Symbol(""),Gs=Symbol("");function Qn(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function zt(e,t,n,r,o,s=i=>i()){const i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((l,a)=>{const c=d=>{d===!1?a(Nn(4,{from:n,to:t})):d instanceof Error?a(d):Xm(d)?a(Nn(2,{from:t,to:d})):(i&&r.enterCallbacks[o]===i&&typeof d=="function"&&i.push(d),l())},u=s(()=>e.call(r&&r.instances[o],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(d=>a(d))})}function gs(e,t,n,r,o=s=>s()){const s=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(mg(a)){const u=(a.__vccOpts||a)[t];u&&s.push(zt(u,n,r,i,l,o))}else{let c=a();s.push(()=>c.then(u=>{if(!u)return Promise.reject(new Error(`Couldn't resolve component "${l}" at "${i.path}"`));const f=_m(u)?u.default:u;i.components[l]=f;const p=(f.__vccOpts||f)[t];return p&&zt(p,n,r,i,l,o)()}))}}return s}function mg(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Zl(e){const t=et(Uo),n=et(Fi),r=R(()=>{const a=fn(e.to);return t.resolve(a)}),o=R(()=>{const{matched:a}=r.value,{length:c}=a,u=a[c-1],f=n.matched;if(!u||!f.length)return-1;const d=f.findIndex(qn.bind(null,u));if(d>-1)return d;const p=ea(a[c-2]);return c>1&&ea(u)===p&&f[f.length-1].path!==p?f.findIndex(qn.bind(null,a[c-2])):d}),s=R(()=>o.value>-1&&bg(n.params,r.value.params)),i=R(()=>o.value>-1&&o.value===n.matched.length-1&&qc(n.params,r.value.params));function l(a={}){return yg(a)?t[fn(e.replace)?"replace":"push"](fn(e.to)).catch(cr):Promise.resolve()}return{route:r,href:R(()=>r.value.href),isActive:s,isExactActive:i,navigate:l}}const gg=Fo({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Zl,setup(e,{slots:t}){const n=yn(Zl(e)),{options:r}=et(Uo),o=R(()=>({[ta(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[ta(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&t.default(n);return e.custom?s:P("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),vg=gg;function yg(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function bg(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!_t(o)||o.length!==r.length||r.some((s,i)=>s!==o[i]))return!1}return!0}function ea(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ta=(e,t,n)=>e!=null?e:t!=null?t:n,_g=Fo({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=et(Gs),o=R(()=>e.route||r.value),s=et(Yl,0),i=R(()=>{let c=fn(s);const{matched:u}=o.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),l=R(()=>o.value.matched[i.value]);Kr(Yl,R(()=>i.value+1)),Kr(pg,l),Kr(Gs,o);const a=de();return be(()=>[a.value,l.value,e.name],([c,u,f],[d,p,g])=>{u&&(u.instances[f]=c,p&&p!==u&&c&&c===d&&(u.leaveGuards.size||(u.leaveGuards=p.leaveGuards),u.updateGuards.size||(u.updateGuards=p.updateGuards))),c&&u&&(!p||!qn(u,p)||!d)&&(u.enterCallbacks[f]||[]).forEach(x=>x(c))},{flush:"post"}),()=>{const c=o.value,u=e.name,f=l.value,d=f&&f.components[u];if(!d)return na(n.default,{Component:d,route:c});const p=f.props[u],g=p?p===!0?c.params:typeof p=="function"?p(c):p:null,w=P(d,ve({},g,t,{onVnodeUnmounted:O=>{O.component.isUnmounted&&(f.instances[u]=null)},ref:a}));return na(n.default,{Component:w,route:c})||w}}});function na(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const wg=_g;function xg(e){const t=ig(e.routes,e),n=e.parseQuery||dg,r=e.stringifyQuery||Xl,o=e.history,s=Qn(),i=Qn(),l=Qn(),a=Pd(Mt);let c=Mt;Rn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=ps.bind(null,k=>""+k),f=ps.bind(null,Bm),d=ps.bind(null,wr);function p(k,Q){let K,Z;return Vc(k)?(K=t.getRecordMatcher(k),Z=Q):Z=k,t.addRoute(Z,K)}function g(k){const Q=t.getRecordMatcher(k);Q&&t.removeRoute(Q)}function x(){return t.getRoutes().map(k=>k.record)}function w(k){return!!t.getRecordMatcher(k)}function O(k,Q){if(Q=ve({},Q||a.value),typeof k=="string"){const m=ms(n,k,Q.path),S=t.resolve({path:m.path},Q),$=o.createHref(m.fullPath);return ve(m,S,{params:d(S.params),hash:wr(m.hash),redirectedFrom:void 0,href:$})}let K;if(k.path!=null)K=ve({},k,{path:ms(n,k.path,Q.path).path});else{const m=ve({},k.params);for(const S in m)m[S]==null&&delete m[S];K=ve({},k,{params:f(m)}),Q.params=f(Q.params)}const Z=t.resolve(K,Q),pe=k.hash||"";Z.params=u(d(Z.params));const Ee=Im(r,ve({},k,{hash:Pm(pe),path:Z.path})),h=o.createHref(Ee);return ve({fullPath:Ee,hash:pe,query:r===Xl?hg(k.query):k.query||{}},Z,{redirectedFrom:void 0,href:h})}function v(k){return typeof k=="string"?ms(n,k,a.value.path):ve({},k)}function y(k,Q){if(c!==k)return Nn(8,{from:Q,to:k})}function E(k){return D(k)}function F(k){return E(ve(v(k),{replace:!0}))}function I(k){const Q=k.matched[k.matched.length-1];if(Q&&Q.redirect){const{redirect:K}=Q;let Z=typeof K=="function"?K(k):K;return typeof Z=="string"&&(Z=Z.includes("?")||Z.includes("#")?Z=v(Z):{path:Z},Z.params={}),ve({query:k.query,hash:k.hash,params:Z.path!=null?{}:k.params},Z)}}function D(k,Q){const K=c=O(k),Z=a.value,pe=k.state,Ee=k.force,h=k.replace===!0,m=I(K);if(m)return D(ve(v(m),{state:typeof m=="object"?ve({},pe,m.state):pe,force:Ee,replace:h}),Q||K);const S=K;S.redirectedFrom=Q;let $;return!Ee&&Mm(r,Z,K)&&($=Nn(16,{to:S,from:Z}),ce(Z,Z,!0,!1)),($?Promise.resolve($):C(S,Z)).catch(B=>Tt(B)?Tt(B,2)?B:ue(B):N(B,S,Z)).then(B=>{if(B){if(Tt(B,2))return D(ve({replace:h},v(B.to),{state:typeof B.to=="object"?ve({},pe,B.to.state):pe,force:Ee}),Q||S)}else B=b(S,Z,!0,h,pe);return L(S,Z,B),B})}function M(k,Q){const K=y(k,Q);return K?Promise.reject(K):Promise.resolve()}function _(k){const Q=Re.values().next().value;return Q&&typeof Q.runWithContext=="function"?Q.runWithContext(k):k()}function C(k,Q){let K;const[Z,pe,Ee]=Eg(k,Q);K=gs(Z.reverse(),"beforeRouteLeave",k,Q);for(const m of Z)m.leaveGuards.forEach(S=>{K.push(zt(S,k,Q))});const h=M.bind(null,k,Q);return K.push(h),ee(K).then(()=>{K=[];for(const m of s.list())K.push(zt(m,k,Q));return K.push(h),ee(K)}).then(()=>{K=gs(pe,"beforeRouteUpdate",k,Q);for(const m of pe)m.updateGuards.forEach(S=>{K.push(zt(S,k,Q))});return K.push(h),ee(K)}).then(()=>{K=[];for(const m of Ee)if(m.beforeEnter)if(_t(m.beforeEnter))for(const S of m.beforeEnter)K.push(zt(S,k,Q));else K.push(zt(m.beforeEnter,k,Q));return K.push(h),ee(K)}).then(()=>(k.matched.forEach(m=>m.enterCallbacks={}),K=gs(Ee,"beforeRouteEnter",k,Q,_),K.push(h),ee(K))).then(()=>{K=[];for(const m of i.list())K.push(zt(m,k,Q));return K.push(h),ee(K)}).catch(m=>Tt(m,8)?m:Promise.reject(m))}function L(k,Q,K){l.list().forEach(Z=>_(()=>Z(k,Q,K)))}function b(k,Q,K,Z,pe){const Ee=y(k,Q);if(Ee)return Ee;const h=Q===Mt,m=Rn?history.state:{};K&&(Z||h?o.replace(k.fullPath,ve({scroll:h&&m&&m.scroll},pe)):o.push(k.fullPath,pe)),a.value=k,ce(k,Q,K,h),ue()}let U;function T(){U||(U=o.listen((k,Q,K)=>{if(!Le.listening)return;const Z=O(k),pe=I(Z);if(pe){D(ve(pe,{replace:!0}),Z).catch(cr);return}c=Z;const Ee=a.value;Rn&&zm(Ul(Ee.fullPath,K.delta),jo()),C(Z,Ee).catch(h=>Tt(h,12)?h:Tt(h,2)?(D(h.to,Z).then(m=>{Tt(m,20)&&!K.delta&&K.type===xr.pop&&o.go(-1,!1)}).catch(cr),Promise.reject()):(K.delta&&o.go(-K.delta,!1),N(h,Z,Ee))).then(h=>{h=h||b(Z,Ee,!1),h&&(K.delta&&!Tt(h,8)?o.go(-K.delta,!1):K.type===xr.pop&&Tt(h,20)&&o.go(-1,!1)),L(Z,Ee,h)}).catch(cr)}))}let Y=Qn(),J=Qn(),H;function N(k,Q,K){ue(k);const Z=J.list();return Z.length?Z.forEach(pe=>pe(k,Q,K)):console.error(k),Promise.reject(k)}function ne(){return H&&a.value!==Mt?Promise.resolve():new Promise((k,Q)=>{Y.add([k,Q])})}function ue(k){return H||(H=!k,T(),Y.list().forEach(([Q,K])=>k?K(k):Q()),Y.reset()),k}function ce(k,Q,K,Z){const{scrollBehavior:pe}=e;if(!Rn||!pe)return Promise.resolve();const Ee=!K&&Wm(Ul(k.fullPath,0))||(Z||!K)&&history.state&&history.state.scroll||null;return Qe().then(()=>pe(k,Q,Ee)).then(h=>h&&Hm(h)).catch(h=>N(h,k,Q))}const q=k=>o.go(k);let he;const Re=new Set,Le={currentRoute:a,listening:!0,addRoute:p,removeRoute:g,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:x,resolve:O,options:e,push:E,replace:F,go:q,back:()=>q(-1),forward:()=>q(1),beforeEach:s.add,beforeResolve:i.add,afterEach:l.add,onError:J.add,isReady:ne,install(k){const Q=this;k.component("RouterLink",vg),k.component("RouterView",wg),k.config.globalProperties.$router=Q,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>fn(a)}),Rn&&!he&&a.value===Mt&&(he=!0,E(o.location).catch(pe=>{}));const K={};for(const pe in Mt)Object.defineProperty(K,pe,{get:()=>a.value[pe],enumerable:!0});k.provide(Uo,Q),k.provide(Fi,yu(K)),k.provide(Gs,a);const Z=k.unmount;Re.add(k),k.unmount=function(){Re.delete(k),Re.size<1&&(c=Mt,U&&U(),U=null,a.value=Mt,he=!1,H=!1),Z()}}};function ee(k){return k.reduce((Q,K)=>Q.then(()=>_(K)),Promise.resolve())}return Le}function Eg(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const l=t.matched[i];l&&(e.matched.find(c=>qn(c,l))?r.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(c=>qn(c,a))||o.push(a))}return[n,r,o]}function Cg(){return et(Uo)}function C0(e){return et(Fi)}const Js={xs:18,sm:24,md:32,lg:38,xl:46},Pr={size:String};function Or(e,t=Js){return R(()=>e.size!==void 0?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null)}function pt(e,t){return e!==void 0&&e()||t}function S0(e,t){if(e!==void 0){const n=e();if(n!=null)return n.slice()}return t}function ln(e,t){return e!==void 0?t.concat(e()):t}function Sg(e,t){return e===void 0?t:t!==void 0?t.concat(e()):e()}function k0(e,t,n,r,o,s){t.key=r+o;const i=P(e,t,n);return o===!0?Au(i,s()):i}const ra="0 0 24 24",oa=e=>e,vs=e=>`ionicons ${e}`,zc={"mdi-":e=>`mdi ${e}`,"icon-":oa,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":vs,"ion-ios":vs,"ion-logo":vs,"iconfont ":oa,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},Wc={o_:"-outlined",r_:"-round",s_:"-sharp"},Kc={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},kg=new RegExp("^("+Object.keys(zc).join("|")+")"),Ag=new RegExp("^("+Object.keys(Wc).join("|")+")"),sa=new RegExp("^("+Object.keys(Kc).join("|")+")"),Tg=/^[Mm]\s?[-+]?\.?\d/,Rg=/^img:/,Pg=/^svguse:/,Og=/^ion-/,Fg=/^(fa-(sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /;var Bt=Me({name:"QIcon",props:{...Pr,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=Se(),r=Or(e),o=R(()=>"q-icon"+(e.left===!0?" on-left":"")+(e.right===!0?" on-right":"")+(e.color!==void 0?` text-${e.color}`:"")),s=R(()=>{let i,l=e.name;if(l==="none"||!l)return{none:!0};if(n.iconMapFn!==null){const u=n.iconMapFn(l);if(u!==void 0)if(u.icon!==void 0){if(l=u.icon,l==="none"||!l)return{none:!0}}else return{cls:u.cls,content:u.content!==void 0?u.content:" "}}if(Tg.test(l)===!0){const[u,f=ra]=l.split("|");return{svg:!0,viewBox:f,nodes:u.split("&&").map(d=>{const[p,g,x]=d.split("@@");return P("path",{style:g,d:p,transform:x})})}}if(Rg.test(l)===!0)return{img:!0,src:l.substring(4)};if(Pg.test(l)===!0){const[u,f=ra]=l.split("|");return{svguse:!0,src:u.substring(7),viewBox:f}}let a=" ";const c=l.match(kg);if(c!==null)i=zc[c[1]](l);else if(Fg.test(l)===!0)i=l;else if(Og.test(l)===!0)i=`ionicons ion-${n.platform.is.ios===!0?"ios":"md"}${l.substring(3)}`;else if(sa.test(l)===!0){i="notranslate material-symbols";const u=l.match(sa);u!==null&&(l=l.substring(6),i+=Kc[u[1]]),a=l}else{i="notranslate material-icons";const u=l.match(Ag);u!==null&&(l=l.substring(2),i+=Wc[u[1]]),a=l}return{cls:i,content:a}});return()=>{const i={class:o.value,style:r.value,"aria-hidden":"true",role:"presentation"};return s.value.none===!0?P(e.tag,i,pt(t.default)):s.value.img===!0?P(e.tag,i,ln(t.default,[P("img",{src:s.value.src})])):s.value.svg===!0?P(e.tag,i,ln(t.default,[P("svg",{viewBox:s.value.viewBox||"0 0 24 24"},s.value.nodes)])):s.value.svguse===!0?P(e.tag,i,ln(t.default,[P("svg",{viewBox:s.value.viewBox},[P("use",{"xlink:href":s.value.src})])])):(s.value.cls!==void 0&&(i.class+=" "+s.value.cls),P(e.tag,i,ln(t.default,[s.value.content])))}}}),Bg=Me({name:"QAvatar",props:{...Pr,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=Or(e),r=R(()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(e.square===!0?" q-avatar--square":e.rounded===!0?" rounded-borders":"")),o=R(()=>e.fontSize?{fontSize:e.fontSize}:null);return()=>{const s=e.icon!==void 0?[P(Bt,{name:e.icon})]:void 0;return P("div",{class:r.value,style:n.value},[P("div",{class:"q-avatar__content row flex-center overflow-hidden",style:o.value},Sg(t.default,s))])}}});const Lg={size:{type:[String,Number],default:"1em"},color:String};function Dg(e){return{cSize:R(()=>e.size in Js?`${Js[e.size]}px`:e.size),classes:R(()=>"q-spinner"+(e.color?` text-${e.color}`:""))}}var Er=Me({name:"QSpinner",props:{...Lg,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=Dg(e);return()=>P("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[P("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}});function Xs(e,t){const n=e.style;for(const r in t)n[r]=t[r]}function Ig(e){if(e==null)return;if(typeof e=="string")try{return document.querySelector(e)||void 0}catch{return}const t=fn(e);if(t)return t.$el||t}function Mg(e,t){if(e==null||e.contains(t)===!0)return!0;for(let n=e.nextElementSibling;n!==null;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}function $g(e,t=250){let n=!1,r;return function(){return n===!1&&(n=!0,setTimeout(()=>{n=!1},t),r=e.apply(this,arguments)),r}}function ia(e,t,n,r){n.modifiers.stop===!0&&bo(e);const o=n.modifiers.color;let s=n.modifiers.center;s=s===!0||r===!0;const i=document.createElement("span"),l=document.createElement("span"),a=Fp(e),{left:c,top:u,width:f,height:d}=t.getBoundingClientRect(),p=Math.sqrt(f*f+d*d),g=p/2,x=`${(f-p)/2}px`,w=s?x:`${a.left-c-g}px`,O=`${(d-p)/2}px`,v=s?O:`${a.top-u-g}px`;l.className="q-ripple__inner",Xs(l,{height:`${p}px`,width:`${p}px`,transform:`translate3d(${w},${v},0) scale3d(.2,.2,1)`,opacity:0}),i.className=`q-ripple${o?" text-"+o:""}`,i.setAttribute("dir","ltr"),i.appendChild(l),t.appendChild(i);const y=()=>{i.remove(),clearTimeout(E)};n.abort.push(y);let E=setTimeout(()=>{l.classList.add("q-ripple__inner--enter"),l.style.transform=`translate3d(${x},${O},0) scale3d(1,1,1)`,l.style.opacity=.2,E=setTimeout(()=>{l.classList.remove("q-ripple__inner--enter"),l.classList.add("q-ripple__inner--leave"),l.style.opacity=0,E=setTimeout(()=>{i.remove(),n.abort.splice(n.abort.indexOf(y),1)},275)},250)},50)}function la(e,{modifiers:t,value:n,arg:r}){const o=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:o.early===!0,stop:o.stop===!0,center:o.center===!0,color:o.color||r,keyCodes:[].concat(o.keyCodes||13)}}var qg=Op({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(n.ripple===!1)return;const r={cfg:n,enabled:t.value!==!1,modifiers:{},abort:[],start(o){r.enabled===!0&&o.qSkipRipple!==!0&&o.type===(r.modifiers.early===!0?"pointerdown":"click")&&ia(o,e,r,o.qKeyEvent===!0)},keystart:$g(o=>{r.enabled===!0&&o.qSkipRipple!==!0&&_r(o,r.modifiers.keyCodes)===!0&&o.type===`key${r.modifiers.early===!0?"down":"up"}`&&ia(o,e,r,!0)},300)};la(r,t),e.__qripple=r,Lp(r,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n!==void 0&&(n.enabled=t.value!==!1,n.enabled===!0&&Object(t.value)===t.value&&la(n,t))}},beforeUnmount(e){const t=e.__qripple;t!==void 0&&(t.abort.forEach(n=>{n()}),Dp(t,"main"),delete e._qripple)}});const Qc={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},Ng=Object.keys(Qc),Gc={align:{type:String,validator:e=>Ng.includes(e)}};function Jc(e){return R(()=>{const t=e.align===void 0?e.vertical===!0?"stretch":"left":e.align;return`${e.vertical===!0?"items":"justify"}-${Qc[t]}`})}function Zr(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;for(;Object(t)===t;){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function Xc(e,t){typeof t.type=="symbol"?Array.isArray(t.children)===!0&&t.children.forEach(n=>{Xc(e,n)}):e.add(t)}function A0(e){const t=new Set;return e.forEach(n=>{Xc(t,n)}),Array.from(t)}function Yc(e){return e.appContext.config.globalProperties.$router!==void 0}function Zc(e){return e.isUnmounted===!0||e.isDeactivated===!0}function aa(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function ua(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Vg(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(Array.isArray(o)===!1||o.length!==r.length||r.some((s,i)=>s!==o[i]))return!1}return!0}function ca(e,t){return Array.isArray(t)===!0?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function jg(e,t){return Array.isArray(e)===!0?ca(e,t):Array.isArray(t)===!0?ca(t,e):e===t}function Ug(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(jg(e[n],t[n])===!1)return!1;return!0}const ef={to:[String,Object],replace:Boolean,href:String,target:String,disable:Boolean},T0={...ef,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"}};function Hg({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=Se(),{props:r,proxy:o,emit:s}=n,i=Yc(n),l=R(()=>r.disable!==!0&&r.href!==void 0),a=R(t===!0?()=>i===!0&&r.disable!==!0&&l.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!=="":()=>i===!0&&l.value!==!0&&r.to!==void 0&&r.to!==null&&r.to!==""),c=R(()=>a.value===!0?v(r.to):null),u=R(()=>c.value!==null),f=R(()=>l.value===!0||u.value===!0),d=R(()=>r.type==="a"||f.value===!0?"a":r.tag||e||"div"),p=R(()=>l.value===!0?{href:r.href,target:r.target}:u.value===!0?{href:c.value.href,target:r.target}:{}),g=R(()=>{if(u.value===!1)return-1;const{matched:F}=c.value,{length:I}=F,D=F[I-1];if(D===void 0)return-1;const M=o.$route.matched;if(M.length===0)return-1;const _=M.findIndex(ua.bind(null,D));if(_!==-1)return _;const C=aa(F[I-2]);return I>1&&aa(D)===C&&M[M.length-1].path!==C?M.findIndex(ua.bind(null,F[I-2])):_}),x=R(()=>u.value===!0&&g.value!==-1&&Vg(o.$route.params,c.value.params)),w=R(()=>x.value===!0&&g.value===o.$route.matched.length-1&&Ug(o.$route.params,c.value.params)),O=R(()=>u.value===!0?w.value===!0?` ${r.exactActiveClass} ${r.activeClass}`:r.exact===!0?"":x.value===!0?` ${r.activeClass}`:"":"");function v(F){try{return o.$router.resolve(F)}catch{}return null}function y(F,{returnRouterError:I,to:D=r.to,replace:M=r.replace}={}){if(r.disable===!0)return F.preventDefault(),Promise.resolve(!1);if(F.metaKey||F.altKey||F.ctrlKey||F.shiftKey||F.button!==void 0&&F.button!==0||r.target==="_blank")return Promise.resolve(!1);F.preventDefault();const _=o.$router[M===!0?"replace":"push"](D);return I===!0?_:_.then(()=>{}).catch(()=>{})}function E(F){if(u.value===!0){const I=D=>y(F,D);s("click",F,I),F.defaultPrevented!==!0&&I()}else s("click",F)}return{hasRouterLink:u,hasHrefLink:l,hasLink:f,linkTag:d,resolvedLink:c,linkIsActive:x,linkIsExactActive:w,linkClass:O,linkAttrs:p,getLink:v,navigateToRouterLink:y,navigateOnClick:E}}const fa={none:0,xs:4,sm:8,md:16,lg:24,xl:32},zg={xs:8,sm:10,md:14,lg:20,xl:24},Wg=["button","submit","reset"],Kg=/[^\s]\/[^\s]/,Qg=["flat","outline","push","unelevated"];function Gg(e,t){return e.flat===!0?"flat":e.outline===!0?"outline":e.push===!0?"push":e.unelevated===!0?"unelevated":t}const Jg={...Pr,...ef,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...Qg.reduce((e,t)=>(e[t]=Boolean)&&e,{}),square:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...Gc.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean},Xg={...Jg,round:Boolean};function Yg(e){const t=Or(e,zg),n=Jc(e),{hasRouterLink:r,hasLink:o,linkTag:s,linkAttrs:i,navigateOnClick:l}=Hg({fallbackTag:"button"}),a=R(()=>{const w=e.fab===!1&&e.fabMini===!1?t.value:{};return e.padding!==void 0?Object.assign({},w,{padding:e.padding.split(/\s+/).map(O=>O in fa?fa[O]+"px":O).join(" "),minWidth:"0",minHeight:"0"}):w}),c=R(()=>e.rounded===!0||e.fab===!0||e.fabMini===!0),u=R(()=>e.disable!==!0&&e.loading!==!0),f=R(()=>u.value===!0?e.tabindex||0:-1),d=R(()=>Gg(e,"standard")),p=R(()=>{const w={tabindex:f.value};return o.value===!0?Object.assign(w,i.value):Wg.includes(e.type)===!0&&(w.type=e.type),s.value==="a"?(e.disable===!0?w["aria-disabled"]="true":w.href===void 0&&(w.role="button"),r.value!==!0&&Kg.test(e.type)===!0&&(w.type=e.type)):e.disable===!0&&(w.disabled="",w["aria-disabled"]="true"),e.loading===!0&&e.percentage!==void 0&&Object.assign(w,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),w}),g=R(()=>{let w;e.color!==void 0?e.flat===!0||e.outline===!0?w=`text-${e.textColor||e.color}`:w=`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(w=`text-${e.textColor}`);const O=e.round===!0?"round":`rectangle${c.value===!0?" q-btn--rounded":e.square===!0?" q-btn--square":""}`;return`q-btn--${d.value} q-btn--${O}`+(w!==void 0?" "+w:"")+(u.value===!0?" q-btn--actionable q-focusable q-hoverable":e.disable===!0?" disabled":"")+(e.fab===!0?" q-btn--fab":e.fabMini===!0?" q-btn--fab-mini":"")+(e.noCaps===!0?" q-btn--no-uppercase":"")+(e.dense===!0?" q-btn--dense":"")+(e.stretch===!0?" no-border-radius self-stretch":"")+(e.glossy===!0?" glossy":"")+(e.square?" q-btn--square":"")}),x=R(()=>n.value+(e.stack===!0?" column":" row")+(e.noWrap===!0?" no-wrap text-no-wrap":"")+(e.loading===!0?" q-btn__content--hidden":""));return{classes:g,style:a,innerClasses:x,attributes:p,hasLink:o,linkTag:s,navigateOnClick:l,isActionable:u}}const{passiveCapture:ot}=Ke;let Sn=null,kn=null,An=null;var Ys=Me({name:"QBtn",props:{...Xg,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:r}=Se(),{classes:o,style:s,innerClasses:i,attributes:l,hasLink:a,linkTag:c,navigateOnClick:u,isActionable:f}=Yg(e),d=de(null),p=de(null);let g=null,x,w=null;const O=R(()=>e.label!==void 0&&e.label!==null&&e.label!==""),v=R(()=>e.disable===!0||e.ripple===!1?!1:{keyCodes:a.value===!0?[13,32]:[13],...e.ripple===!0?{}:e.ripple}),y=R(()=>({center:e.round})),E=R(()=>{const T=Math.max(0,Math.min(100,e.percentage));return T>0?{transition:"transform 0.6s",transform:`translateX(${T-100}%)`}:{}}),F=R(()=>{if(e.loading===!0)return{onMousedown:U,onTouchstart:U,onClick:U,onKeydown:U,onKeyup:U};if(f.value===!0){const T={onClick:D,onKeydown:M,onMousedown:C};if(r.$q.platform.has.touch===!0){const Y=e.onTouchstart!==void 0?"":"Passive";T[`onTouchstart${Y}`]=_}return T}return{onClick:it}}),I=R(()=>({ref:d,class:"q-btn q-btn-item non-selectable no-outline "+o.value,style:s.value,...l.value,...F.value}));function D(T){if(d.value!==null){if(T!==void 0){if(T.defaultPrevented===!0)return;const Y=document.activeElement;if(e.type==="submit"&&Y!==document.body&&d.value.contains(Y)===!1&&Y.contains(d.value)===!1){d.value.focus();const J=()=>{document.removeEventListener("keydown",it,!0),document.removeEventListener("keyup",J,ot),d.value!==null&&d.value.removeEventListener("blur",J,ot)};document.addEventListener("keydown",it,!0),document.addEventListener("keyup",J,ot),d.value.addEventListener("blur",J,ot)}}u(T)}}function M(T){d.value!==null&&(n("keydown",T),_r(T,[13,32])===!0&&kn!==d.value&&(kn!==null&&b(),T.defaultPrevented!==!0&&(d.value.focus(),kn=d.value,d.value.classList.add("q-btn--active"),document.addEventListener("keyup",L,!0),d.value.addEventListener("blur",L,ot)),it(T)))}function _(T){d.value!==null&&(n("touchstart",T),T.defaultPrevented!==!0&&(Sn!==d.value&&(Sn!==null&&b(),Sn=d.value,g=T.target,g.addEventListener("touchcancel",L,ot),g.addEventListener("touchend",L,ot)),x=!0,w!==null&&clearTimeout(w),w=setTimeout(()=>{w=null,x=!1},200)))}function C(T){d.value!==null&&(T.qSkipRipple=x===!0,n("mousedown",T),T.defaultPrevented!==!0&&An!==d.value&&(An!==null&&b(),An=d.value,d.value.classList.add("q-btn--active"),document.addEventListener("mouseup",L,ot)))}function L(T){if(d.value!==null&&!(T!==void 0&&T.type==="blur"&&document.activeElement===d.value)){if(T!==void 0&&T.type==="keyup"){if(kn===d.value&&_r(T,[13,32])===!0){const Y=new MouseEvent("click",T);Y.qKeyEvent=!0,T.defaultPrevented===!0&&Kt(Y),T.cancelBubble===!0&&bo(Y),d.value.dispatchEvent(Y),it(T),T.qKeyEvent=!0}n("keyup",T)}b()}}function b(T){const Y=p.value;T!==!0&&(Sn===d.value||An===d.value)&&Y!==null&&Y!==document.activeElement&&(Y.setAttribute("tabindex",-1),Y.focus()),Sn===d.value&&(g!==null&&(g.removeEventListener("touchcancel",L,ot),g.removeEventListener("touchend",L,ot)),Sn=g=null),An===d.value&&(document.removeEventListener("mouseup",L,ot),An=null),kn===d.value&&(document.removeEventListener("keyup",L,!0),d.value!==null&&d.value.removeEventListener("blur",L,ot),kn=null),d.value!==null&&d.value.classList.remove("q-btn--active")}function U(T){it(T),T.qSkipRipple=!0}return ct(()=>{b(!0)}),Object.assign(r,{click:T=>{f.value===!0&&D(T)}}),()=>{let T=[];e.icon!==void 0&&T.push(P(Bt,{name:e.icon,left:e.stack!==!0&&O.value===!0,role:"img"})),O.value===!0&&T.push(P("span",{class:"block"},[e.label])),T=ln(t.default,T),e.iconRight!==void 0&&e.round===!1&&T.push(P(Bt,{name:e.iconRight,right:e.stack!==!0&&O.value===!0,role:"img"}));const Y=[P("span",{class:"q-focus-helper",ref:p})];return e.loading===!0&&e.percentage!==void 0&&Y.push(P("span",{class:"q-btn__progress absolute-full overflow-hidden"+(e.darkPercentage===!0?" q-btn__progress--dark":"")},[P("span",{class:"q-btn__progress-indicator fit block",style:E.value})])),Y.push(P("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+i.value},T)),e.loading!==null&&Y.push(P(go,{name:"q-transition--fade"},()=>e.loading===!0?[P("span",{key:"loading",class:"absolute-full flex flex-center"},t.loading!==void 0?t.loading():[P(Er)])]:null)),Au(P(c.value,I.value,Y),[[qg,v.value,void 0,y.value]])}}});let Zg=1,ev=document.body;function Bi(e,t){const n=document.createElement("div");if(n.id=t!==void 0?`q-portal--${t}--${Zg++}`:e,wo.globalNodes!==void 0){const r=wo.globalNodes.class;r!==void 0&&(n.className=r)}return ev.appendChild(n),n}function tf(e){e.remove()}let tv=0;const eo={},to={},dt={},nf={},nv=/^\s*$/,rf=[],rv=[void 0,null,!0,!1,""],Li=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],ov=["top-left","top-right","bottom-left","bottom-right"],Pn={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function of(e,t,n){if(!e)return Gn("parameter required");let r;const o={textColor:"white"};if(e.ignoreDefaults!==!0&&Object.assign(o,eo),kt(e)===!1&&(o.type&&Object.assign(o,Pn[o.type]),e={message:e}),Object.assign(o,Pn[e.type||o.type],e),typeof o.icon=="function"&&(o.icon=o.icon(t)),o.spinner?(o.spinner===!0&&(o.spinner=Er),o.spinner=bn(o.spinner)):o.spinner=!1,o.meta={hasMedia:Boolean(o.spinner!==!1||o.icon||o.avatar),hasText:da(o.message)||da(o.caption)},o.position){if(Li.includes(o.position)===!1)return Gn("wrong position",e)}else o.position="bottom";if(rv.includes(o.timeout)===!0)o.timeout=5e3;else{const a=Number(o.timeout);if(isNaN(a)||a<0)return Gn("wrong timeout",e);o.timeout=Number.isFinite(a)?a:0}o.timeout===0?o.progress=!1:o.progress===!0&&(o.meta.progressClass="q-notification__progress"+(o.progressClass?` ${o.progressClass}`:""),o.meta.progressStyle={animationDuration:`${o.timeout+1e3}ms`});const s=(Array.isArray(e.actions)===!0?e.actions:[]).concat(e.ignoreDefaults!==!0&&Array.isArray(eo.actions)===!0?eo.actions:[]).concat(Pn[e.type]!==void 0&&Array.isArray(Pn[e.type].actions)===!0?Pn[e.type].actions:[]),{closeBtn:i}=o;if(i&&s.push({label:typeof i=="string"?i:t.lang.label.close}),o.actions=s.map(({handler:a,noDismiss:c,...u})=>({flat:!0,...u,onClick:typeof a=="function"?()=>{a(),c!==!0&&l()}:()=>{l()}})),o.multiLine===void 0&&(o.multiLine=o.actions.length>1),Object.assign(o.meta,{class:`q-notification row items-stretch q-notification--${o.multiLine===!0?"multi-line":"standard"}`+(o.color!==void 0?` bg-${o.color}`:"")+(o.textColor!==void 0?` text-${o.textColor}`:"")+(o.classes!==void 0?` ${o.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(o.multiLine===!0?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(o.multiLine===!0?"":" col"),leftClass:o.meta.hasText===!0?"additional":"single",attrs:{role:"alert",...o.attrs}}),o.group===!1?(o.group=void 0,o.meta.group=void 0):((o.group===void 0||o.group===!0)&&(o.group=[o.message,o.caption,o.multiline].concat(o.actions.map(a=>`${a.label}*${a.icon}`)).join("|")),o.meta.group=o.group+"|"+o.position),o.actions.length===0?o.actions=void 0:o.meta.actionsClass="q-notification__actions row items-center "+(o.multiLine===!0?"justify-end":"col-auto")+(o.meta.hasMedia===!0?" q-notification__actions--with-media":""),n!==void 0){n.notif.meta.timer&&(clearTimeout(n.notif.meta.timer),n.notif.meta.timer=void 0),o.meta.uid=n.notif.meta.uid;const a=dt[o.position].value.indexOf(n.notif);dt[o.position].value[a]=o}else{const a=to[o.meta.group];if(a===void 0){if(o.meta.uid=tv++,o.meta.badge=1,["left","right","center"].indexOf(o.position)!==-1)dt[o.position].value.splice(Math.floor(dt[o.position].value.length/2),0,o);else{const c=o.position.indexOf("top")!==-1?"unshift":"push";dt[o.position].value[c](o)}o.group!==void 0&&(to[o.meta.group]=o)}else{if(a.meta.timer&&(clearTimeout(a.meta.timer),a.meta.timer=void 0),o.badgePosition!==void 0){if(ov.includes(o.badgePosition)===!1)return Gn("wrong badgePosition",e)}else o.badgePosition=`top-${o.position.indexOf("left")!==-1?"right":"left"}`;o.meta.uid=a.meta.uid,o.meta.badge=a.meta.badge+1,o.meta.badgeClass=`q-notification__badge q-notification__badge--${o.badgePosition}`+(o.badgeColor!==void 0?` bg-${o.badgeColor}`:"")+(o.badgeTextColor!==void 0?` text-${o.badgeTextColor}`:"")+(o.badgeClass?` ${o.badgeClass}`:"");const c=dt[o.position].value.indexOf(a);dt[o.position].value[c]=to[o.meta.group]=o}}const l=()=>{sv(o),r=void 0};if(o.timeout>0&&(o.meta.timer=setTimeout(()=>{o.meta.timer=void 0,l()},o.timeout+1e3)),o.group!==void 0)return a=>{a!==void 0?Gn("trying to update a grouped one which is forbidden",e):l()};if(r={dismiss:l,config:e,notif:o},n!==void 0){Object.assign(n,r);return}return a=>{if(r!==void 0)if(a===void 0)r.dismiss();else{const c=Object.assign({},r.config,a,{group:!1,position:o.position});of(c,t,r)}}}function sv(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);const t=dt[e.position].value.indexOf(e);if(t!==-1){e.group!==void 0&&delete to[e.meta.group];const n=rf[""+e.meta.uid];if(n){const{width:r,height:o}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=r,n.style.height=o}dt[e.position].value.splice(t,1),typeof e.onDismiss=="function"&&e.onDismiss()}}function da(e){return e!=null&&nv.test(e)!==!0}function Gn(e,t){return console.error(`Notify: ${e}`,t),!1}function iv(){return Me({name:"QNotifications",devtools:{hide:!0},setup(){return()=>P("div",{class:"q-notifications"},Li.map(e=>P(gp,{key:e,class:nf[e],tag:"div",name:`q-notification--${e}`},()=>dt[e].value.map(t=>{const n=t.meta,r=[];if(n.hasMedia===!0&&(t.spinner!==!1?r.push(P(t.spinner,{class:"q-notification__spinner q-notification__spinner--"+n.leftClass,color:t.spinnerColor,size:t.spinnerSize})):t.icon?r.push(P(Bt,{class:"q-notification__icon q-notification__icon--"+n.leftClass,name:t.icon,color:t.iconColor,size:t.iconSize,role:"img"})):t.avatar&&r.push(P(Bg,{class:"q-notification__avatar q-notification__avatar--"+n.leftClass},()=>P("img",{src:t.avatar,"aria-hidden":"true"})))),n.hasText===!0){let s;const i={class:"q-notification__message col"};if(t.html===!0)i.innerHTML=t.caption?`<div>${t.message}</div><div class="q-notification__caption">${t.caption}</div>`:t.message;else{const l=[t.message];s=t.caption?[P("div",l),P("div",{class:"q-notification__caption"},[t.caption])]:l}r.push(P("div",i,s))}const o=[P("div",{class:n.contentClass},r)];return t.progress===!0&&o.push(P("div",{key:`${n.uid}|p|${n.badge}`,class:n.progressClass,style:n.progressStyle})),t.actions!==void 0&&o.push(P("div",{class:n.actionsClass},t.actions.map(s=>P(Ys,s)))),n.badge>1&&o.push(P("div",{key:`${n.uid}|${n.badge}`,class:t.meta.badgeClass,style:t.badgeStyle},[n.badge])),P("div",{ref:s=>{rf[""+n.uid]=s},key:n.uid,class:n.class,...n.attrs},[P("div",{class:n.wrapperClass},o)])}))))}})}var sf={setDefaults(e){kt(e)===!0&&Object.assign(eo,e)},registerType(e,t){kt(t)===!0&&(Pn[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=n=>of(n,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,e.config.notify!==void 0&&this.setDefaults(e.config.notify),this.__installed!==!0){Li.forEach(r=>{dt[r]=de([]);const o=["left","center","right"].includes(r)===!0?"center":r.indexOf("top")!==-1?"top":"bottom",s=r.indexOf("left")!==-1?"start":r.indexOf("right")!==-1?"end":"center",i=["left","right"].includes(r)?`items-${r==="left"?"start":"end"} justify-center`:r==="center"?"flex-center":`items-${s}`;nf[r]=`q-notifications__list q-notifications__list--${o} fixed column no-wrap ${i}`});const n=Bi("q-notify");kc(iv(),t).mount(n)}}};let qr;const Zs=um("auth",{state:()=>({user:null,accessToken:"",tokenExpiry:null,refreshToken:"",refreshTokenExpiry:null}),persist:!0,getters:{userInfo:e=>e.user,isAuthenticated:e=>!!e.accessToken,isTokenExpired:e=>e.tokenExpiry?new Date>=e.tokenExpiry:!0,isRefreshTokenExpired:e=>e.refreshTokenExpiry?new Date>=e.refreshTokenExpiry:!0},actions:{login(e){this.user=e.user,this.updateToken(e)},getTokenClaims(){const e=this.accessToken;if(!e)return null;try{const n=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),r=JSON.parse(atob(n));return{user_id:r.user_id,is_admin:r.is_admin}}catch{return null}},isAdmin(){const e=this.getTokenClaims();return(e==null?void 0:e.is_admin)||!1},updateToken(e){this.accessToken=e.access_token,this.tokenExpiry=e.expires_at,this.refreshToken=e.refresh_token,this.refreshTokenExpiry=e.refresh_expires_at,localStorage.setItem("accessToken",e.access_token),localStorage.setItem("tokenExpiry",e.expires_at.toString()),localStorage.setItem("refreshToken",e.refresh_token),localStorage.setItem("refreshTokenExpiry",e.refresh_expires_at.toString())},logout(){this.accessToken="",this.tokenExpiry=null,this.refreshToken="",this.refreshTokenExpiry=null,this.user=null,localStorage.removeItem("accessToken"),localStorage.removeItem("tokenExpiry"),localStorage.removeItem("refreshToken"),localStorage.removeItem("refreshTokenExpiry")},initializeFromStorage(){const e=localStorage.getItem("accessToken"),t=localStorage.getItem("refreshToken"),n=localStorage.getItem("tokenExpiry"),r=localStorage.getItem("refreshTokenExpiry");e&&t&&n&&r&&(this.accessToken=e,this.tokenExpiry=new Date(n),this.refreshToken=t,this.refreshTokenExpiry=new Date(r))},setupWebSocket(){qr&&qr.close();const e=localStorage.getItem("token"),t={}.WS_URL;qr=new WebSocket(`${t}?token=${e}`),qr.onmessage=n=>{const r=JSON.parse(n.data);r.type==="forced_logout"&&(sf.create({type:"warning",message:r.message||"\u60A8\u7684\u5E33\u865F\u5DF2\u5728\u53E6\u4E00\u500B\u8A2D\u5099\u767B\u5165"}),this.logout(),Cg().push("/login"))}}}});function lf(e,t){return function(){return e.apply(t,arguments)}}const{toString:lv}=Object.prototype,{getPrototypeOf:Di}=Object,Ho=(e=>t=>{const n=lv.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),wt=e=>(e=e.toLowerCase(),t=>Ho(t)===e),zo=e=>t=>typeof t===e,{isArray:jn}=Array,Cr=zo("undefined");function av(e){return e!==null&&!Cr(e)&&e.constructor!==null&&!Cr(e.constructor)&&tt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const af=wt("ArrayBuffer");function uv(e){let t;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&af(e.buffer),t}const cv=zo("string"),tt=zo("function"),uf=zo("number"),Wo=e=>e!==null&&typeof e=="object",fv=e=>e===!0||e===!1,no=e=>{if(Ho(e)!=="object")return!1;const t=Di(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},dv=wt("Date"),hv=wt("File"),pv=wt("Blob"),mv=wt("FileList"),gv=e=>Wo(e)&&tt(e.pipe),vv=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||tt(e.append)&&((t=Ho(e))==="formdata"||t==="object"&&tt(e.toString)&&e.toString()==="[object FormData]"))},yv=wt("URLSearchParams"),[bv,_v,wv,xv]=["ReadableStream","Request","Response","Headers"].map(wt),Ev=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Fr(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e=="undefined")return;let r,o;if(typeof e!="object"&&(e=[e]),jn(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let l;for(r=0;r<i;r++)l=s[r],t.call(null,e[l],l,e)}}function cf(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const an=(()=>typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global)(),ff=e=>!Cr(e)&&e!==an;function ei(){const{caseless:e}=ff(this)&&this||{},t={},n=(r,o)=>{const s=e&&cf(t,o)||o;no(t[s])&&no(r)?t[s]=ei(t[s],r):no(r)?t[s]=ei({},r):jn(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Fr(arguments[r],n);return t}const Cv=(e,t,n,{allOwnKeys:r}={})=>(Fr(t,(o,s)=>{n&&tt(o)?e[s]=lf(o,n):e[s]=o},{allOwnKeys:r}),e),Sv=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),kv=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Av=(e,t,n,r)=>{let o,s,i;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&Di(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Tv=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Rv=e=>{if(!e)return null;if(jn(e))return e;let t=e.length;if(!uf(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Pv=(e=>t=>e&&t instanceof e)(typeof Uint8Array!="undefined"&&Di(Uint8Array)),Ov=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},Fv=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Bv=wt("HTMLFormElement"),Lv=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),ha=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Dv=wt("RegExp"),df=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Fr(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},Iv=e=>{df(e,(t,n)=>{if(tt(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(!!tt(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Mv=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return jn(e)?r(e):r(String(e).split(t)),n},$v=()=>{},qv=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,ys="abcdefghijklmnopqrstuvwxyz",pa="0123456789",hf={DIGIT:pa,ALPHA:ys,ALPHA_DIGIT:ys+ys.toUpperCase()+pa},Nv=(e=16,t=hf.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n};function Vv(e){return!!(e&&tt(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const jv=e=>{const t=new Array(10),n=(r,o)=>{if(Wo(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=jn(r)?[]:{};return Fr(r,(i,l)=>{const a=n(i,o+1);!Cr(a)&&(s[l]=a)}),t[o]=void 0,s}}return r};return n(e,0)},Uv=wt("AsyncFunction"),Hv=e=>e&&(Wo(e)||tt(e))&&tt(e.then)&&tt(e.catch),pf=((e,t)=>e?setImmediate:t?((n,r)=>(an.addEventListener("message",({source:o,data:s})=>{o===an&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),an.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",tt(an.postMessage)),zv=typeof queueMicrotask!="undefined"?queueMicrotask.bind(an):typeof process!="undefined"&&process.nextTick||pf;var A={isArray:jn,isArrayBuffer:af,isBuffer:av,isFormData:vv,isArrayBufferView:uv,isString:cv,isNumber:uf,isBoolean:fv,isObject:Wo,isPlainObject:no,isReadableStream:bv,isRequest:_v,isResponse:wv,isHeaders:xv,isUndefined:Cr,isDate:dv,isFile:hv,isBlob:pv,isRegExp:Dv,isFunction:tt,isStream:gv,isURLSearchParams:yv,isTypedArray:Pv,isFileList:mv,forEach:Fr,merge:ei,extend:Cv,trim:Ev,stripBOM:Sv,inherits:kv,toFlatObject:Av,kindOf:Ho,kindOfTest:wt,endsWith:Tv,toArray:Rv,forEachEntry:Ov,matchAll:Fv,isHTMLForm:Bv,hasOwnProperty:ha,hasOwnProp:ha,reduceDescriptors:df,freezeMethods:Iv,toObjectSet:Mv,toCamelCase:Lv,noop:$v,toFiniteNumber:qv,findKey:cf,global:an,isContextDefined:ff,ALPHABET:hf,generateString:Nv,isSpecCompliantForm:Vv,toJSONObject:jv,isAsyncFn:Uv,isThenable:Hv,setImmediate:pf,asap:zv};function ae(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}A.inherits(ae,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:A.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const mf=ae.prototype,gf={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{gf[e]={value:e}});Object.defineProperties(ae,gf);Object.defineProperty(mf,"isAxiosError",{value:!0});ae.from=(e,t,n,r,o,s)=>{const i=Object.create(mf);return A.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),ae.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};var Wv=null;function ti(e){return A.isPlainObject(e)||A.isArray(e)}function vf(e){return A.endsWith(e,"[]")?e.slice(0,-2):e}function ma(e,t,n){return e?e.concat(t).map(function(o,s){return o=vf(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function Kv(e){return A.isArray(e)&&!e.some(ti)}const Qv=A.toFlatObject(A,{},null,function(t){return/^is[A-Z]/.test(t)});function Ko(e,t,n){if(!A.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=A.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,w){return!A.isUndefined(w[x])});const r=n.metaTokens,o=n.visitor||u,s=n.dots,i=n.indexes,a=(n.Blob||typeof Blob!="undefined"&&Blob)&&A.isSpecCompliantForm(t);if(!A.isFunction(o))throw new TypeError("visitor must be a function");function c(g){if(g===null)return"";if(A.isDate(g))return g.toISOString();if(!a&&A.isBlob(g))throw new ae("Blob is not supported. Use a Buffer instead.");return A.isArrayBuffer(g)||A.isTypedArray(g)?a&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function u(g,x,w){let O=g;if(g&&!w&&typeof g=="object"){if(A.endsWith(x,"{}"))x=r?x:x.slice(0,-2),g=JSON.stringify(g);else if(A.isArray(g)&&Kv(g)||(A.isFileList(g)||A.endsWith(x,"[]"))&&(O=A.toArray(g)))return x=vf(x),O.forEach(function(y,E){!(A.isUndefined(y)||y===null)&&t.append(i===!0?ma([x],E,s):i===null?x:x+"[]",c(y))}),!1}return ti(g)?!0:(t.append(ma(w,x,s),c(g)),!1)}const f=[],d=Object.assign(Qv,{defaultVisitor:u,convertValue:c,isVisitable:ti});function p(g,x){if(!A.isUndefined(g)){if(f.indexOf(g)!==-1)throw Error("Circular reference detected in "+x.join("."));f.push(g),A.forEach(g,function(O,v){(!(A.isUndefined(O)||O===null)&&o.call(t,O,A.isString(v)?v.trim():v,x,d))===!0&&p(O,x?x.concat(v):[v])}),f.pop()}}if(!A.isObject(e))throw new TypeError("data must be an object");return p(e),t}function ga(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Ii(e,t){this._pairs=[],e&&Ko(e,this,t)}const yf=Ii.prototype;yf.append=function(t,n){this._pairs.push([t,n])};yf.toString=function(t){const n=t?function(r){return t.call(this,r,ga)}:ga;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function Gv(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function bf(e,t,n){if(!t)return e;const r=n&&n.encode||Gv,o=n&&n.serialize;let s;if(o?s=o(t,n):s=A.isURLSearchParams(t)?t.toString():new Ii(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Jv{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){A.forEach(this.handlers,function(r){r!==null&&t(r)})}}var va=Jv,_f={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Xv=typeof URLSearchParams!="undefined"?URLSearchParams:Ii,Yv=typeof FormData!="undefined"?FormData:null,Zv=typeof Blob!="undefined"?Blob:null,ey={isBrowser:!0,classes:{URLSearchParams:Xv,FormData:Yv,Blob:Zv},protocols:["http","https","file","blob","url","data"]};const Mi=typeof window!="undefined"&&typeof document!="undefined",ty=(e=>Mi&&["ReactNative","NativeScript","NS"].indexOf(e)<0)(typeof navigator!="undefined"&&navigator.product),ny=(()=>typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),ry=Mi&&window.location.href||"http://localhost";var oy=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Mi,hasStandardBrowserWebWorkerEnv:ny,hasStandardBrowserEnv:ty,origin:ry},Symbol.toStringTag,{value:"Module"})),vt={...oy,...ey};function sy(e,t){return Ko(e,new vt.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return vt.isNode&&A.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function iy(e){return A.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ly(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function wf(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=s>=n.length;return i=!i&&A.isArray(o)?o.length:i,a?(A.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!l):((!o[i]||!A.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&A.isArray(o[i])&&(o[i]=ly(o[i])),!l)}if(A.isFormData(e)&&A.isFunction(e.entries)){const n={};return A.forEachEntry(e,(r,o)=>{t(iy(r),o,n,0)}),n}return null}function ay(e,t,n){if(A.isString(e))try{return(t||JSON.parse)(e),A.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const $i={transitional:_f,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=A.isObject(t);if(s&&A.isHTMLForm(t)&&(t=new FormData(t)),A.isFormData(t))return o?JSON.stringify(wf(t)):t;if(A.isArrayBuffer(t)||A.isBuffer(t)||A.isStream(t)||A.isFile(t)||A.isBlob(t)||A.isReadableStream(t))return t;if(A.isArrayBufferView(t))return t.buffer;if(A.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return sy(t,this.formSerializer).toString();if((l=A.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return Ko(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),ay(t)):t}],transformResponse:[function(t){const n=this.transitional||$i.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(A.isResponse(t)||A.isReadableStream(t))return t;if(t&&A.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?ae.from(l,ae.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:vt.classes.FormData,Blob:vt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};A.forEach(["delete","get","head","post","put","patch"],e=>{$i.headers[e]={}});var qi=$i;const uy=A.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var cy=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&uy[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t};const ya=Symbol("internals");function Jn(e){return e&&String(e).trim().toLowerCase()}function ro(e){return e===!1||e==null?e:A.isArray(e)?e.map(ro):String(e)}function fy(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const dy=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function bs(e,t,n,r,o){if(A.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!A.isString(t)){if(A.isString(r))return t.indexOf(r)!==-1;if(A.isRegExp(r))return r.test(t)}}function hy(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function py(e,t){const n=A.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}class Qo{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(l,a,c){const u=Jn(a);if(!u)throw new Error("header name must be a non-empty string");const f=A.findKey(o,u);(!f||o[f]===void 0||c===!0||c===void 0&&o[f]!==!1)&&(o[f||a]=ro(l))}const i=(l,a)=>A.forEach(l,(c,u)=>s(c,u,a));if(A.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(A.isString(t)&&(t=t.trim())&&!dy(t))i(cy(t),n);else if(A.isHeaders(t))for(const[l,a]of t.entries())s(a,l,r);else t!=null&&s(n,t,r);return this}get(t,n){if(t=Jn(t),t){const r=A.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return fy(o);if(A.isFunction(n))return n.call(this,o,r);if(A.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Jn(t),t){const r=A.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||bs(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=Jn(i),i){const l=A.findKey(r,i);l&&(!n||bs(r,r[l],l,n))&&(delete r[l],o=!0)}}return A.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||bs(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return A.forEach(this,(o,s)=>{const i=A.findKey(r,s);if(i){n[i]=ro(o),delete n[s];return}const l=t?hy(s):String(s).trim();l!==s&&delete n[s],n[l]=ro(o),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return A.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&A.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[ya]=this[ya]={accessors:{}}).accessors,o=this.prototype;function s(i){const l=Jn(i);r[l]||(py(o,i),r[l]=!0)}return A.isArray(t)?t.forEach(s):s(t),this}}Qo.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);A.reduceDescriptors(Qo.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});A.freezeMethods(Qo);var yt=Qo;function _s(e,t){const n=this||qi,r=t||n,o=yt.from(r.headers);let s=r.data;return A.forEach(e,function(l){s=l.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function xf(e){return!!(e&&e.__CANCEL__)}function Un(e,t,n){ae.call(this,e==null?"canceled":e,ae.ERR_CANCELED,t,n),this.name="CanceledError"}A.inherits(Un,ae,{__CANCEL__:!0});function Ef(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new ae("Request failed with status code "+n.status,[ae.ERR_BAD_REQUEST,ae.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function my(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function gy(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(a){const c=Date.now(),u=r[s];i||(i=c),n[o]=a,r[o]=c;let f=s,d=0;for(;f!==o;)d+=n[f++],f=f%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const p=u&&c-u;return p?Math.round(d*1e3/p):void 0}}function vy(e,t){let n=0,r=1e3/t,o,s;const i=(c,u=Date.now())=>{n=u,o=null,s&&(clearTimeout(s),s=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),f=u-n;f>=r?i(c,u):(o=c,s||(s=setTimeout(()=>{s=null,i(o)},r-f)))},()=>o&&i(o)]}const xo=(e,t,n=3)=>{let r=0;const o=gy(50,250);return vy(s=>{const i=s.loaded,l=s.lengthComputable?s.total:void 0,a=i-r,c=o(a),u=i<=l;r=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:c||void 0,estimated:c&&l&&u?(l-i)/c:void 0,event:s,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},ba=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},_a=e=>(...t)=>A.asap(()=>e(...t));var yy=vt.hasStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function o(s){let i=s;return t&&(n.setAttribute("href",i),i=n.href),n.setAttribute("href",i),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=o(window.location.href),function(i){const l=A.isString(i)?o(i):i;return l.protocol===r.protocol&&l.host===r.host}}():function(){return function(){return!0}}(),by=vt.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];A.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),A.isString(r)&&i.push("path="+r),A.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function _y(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function wy(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Cf(e,t){return e&&!_y(t)?wy(e,t):t}const wa=e=>e instanceof yt?{...e}:e;function gn(e,t){t=t||{};const n={};function r(c,u,f){return A.isPlainObject(c)&&A.isPlainObject(u)?A.merge.call({caseless:f},c,u):A.isPlainObject(u)?A.merge({},u):A.isArray(u)?u.slice():u}function o(c,u,f){if(A.isUndefined(u)){if(!A.isUndefined(c))return r(void 0,c,f)}else return r(c,u,f)}function s(c,u){if(!A.isUndefined(u))return r(void 0,u)}function i(c,u){if(A.isUndefined(u)){if(!A.isUndefined(c))return r(void 0,c)}else return r(void 0,u)}function l(c,u,f){if(f in t)return r(c,u);if(f in e)return r(void 0,c)}const a={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(c,u)=>o(wa(c),wa(u),!0)};return A.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=a[u]||o,d=f(e[u],t[u],u);A.isUndefined(d)&&f!==l||(n[u]=d)}),n}var Sf=e=>{const t=gn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:l}=t;t.headers=i=yt.from(i),t.url=bf(Cf(t.baseURL,t.url),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(A.isFormData(n)){if(vt.hasStandardBrowserEnv||vt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[c,...u]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...u].join("; "))}}if(vt.hasStandardBrowserEnv&&(r&&A.isFunction(r)&&(r=r(t)),r||r!==!1&&yy(t.url))){const c=o&&s&&by.read(s);c&&i.set(o,c)}return t};const xy=typeof XMLHttpRequest!="undefined";var Ey=xy&&function(e){return new Promise(function(n,r){const o=Sf(e);let s=o.data;const i=yt.from(o.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:c}=o,u,f,d,p,g;function x(){p&&p(),g&&g(),o.cancelToken&&o.cancelToken.unsubscribe(u),o.signal&&o.signal.removeEventListener("abort",u)}let w=new XMLHttpRequest;w.open(o.method.toUpperCase(),o.url,!0),w.timeout=o.timeout;function O(){if(!w)return;const y=yt.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),F={data:!l||l==="text"||l==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:y,config:e,request:w};Ef(function(D){n(D),x()},function(D){r(D),x()},F),w=null}"onloadend"in w?w.onloadend=O:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(O)},w.onabort=function(){!w||(r(new ae("Request aborted",ae.ECONNABORTED,e,w)),w=null)},w.onerror=function(){r(new ae("Network Error",ae.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let E=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const F=o.transitional||_f;o.timeoutErrorMessage&&(E=o.timeoutErrorMessage),r(new ae(E,F.clarifyTimeoutError?ae.ETIMEDOUT:ae.ECONNABORTED,e,w)),w=null},s===void 0&&i.setContentType(null),"setRequestHeader"in w&&A.forEach(i.toJSON(),function(E,F){w.setRequestHeader(F,E)}),A.isUndefined(o.withCredentials)||(w.withCredentials=!!o.withCredentials),l&&l!=="json"&&(w.responseType=o.responseType),c&&([d,g]=xo(c,!0),w.addEventListener("progress",d)),a&&w.upload&&([f,p]=xo(a),w.upload.addEventListener("progress",f),w.upload.addEventListener("loadend",p)),(o.cancelToken||o.signal)&&(u=y=>{!w||(r(!y||y.type?new Un(null,e,w):y),w.abort(),w=null)},o.cancelToken&&o.cancelToken.subscribe(u),o.signal&&(o.signal.aborted?u():o.signal.addEventListener("abort",u)));const v=my(o.url);if(v&&vt.protocols.indexOf(v)===-1){r(new ae("Unsupported protocol "+v+":",ae.ERR_BAD_REQUEST,e));return}w.send(s||null)})};const Cy=(e,t)=>{let n=new AbortController,r;const o=function(a){if(!r){r=!0,i();const c=a instanceof Error?a:this.reason;n.abort(c instanceof ae?c:new Un(c instanceof Error?c.message:c))}};let s=t&&setTimeout(()=>{o(new ae(`timeout ${t} of ms exceeded`,ae.ETIMEDOUT))},t);const i=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(a=>{a&&(a.removeEventListener?a.removeEventListener("abort",o):a.unsubscribe(o))}),e=null)};e.forEach(a=>a&&a.addEventListener&&a.addEventListener("abort",o));const{signal:l}=n;return l.unsubscribe=i,[l,()=>{s&&clearTimeout(s),s=null}]};var Sy=Cy;const ky=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Ay=async function*(e,t,n){for await(const r of e)yield*ky(ArrayBuffer.isView(r)?r:await n(String(r)),t)},xa=(e,t,n,r,o)=>{const s=Ay(e,t,o);let i=0,l,a=c=>{l||(l=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:u,value:f}=await s.next();if(u){a(),c.close();return}let d=f.byteLength;if(n){let p=i+=d;n(p)}c.enqueue(new Uint8Array(f))}catch(u){throw a(u),u}},cancel(c){return a(c),s.return()}},{highWaterMark:2})},Go=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",kf=Go&&typeof ReadableStream=="function",ni=Go&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Af=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Ty=kf&&Af(()=>{let e=!1;const t=new Request(vt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ea=64*1024,ri=kf&&Af(()=>A.isReadableStream(new Response("").body)),Eo={stream:ri&&(e=>e.body)};Go&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Eo[t]&&(Eo[t]=A.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new ae(`Response type '${t}' is not supported`,ae.ERR_NOT_SUPPORT,r)})})})(new Response);const Ry=async e=>{if(e==null)return 0;if(A.isBlob(e))return e.size;if(A.isSpecCompliantForm(e))return(await new Request(e).arrayBuffer()).byteLength;if(A.isArrayBufferView(e)||A.isArrayBuffer(e))return e.byteLength;if(A.isURLSearchParams(e)&&(e=e+""),A.isString(e))return(await ni(e)).byteLength},Py=async(e,t)=>{const n=A.toFiniteNumber(e.getContentLength());return n==null?Ry(t):n};var Oy=Go&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:c,headers:u,withCredentials:f="same-origin",fetchOptions:d}=Sf(e);c=c?(c+"").toLowerCase():"text";let[p,g]=o||s||i?Sy([o,s],i):[],x,w;const O=()=>{!x&&setTimeout(()=>{p&&p.unsubscribe()}),x=!0};let v;try{if(a&&Ty&&n!=="get"&&n!=="head"&&(v=await Py(u,r))!==0){let I=new Request(t,{method:"POST",body:r,duplex:"half"}),D;if(A.isFormData(r)&&(D=I.headers.get("content-type"))&&u.setContentType(D),I.body){const[M,_]=ba(v,xo(_a(a)));r=xa(I.body,Ea,M,_,ni)}}A.isString(f)||(f=f?"include":"omit"),w=new Request(t,{...d,signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:f});let y=await fetch(w);const E=ri&&(c==="stream"||c==="response");if(ri&&(l||E)){const I={};["status","statusText","headers"].forEach(C=>{I[C]=y[C]});const D=A.toFiniteNumber(y.headers.get("content-length")),[M,_]=l&&ba(D,xo(_a(l),!0))||[];y=new Response(xa(y.body,Ea,M,()=>{_&&_(),E&&O()},ni),I)}c=c||"text";let F=await Eo[A.findKey(Eo,c)||"text"](y,e);return!E&&O(),g&&g(),await new Promise((I,D)=>{Ef(I,D,{data:F,headers:yt.from(y.headers),status:y.status,statusText:y.statusText,config:e,request:w})})}catch(y){throw O(),y&&y.name==="TypeError"&&/fetch/i.test(y.message)?Object.assign(new ae("Network Error",ae.ERR_NETWORK,e,w),{cause:y.cause||y}):ae.from(y,y&&y.code,e,w)}});const oi={http:Wv,xhr:Ey,fetch:Oy};A.forEach(oi,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ca=e=>`- ${e}`,Fy=e=>A.isFunction(e)||e===null||e===!1;var Tf={getAdapter:e=>{e=A.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!Fy(n)&&(r=oi[(i=String(n)).toLowerCase()],r===void 0))throw new ae(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(Ca).join(`
`):" "+Ca(s[0]):"as no adapter specified";throw new ae("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:oi};function ws(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Un(null,e)}function Sa(e){return ws(e),e.headers=yt.from(e.headers),e.data=_s.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Tf.getAdapter(e.adapter||qi.adapter)(e).then(function(r){return ws(e),r.data=_s.call(e,e.transformResponse,r),r.headers=yt.from(r.headers),r},function(r){return xf(r)||(ws(e),r&&r.response&&(r.response.data=_s.call(e,e.transformResponse,r.response),r.response.headers=yt.from(r.response.headers))),Promise.reject(r)})}const Rf="1.7.3",Ni={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ni[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const ka={};Ni.transitional=function(t,n,r){function o(s,i){return"[Axios v"+Rf+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,l)=>{if(t===!1)throw new ae(o(i," has been removed"+(n?" in "+n:"")),ae.ERR_DEPRECATED);return n&&!ka[i]&&(ka[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,l):!0}};function By(e,t,n){if(typeof e!="object")throw new ae("options must be an object",ae.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const l=e[s],a=l===void 0||i(l,s,e);if(a!==!0)throw new ae("option "+s+" must be "+a,ae.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new ae("Unknown option "+s,ae.ERR_BAD_OPTION)}}var si={assertOptions:By,validators:Ni};const $t=si.validators;class Co{constructor(t){this.defaults=t,this.interceptors={request:new va,response:new va}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o;Error.captureStackTrace?Error.captureStackTrace(o={}):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=gn(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&si.assertOptions(r,{silentJSONParsing:$t.transitional($t.boolean),forcedJSONParsing:$t.transitional($t.boolean),clarifyTimeoutError:$t.transitional($t.boolean)},!1),o!=null&&(A.isFunction(o)?n.paramsSerializer={serialize:o}:si.assertOptions(o,{encode:$t.function,serialize:$t.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&A.merge(s.common,s[n.method]);s&&A.forEach(["delete","get","head","post","put","patch","common"],g=>{delete s[g]}),n.headers=yt.concat(i,s);const l=[];let a=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(n)===!1||(a=a&&x.synchronous,l.unshift(x.fulfilled,x.rejected))});const c=[];this.interceptors.response.forEach(function(x){c.push(x.fulfilled,x.rejected)});let u,f=0,d;if(!a){const g=[Sa.bind(this),void 0];for(g.unshift.apply(g,l),g.push.apply(g,c),d=g.length,u=Promise.resolve(n);f<d;)u=u.then(g[f++],g[f++]);return u}d=l.length;let p=n;for(f=0;f<d;){const g=l[f++],x=l[f++];try{p=g(p)}catch(w){x.call(this,w);break}}try{u=Sa.call(this,p)}catch(g){return Promise.reject(g)}for(f=0,d=c.length;f<d;)u=u.then(c[f++],c[f++]);return u}getUri(t){t=gn(this.defaults,t);const n=Cf(t.baseURL,t.url);return bf(n,t.params,t.paramsSerializer)}}A.forEach(["delete","get","head","options"],function(t){Co.prototype[t]=function(n,r){return this.request(gn(r||{},{method:t,url:n,data:(r||{}).data}))}});A.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,l){return this.request(gn(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}Co.prototype[t]=n(),Co.prototype[t+"Form"]=n(!0)});var oo=Co;class Vi{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(l=>{r.subscribe(l),s=l}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,l){r.reason||(r.reason=new Un(s,i,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new Vi(function(o){t=o}),cancel:t}}}var Ly=Vi;function Dy(e){return function(n){return e.apply(null,n)}}function Iy(e){return A.isObject(e)&&e.isAxiosError===!0}const ii={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ii).forEach(([e,t])=>{ii[t]=e});var My=ii;function Pf(e){const t=new oo(e),n=lf(oo.prototype.request,t);return A.extend(n,oo.prototype,t,{allOwnKeys:!0}),A.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return Pf(gn(e,o))},n}const Oe=Pf(qi);Oe.Axios=oo;Oe.CanceledError=Un;Oe.CancelToken=Ly;Oe.isCancel=xf;Oe.VERSION=Rf;Oe.toFormData=Ko;Oe.AxiosError=ae;Oe.Cancel=Oe.CanceledError;Oe.all=function(t){return Promise.all(t)};Oe.spread=Dy;Oe.isAxiosError=Iy;Oe.mergeConfig=gn;Oe.AxiosHeaders=yt;Oe.formToJSON=e=>wf(A.isHTMLForm(e)?new FormData(e):e);Oe.getAdapter=Tf.getAdapter;Oe.HttpStatusCode=My;Oe.default=Oe;var ji=Oe;const Aa=ji.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}}),lt=ji.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}});var $y=({app:e,router:t})=>{e.config.globalProperties.$axios=ji,e.config.globalProperties.$api=lt;let n=!1,r=[];const o=(i,l=null)=>{r.forEach(a=>{i?a.reject(i):a.resolve(l)}),r=[]};lt.interceptors.request.use(i=>{const l=Zs(),a=l.accessToken;return a&&(i.headers.Authorization=`Bearer ${a}`,i.headers["X-Refresh-Token"]=l.refreshToken),i},i=>Promise.reject(i)),lt.interceptors.response.use(i=>i,async i=>{var a;const l=i.config;switch((a=i.response)==null?void 0:a.status){case 401:return s(l);case 403:t.push("/index");break}return Promise.reject(i)});const s=async i=>{const l=["auth/logout","auth/refresh-token"];if(!i._retry&&!l.some(a=>{var c;return(c=i.url)==null?void 0:c.includes(a)})){i._retry=!0;const a=Zs();if(n)return new Promise((c,u)=>{r.push({resolve:c,reject:u})}).then(c=>(i.headers||(i.headers={}),i.headers.Authorization=`Bearer ${c}`,i.headers["X-Refresh-Token"]=a.refreshToken,lt(i))).catch(c=>Promise.reject(c));n=!0;try{if(!localStorage.getItem("refreshToken"))throw new Error("No refresh token");const f=(await Of.refreshToken()).data;return a.updateToken(f),n=!1,o(null,f.access_token),i.headers||(i.headers={}),i.headers.Authorization=`Bearer ${f.access_token}`,i.headers["X-Refresh-Token"]=f.refresh_token,lt(i)}catch(c){return n=!1,o(c,null),await a.logout(),t.push("/login"),Promise.reject(c)}}}},qy=Object.freeze(Object.defineProperty({__proto__:null,default:$y,api:lt},Symbol.toStringTag,{value:"Module"}));const Of={register:e=>Aa.post("/auth/register",e),login:e=>Aa.post("/auth/login",e),logout:()=>lt.post("/auth/logout"),getCurrentUser:()=>lt.get("/auth/user"),refreshToken:()=>lt.post("/auth/refresh-token"),getUserProfile:()=>lt.get("/profile"),updateUserProfile:e=>lt.patch("/profile",e),changePassword:e=>lt.patch("/profile/password",e)},Ny=[{path:"/login",component:()=>ke(()=>import("./LoginLayout.6a123753.js"),["assets/LoginLayout.6a123753.js","assets/LoginLayout.349bc137.css","assets/title.58682bbe.js","assets/QResizeObserver.949ee671.js"]),children:[{path:"",component:()=>ke(()=>import("./LoginPage.e17732ea.js"),["assets/LoginPage.e17732ea.js","assets/QForm.d9665ee9.js","assets/QPage.48be0cc6.js","assets/error-handler.a349144b.js"])}]},{path:"/register",component:()=>ke(()=>import("./LoginLayout.6a123753.js"),["assets/LoginLayout.6a123753.js","assets/LoginLayout.349bc137.css","assets/title.58682bbe.js","assets/QResizeObserver.949ee671.js"]),children:[{path:"",component:()=>ke(()=>import("./RegisterPage.e592e5e4.js"),["assets/RegisterPage.e592e5e4.js","assets/QForm.d9665ee9.js","assets/QPage.48be0cc6.js","assets/dialog.3a58cef7.js","assets/error-handler.a349144b.js"])}]},{path:"/",redirect:"/lotto-results",component:()=>ke(()=>import("./MainLayout.46365809.js"),["assets/MainLayout.46365809.js","assets/MainLayout.32683a3e.css","assets/title.58682bbe.js","assets/QResizeObserver.949ee671.js","assets/QSpace.c6e7a6da.js","assets/QList.ec342d80.js","assets/touch.9135741d.js","assets/selection.ed72df40.js","assets/QItem.68221b4d.js"]),children:[{path:"index",component:()=>ke(()=>import("./IndexPage.c697ef24.js").then(function(e){return e.I}),["assets/IndexPage.c697ef24.js","assets/IndexPage.8562f384.css","assets/QSelect.5be6abd0.js","assets/QItem.68221b4d.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QPage.48be0cc6.js","assets/QPopupProxy.3bc9b060.js","assets/lotto.b1d9e5c1.js","assets/padding.dd505b59.js"])},{path:"lotto-results",component:()=>ke(()=>import("./LottoResultsPage.56b9f154.js"),["assets/LottoResultsPage.56b9f154.js","assets/LottoResultsPage.4eb7443b.css","assets/QSpinnerDots.70047834.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QPage.48be0cc6.js","assets/lotto.b1d9e5c1.js","assets/padding.dd505b59.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"lotto-detail/:drawType",component:()=>ke(()=>import("./LottoDetailPage.41acbb3c.js"),["assets/LottoDetailPage.41acbb3c.js","assets/LottoDetailPage.a907d4c4.css","assets/QSpinnerDots.70047834.js","assets/QPage.48be0cc6.js","assets/lotto.b1d9e5c1.js","assets/padding.dd505b59.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"profile",component:()=>ke(()=>import("./ProfilePage.29540ee8.js"),["assets/ProfilePage.29540ee8.js","assets/ProfilePage.14b39986.css","assets/QForm.d9665ee9.js","assets/QPage.48be0cc6.js","assets/error-handler.a349144b.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"rd1",component:()=>ke(()=>import("./BallFollowPage.dfdc275a.js"),["assets/BallFollowPage.dfdc275a.js","assets/BallFollowPage.0e438e71.css","assets/QSelect.5be6abd0.js","assets/QItem.68221b4d.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QSpinnerDots.70047834.js","assets/QLinearProgress.46c3b050.js","assets/QPage.48be0cc6.js","assets/QTabPanels.53d33689.js","assets/QResizeObserver.949ee671.js","assets/touch.9135741d.js","assets/use-render-cache.3aae9b27.js","assets/IndexPage.c697ef24.js","assets/IndexPage.8562f384.css","assets/QPopupProxy.3bc9b060.js","assets/lotto.b1d9e5c1.js","assets/padding.dd505b59.js","assets/useLotteryAnalysis.9339f52c.js","assets/QPagination.0eb733af.js","assets/QSpace.c6e7a6da.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"tail",component:()=>ke(()=>import("./TailPage.d861a167.js"),["assets/TailPage.d861a167.js","assets/TailPage.514137fe.css","assets/QSelect.5be6abd0.js","assets/QItem.68221b4d.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QSpinnerDots.70047834.js","assets/QLinearProgress.46c3b050.js","assets/QPage.48be0cc6.js","assets/QTabPanels.53d33689.js","assets/QResizeObserver.949ee671.js","assets/touch.9135741d.js","assets/use-render-cache.3aae9b27.js","assets/IndexPage.c697ef24.js","assets/IndexPage.8562f384.css","assets/QPopupProxy.3bc9b060.js","assets/lotto.b1d9e5c1.js","assets/padding.dd505b59.js","assets/useLotteryAnalysis.9339f52c.js","assets/QPagination.0eb733af.js","assets/QTable.f6ea53e5.js","assets/QList.ec342d80.js","assets/use-quasar.57b6b537.js","assets/QSpace.c6e7a6da.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"pattern",component:()=>ke(()=>import("./PatternPage.53c56df5.js"),["assets/PatternPage.53c56df5.js","assets/PatternPage.dfe12680.css","assets/QSelect.5be6abd0.js","assets/QItem.68221b4d.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QSpinnerDots.70047834.js","assets/QLinearProgress.46c3b050.js","assets/QPage.48be0cc6.js","assets/IndexPage.c697ef24.js","assets/IndexPage.8562f384.css","assets/QPopupProxy.3bc9b060.js","assets/lotto.b1d9e5c1.js","assets/padding.dd505b59.js","assets/QTabPanels.53d33689.js","assets/QResizeObserver.949ee671.js","assets/touch.9135741d.js","assets/use-render-cache.3aae9b27.js","assets/useLotteryAnalysis.9339f52c.js","assets/QTable.f6ea53e5.js","assets/QList.ec342d80.js","assets/plugin-vue_export-helper.21dcd24c.js"])},{path:"install",component:()=>ke(()=>import("./InstallmentPage.ec618fd7.js"),["assets/InstallmentPage.ec618fd7.js","assets/QPage.48be0cc6.js"])},{path:"update-test",component:()=>ke(()=>import("./UpdateTestPage.df514f48.js"),["assets/UpdateTestPage.df514f48.js","assets/QPage.48be0cc6.js","assets/use-quasar.57b6b537.js"])},{path:"disclaimer",component:()=>ke(()=>import("./DisclaimerPage.969dec4a.js"),["assets/DisclaimerPage.969dec4a.js","assets/DisclaimerPage.0701b0f4.css","assets/QPage.48be0cc6.js","assets/plugin-vue_export-helper.21dcd24c.js"])}],meta:{requiresAuth:!0}},{path:"/admin/dashboard",redirect:"/admin/dashboard/user",component:()=>ke(()=>import("./MainLayout.46365809.js"),["assets/MainLayout.46365809.js","assets/MainLayout.32683a3e.css","assets/title.58682bbe.js","assets/QResizeObserver.949ee671.js","assets/QSpace.c6e7a6da.js","assets/QList.ec342d80.js","assets/touch.9135741d.js","assets/selection.ed72df40.js","assets/QItem.68221b4d.js"]),children:[{path:"user",component:()=>ke(()=>import("./UserPage.48097223.js"),["assets/UserPage.48097223.js","assets/UserPage.82259ac9.css","assets/QTable.f6ea53e5.js","assets/QList.ec342d80.js","assets/QSelect.5be6abd0.js","assets/QItem.68221b4d.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QLinearProgress.46c3b050.js","assets/use-render-cache.3aae9b27.js","assets/QPopupProxy.3bc9b060.js","assets/QPage.48be0cc6.js","assets/QSpace.c6e7a6da.js","assets/QForm.d9665ee9.js","assets/error-handler.a349144b.js","assets/dialog.3a58cef7.js"])},{path:"batch-analysis",component:()=>ke(()=>import("./BatchAnalysisPage.771e51e9.js"),["assets/BatchAnalysisPage.771e51e9.js","assets/BatchAnalysisPage.7005daa9.css","assets/QSelect.5be6abd0.js","assets/QItem.68221b4d.js","assets/position-engine.d4e46236.js","assets/selection.ed72df40.js","assets/QSpinnerDots.70047834.js","assets/QLinearProgress.46c3b050.js","assets/QPage.48be0cc6.js","assets/lotto.b1d9e5c1.js","assets/useLotteryAnalysis.9339f52c.js","assets/error-handler.a349144b.js","assets/plugin-vue_export-helper.21dcd24c.js"])}],meta:{requiresAuth:!0,requiresAdmin:!0}},{path:"/:catchAll(.*)*",component:()=>ke(()=>import("./ErrorNotFound.3df874de.js"),[])}];var xs=function(){const t=xg({scrollBehavior:()=>({left:0,top:0}),routes:Ny,history:Jm("/")}),n=a=>a.matched.some(c=>c.meta.requiresAuth),r=a=>a.matched.some(c=>c.meta.requiresAdmin),o=(a,c)=>{c({path:"/login",query:{redirect:a.fullPath}})},s=a=>{a.accessToken||a.initializeFromStorage()},i=async(a,c,u)=>{try{const f=await Of.refreshToken();return a.updateToken(f.data),u(),!0}catch{return a.logout(),o(c,u),!1}},l=(a,c)=>!a.isAuthenticated||!a.isAdmin()?(c({path:"/index"}),!1):!0;return t.beforeEach(async(a,c,u)=>{const f=Zs(),d=n(a),p=r(a);if(s(f),d){if(!f.isAuthenticated){o(a,u);return}if(f.isTokenExpired&&!f.isRefreshTokenExpired&&!await i(f,a,u))return;if(f.isRefreshTokenExpired){f.logout(),o(a,u);return}}p&&!l(f,u)||u()}),t};async function Vy(e,t){const n=e(tm);n.use(Yp,t);const r=typeof hs=="function"?await hs({}):hs;n.use(r);const o=bn(typeof xs=="function"?await xs({store:r}):xs);return r.use(({store:s})=>{s.router=o}),{app:n,store:r,router:o}}var jy={isoName:"zh-TW",nativeName:"\u4E2D\u6587\uFF08\u7E41\u9AD4\uFF09",label:{clear:"\u6E05\u9664",ok:"\u78BA\u5B9A",cancel:"\u53D6\u6D88",close:"\u95DC\u9589",set:"\u8A2D\u5B9A",select:"\u9078\u64C7",reset:"\u91CD\u7F6E",remove:"\u79FB\u9664",update:"\u66F4\u65B0",create:"\u65B0\u589E",search:"\u641C\u5C0B",filter:"\u7BE9\u9078",refresh:"\u66F4\u65B0",expand:e=>e?`\u5C55\u958B"${e}"`:"\u64F4\u5F35",collapse:e=>e?`\u6298\u758A"${e}"`:"\u574D\u584C"},date:{days:"\u661F\u671F\u65E5_\u661F\u671F\u4E00_\u661F\u671F\u4E8C_\u661F\u671F\u4E09_\u661F\u671F\u56DB_\u661F\u671F\u4E94_\u661F\u671F\u516D".split("_"),daysShort:"\u9031\u65E5_\u9031\u4E00_\u9031\u4E8C_\u9031\u4E09_\u9031\u56DB_\u9031\u4E94_\u9031\u516D".split("_"),months:"\u4E00\u6708_\u4E8C\u6708_\u4E09\u6708_\u56DB\u6708_\u4E94\u6708_\u516D\u6708_\u4E03\u6708_\u516B\u6708_\u4E5D\u6708_\u5341\u6708_\u5341\u4E00\u6708_\u5341\u4E8C\u6708".split("_"),monthsShort:"\u4E00\u6708_\u4E8C\u6708_\u4E09\u6708_\u56DB\u6708_\u4E94\u6708_\u516D\u6708_\u4E03\u6708_\u516B\u6708_\u4E5D\u6708_\u5341\u6708_\u5341\u4E00\u6708_\u5341\u4E8C\u6708".split("_"),headerTitle:e=>new Intl.DateTimeFormat("zh-TW",{weekday:"short",month:"short",day:"numeric"}).format(e),firstDayOfWeek:0,format24h:!1,pluralDay:"\u65E5"},table:{noData:"\u6C92\u6709\u8CC7\u6599",noResults:"\u6C92\u6709\u76F8\u7B26\u8CC7\u6599",loading:"\u8F09\u5165\u4E2D...",selectedRecords:e=>"\u5DF2\u9078\u64C7 "+e+" \u5217",recordsPerPage:"\u6BCF\u9801\u5217\u6578\uFF1A",allRows:"\u5168\u90E8",pagination:(e,t,n)=>e+"-"+t+" \u5217\uFF0C\u5171 "+n+" \u5217",columns:"\u6B04\u4F4D"},editor:{url:"\u7DB2\u5740",bold:"\u7C97\u9AD4",italic:"\u659C\u9AD4",strikethrough:"\u522A\u9664\u7DDA",underline:"\u4E0B\u5283\u7DDA",unorderedList:"\u9805\u76EE\u7B26\u865F\u6E05\u55AE",orderedList:"\u7DE8\u865F\u6E05\u55AE",subscript:"\u4E0B\u6A19",superscript:"\u4E0A\u6A19",hyperlink:"\u8D85\u9023\u7D50",toggleFullscreen:"\u5207\u63DB\u5168\u87A2\u5E55",quote:"\u6BB5\u843D\u5F15\u7528",left:"\u9760\u5DE6\u5C0D\u9F4A",center:"\u7F6E\u4E2D\u5C0D\u9F4A",right:"\u9760\u53F3\u5C0D\u9F4A",justify:"\u5206\u6563\u5C0D\u9F4A",print:"\u5217\u5370",outdent:"\u6E1B\u5C11\u7E2E\u6392",indent:"\u589E\u52A0\u7E2E\u6392",removeFormat:"\u6E05\u9664\u683C\u5F0F",formatting:"\u5340\u584A\u5143\u7D20",fontSize:"\u5B57\u578B\u5927\u5C0F",align:"\u5C0D\u9F4A",hr:"\u6C34\u5E73\u5206\u9694\u7DDA",undo:"\u5FA9\u539F",redo:"\u53D6\u6D88\u5FA9\u539F",heading1:"\u6A19\u984C 1",heading2:"\u6A19\u984C 2",heading3:"\u6A19\u984C 3",heading4:"\u6A19\u984C 4",heading5:"\u6A19\u984C 5",heading6:"\u6A19\u984C 6",paragraph:"\u6BB5\u843D",code:"\u7A0B\u5F0F\u78BC",size1:"\u975E\u5E38\u5C0F",size2:"\u7A0D\u5C0F",size3:"\u6B63\u5E38",size4:"\u7A0D\u5927",size5:"\u5927",size6:"\u975E\u5E38\u5927",size7:"\u8D85\u7D1A\u5927",defaultFont:"\u9810\u8A2D\u5B57\u578B",viewSource:"\u5207\u63DB\u539F\u59CB\u78BC"},tree:{noNodes:"\u6C92\u6709\u7BC0\u9EDE",noResults:"\u6C92\u6709\u76F8\u7B26\u7BC0\u9EDE"}};function Uy(e,t,n){let r;function o(){r!==void 0&&(Hs.remove(r),r=void 0)}return ct(()=>{e.value===!0&&o()}),{removeFromHistory:o,addToHistory(){r={condition:()=>n.value===!0,handler:t},Hs.add(r)}}}function Hy(){let e=null;const t=Se();function n(){e!==null&&(clearTimeout(e),e=null)}return Lo(n),ct(n),{removeTimeout:n,registerTimeout(r,o){n(),Zc(t)===!1&&(e=setTimeout(()=>{e=null,r()},o))}}}function zy(){let e;const t=Se();function n(){e=void 0}return Lo(n),ct(n),{removeTick:n,registerTick(r){e=r,Qe(()=>{e===r&&(Zc(t)===!1&&e(),e=void 0)})}}}const Wy={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},Ky=["beforeShow","show","beforeHide","hide"];function Qy({showing:e,canShow:t,hideOnRouteChange:n,handleShow:r,handleHide:o,processOnMount:s}){const i=Se(),{props:l,emit:a,proxy:c}=i;let u;function f(v){e.value===!0?g(v):d(v)}function d(v){if(l.disable===!0||v!==void 0&&v.qAnchorHandled===!0||t!==void 0&&t(v)!==!0)return;const y=l["onUpdate:modelValue"]!==void 0;y===!0&&(a("update:modelValue",!0),u=v,Qe(()=>{u===v&&(u=void 0)})),(l.modelValue===null||y===!1)&&p(v)}function p(v){e.value!==!0&&(e.value=!0,a("beforeShow",v),r!==void 0?r(v):a("show",v))}function g(v){if(l.disable===!0)return;const y=l["onUpdate:modelValue"]!==void 0;y===!0&&(a("update:modelValue",!1),u=v,Qe(()=>{u===v&&(u=void 0)})),(l.modelValue===null||y===!1)&&x(v)}function x(v){e.value!==!1&&(e.value=!1,a("beforeHide",v),o!==void 0?o(v):a("hide",v))}function w(v){l.disable===!0&&v===!0?l["onUpdate:modelValue"]!==void 0&&a("update:modelValue",!1):v===!0!==e.value&&(v===!0?p:x)(u)}be(()=>l.modelValue,w),n!==void 0&&Yc(i)===!0&&be(()=>c.$route.fullPath,()=>{n.value===!0&&e.value===!0&&g()}),s===!0&&Dt(()=>{w(l.modelValue)});const O={show:d,hide:g,toggle:f};return Object.assign(c,O),O}const Gy={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function Jy(e,t=()=>{},n=()=>{}){return{transitionProps:R(()=>{const r=`q-transition--${e.transitionShow||t()}`,o=`q-transition--${e.transitionHide||n()}`;return{appear:!0,enterFromClass:`${r}-enter-from`,enterActiveClass:`${r}-enter-active`,enterToClass:`${r}-enter-to`,leaveFromClass:`${o}-leave-from`,leaveActiveClass:`${o}-leave-active`,leaveToClass:`${o}-leave-to`}}),transitionStyle:R(()=>`--q-transition-duration: ${e.transitionDuration}ms`)}}let un=[],Sr=[];function Ff(e){Sr=Sr.filter(t=>t!==e)}function Xy(e){Ff(e),Sr.push(e)}function Ta(e){Ff(e),Sr.length===0&&un.length!==0&&(un[un.length-1](),un=[])}function Ui(e){Sr.length===0?e():un.push(e)}function Yy(e){un=un.filter(t=>t!==e)}const so=[];function R0(e){return so.find(t=>t.contentEl!==null&&t.contentEl.contains(e))}function Zy(e,t){do{if(e.$options.name==="QMenu"){if(e.hide(t),e.$props.separateClosePopup===!0)return Zr(e)}else if(e.__qPortal===!0){const n=Zr(e);return n!==void 0&&n.$options.name==="QPopupProxy"?(e.hide(t),n):e}e=Zr(e)}while(e!=null)}function P0(e,t,n){for(;n!==0&&e!==void 0&&e!==null;){if(e.__qPortal===!0){if(n--,e.$options.name==="QMenu"){e=Zy(e,t);continue}e.hide(t)}e=Zr(e)}}const eb=Me({name:"QPortal",setup(e,{slots:t}){return()=>t.default()}});function tb(e){for(e=e.parent;e!=null;){if(e.type.name==="QGlobalDialog")return!0;if(e.type.name==="QDialog"||e.type.name==="QMenu")return!1;e=e.parent}return!1}function nb(e,t,n,r){const o=de(!1),s=de(!1);let i=null;const l={},a=r==="dialog"&&tb(e);function c(f){if(f===!0){Ta(l),s.value=!0;return}s.value=!1,o.value===!1&&(a===!1&&i===null&&(i=Bi(!1,r)),o.value=!0,so.push(e.proxy),Xy(l))}function u(f){if(s.value=!1,f!==!0)return;Ta(l),o.value=!1;const d=so.indexOf(e.proxy);d!==-1&&so.splice(d,1),i!==null&&(tf(i),i=null)}return Io(()=>{u(!0)}),e.proxy.__qPortal=!0,_n(e.proxy,"contentEl",()=>t.value),{showPortal:c,hidePortal:u,portalIsActive:o,portalIsAccessible:s,renderPortal:()=>a===!0?n():o.value===!0?[P(zd,{to:i},P(eb,n))]:void 0}}const O0=[Element,String],rb=[null,document,document.body,document.scrollingElement,document.documentElement];function F0(e,t){let n=Ig(t);if(n===void 0){if(e==null)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return rb.includes(n)?window:n}function ob(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function sb(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let Nr;function B0(){if(Nr!==void 0)return Nr;const e=document.createElement("p"),t=document.createElement("div");Xs(e,{width:"100%",height:"200px"}),Xs(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),Nr=n-r,Nr}function ib(e,t=!0){return!e||e.nodeType!==Node.ELEMENT_NODE?!1:t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"]))}let Xn=0,Es,Cs,tr,Ss=!1,Ra,Pa,Oa,on=null;function lb(e){ab(e)&&it(e)}function ab(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=Bp(e),n=e.shiftKey&&!e.deltaX,r=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),o=n||r?e.deltaY:e.deltaX;for(let s=0;s<t.length;s++){const i=t[s];if(ib(i,r))return r?o<0&&i.scrollTop===0?!0:o>0&&i.scrollTop+i.clientHeight===i.scrollHeight:o<0&&i.scrollLeft===0?!0:o>0&&i.scrollLeft+i.clientWidth===i.scrollWidth}return!0}function Fa(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function Vr(e){Ss!==!0&&(Ss=!0,requestAnimationFrame(()=>{Ss=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:r}=document.scrollingElement;(tr===void 0||t!==window.innerHeight)&&(tr=n-t,document.scrollingElement.scrollTop=r),r>tr&&(document.scrollingElement.scrollTop-=Math.ceil((r-tr)/8))}))}function Ba(e){const t=document.body,n=window.visualViewport!==void 0;if(e==="add"){const{overflowY:r,overflowX:o}=window.getComputedStyle(t);Es=sb(window),Cs=ob(window),Ra=t.style.left,Pa=t.style.top,Oa=window.location.href,t.style.left=`-${Es}px`,t.style.top=`-${Cs}px`,o!=="hidden"&&(o==="scroll"||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),r!=="hidden"&&(r==="scroll"||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,Be.is.ios===!0&&(n===!0?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",Vr,Ke.passiveCapture),window.visualViewport.addEventListener("scroll",Vr,Ke.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",Fa,Ke.passiveCapture))}Be.is.desktop===!0&&Be.is.mac===!0&&window[`${e}EventListener`]("wheel",lb,Ke.notPassive),e==="remove"&&(Be.is.ios===!0&&(n===!0?(window.visualViewport.removeEventListener("resize",Vr,Ke.passiveCapture),window.visualViewport.removeEventListener("scroll",Vr,Ke.passiveCapture)):window.removeEventListener("scroll",Fa,Ke.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=Ra,t.style.top=Pa,window.location.href===Oa&&window.scrollTo(Es,Cs),tr=void 0)}function ub(e){let t="add";if(e===!0){if(Xn++,on!==null){clearTimeout(on),on=null;return}if(Xn>1)return}else{if(Xn===0||(Xn--,Xn>0))return;if(t="remove",Be.is.ios===!0&&Be.is.nativeMobile===!0){on!==null&&clearTimeout(on),on=setTimeout(()=>{Ba(t),on=null},100);return}}Ba(t)}function cb(){let e;return{preventBodyScroll(t){t!==e&&(e!==void 0||t===!0)&&(e=t,ub(t))}}}const hn=[];let Vn;function fb(e){Vn=e.keyCode===27}function db(){Vn===!0&&(Vn=!1)}function hb(e){Vn===!0&&(Vn=!1,_r(e,27)===!0&&hn[hn.length-1](e))}function Bf(e){window[e]("keydown",fb),window[e]("blur",db),window[e]("keyup",hb),Vn=!1}function pb(e){Be.is.desktop===!0&&(hn.push(e),hn.length===1&&Bf("addEventListener"))}function La(e){const t=hn.indexOf(e);t!==-1&&(hn.splice(t,1),hn.length===0&&Bf("removeEventListener"))}const pn=[];function Lf(e){pn[pn.length-1](e)}function mb(e){Be.is.desktop===!0&&(pn.push(e),pn.length===1&&document.body.addEventListener("focusin",Lf))}function Da(e){const t=pn.indexOf(e);t!==-1&&(pn.splice(t,1),pn.length===0&&document.body.removeEventListener("focusin",Lf))}let jr=0;const gb={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},Ia={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]};var vb=Me({name:"QDialog",inheritAttrs:!1,props:{...Wy,...Gy,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,backdropFilter:String,position:{type:String,default:"standard",validator:e=>["standard","top","bottom","left","right"].includes(e)}},emits:[...Ky,"shake","click","escapeKey"],setup(e,{slots:t,emit:n,attrs:r}){const o=Se(),s=de(null),i=de(!1),l=de(!1);let a=null,c=null,u,f;const d=R(()=>e.persistent!==!0&&e.noRouteDismiss!==!0&&e.seamless!==!0),{preventBodyScroll:p}=cb(),{registerTimeout:g}=Hy(),{registerTick:x,removeTick:w}=zy(),{transitionProps:O,transitionStyle:v}=Jy(e,()=>Ia[e.position][0],()=>Ia[e.position][1]),y=R(()=>v.value+(e.backdropFilter!==void 0?`;backdrop-filter:${e.backdropFilter};-webkit-backdrop-filter:${e.backdropFilter}`:"")),{showPortal:E,hidePortal:F,portalIsAccessible:I,renderPortal:D}=nb(o,s,Le,"dialog"),{hide:M}=Qy({showing:i,hideOnRouteChange:d,handleShow:Y,handleHide:J,processOnMount:!0}),{addToHistory:_,removeFromHistory:C}=Uy(i,M,d),L=R(()=>`q-dialog__inner flex no-pointer-events q-dialog__inner--${e.maximized===!0?"maximized":"minimized"} q-dialog__inner--${e.position} ${gb[e.position]}`+(l.value===!0?" q-dialog__inner--animating":"")+(e.fullWidth===!0?" q-dialog__inner--fullwidth":"")+(e.fullHeight===!0?" q-dialog__inner--fullheight":"")+(e.square===!0?" q-dialog__inner--square":"")),b=R(()=>i.value===!0&&e.seamless!==!0),U=R(()=>e.autoClose===!0?{onClick:q}:{}),T=R(()=>[`q-dialog fullscreen no-pointer-events q-dialog--${b.value===!0?"modal":"seamless"}`,r.class]);be(()=>e.maximized,ee=>{i.value===!0&&ce(ee)}),be(b,ee=>{p(ee),ee===!0?(mb(Re),pb(ne)):(Da(Re),La(ne))});function Y(ee){_(),c=e.noRefocus===!1&&document.activeElement!==null?document.activeElement:null,ce(e.maximized),E(),l.value=!0,e.noFocus!==!0?(document.activeElement!==null&&document.activeElement.blur(),x(H)):w(),g(()=>{if(o.proxy.$q.platform.is.ios===!0){if(e.seamless!==!0&&document.activeElement){const{top:k,bottom:Q}=document.activeElement.getBoundingClientRect(),{innerHeight:K}=window,Z=window.visualViewport!==void 0?window.visualViewport.height:K;k>0&&Q>Z/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-Z,Q>=K?1/0:Math.ceil(document.scrollingElement.scrollTop+Q-Z/2))),document.activeElement.scrollIntoView()}f=!0,s.value.click(),f=!1}E(!0),l.value=!1,n("show",ee)},e.transitionDuration)}function J(ee){w(),C(),ue(!0),l.value=!0,F(),c!==null&&(((ee&&ee.type.indexOf("key")===0?c.closest('[tabindex]:not([tabindex^="-"])'):void 0)||c).focus(),c=null),g(()=>{F(!0),l.value=!1,n("hide",ee)},e.transitionDuration)}function H(ee){Ui(()=>{let k=s.value;if(k!==null){if(ee!==void 0){const Q=k.querySelector(ee);if(Q!==null){Q.focus({preventScroll:!0});return}}k.contains(document.activeElement)!==!0&&(k=k.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||k.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||k.querySelector("[autofocus], [data-autofocus]")||k,k.focus({preventScroll:!0}))}})}function N(ee){ee&&typeof ee.focus=="function"?ee.focus({preventScroll:!0}):H(),n("shake");const k=s.value;k!==null&&(k.classList.remove("q-animate--scale"),k.classList.add("q-animate--scale"),a!==null&&clearTimeout(a),a=setTimeout(()=>{a=null,s.value!==null&&(k.classList.remove("q-animate--scale"),H())},170))}function ne(){e.seamless!==!0&&(e.persistent===!0||e.noEscDismiss===!0?e.maximized!==!0&&e.noShake!==!0&&N():(n("escapeKey"),M()))}function ue(ee){a!==null&&(clearTimeout(a),a=null),(ee===!0||i.value===!0)&&(ce(!1),e.seamless!==!0&&(p(!1),Da(Re),La(ne))),ee!==!0&&(c=null)}function ce(ee){ee===!0?u!==!0&&(jr<1&&document.body.classList.add("q-body--dialog"),jr++,u=!0):u===!0&&(jr<2&&document.body.classList.remove("q-body--dialog"),jr--,u=!1)}function q(ee){f!==!0&&(M(ee),n("click",ee))}function he(ee){e.persistent!==!0&&e.noBackdropDismiss!==!0?M(ee):e.noShake!==!0&&N()}function Re(ee){e.allowFocusOutside!==!0&&I.value===!0&&Mg(s.value,ee.target)!==!0&&H('[tabindex]:not([tabindex="-1"])')}Object.assign(o.proxy,{focus:H,shake:N,__updateRefocusTarget(ee){c=ee||null}}),ct(ue);function Le(){return P("div",{role:"dialog","aria-modal":b.value===!0?"true":"false",...r,class:T.value},[P(go,{name:"q-transition--fade",appear:!0},()=>b.value===!0?P("div",{class:"q-dialog__backdrop fixed-full",style:y.value,"aria-hidden":"true",tabindex:-1,onClick:he}):null),P(go,O.value,()=>i.value===!0?P("div",{ref:s,class:L.value,style:v.value,tabindex:-1,...U.value},pt(t.default)):null)])}return D}});const wn={dark:{type:Boolean,default:null}};function xn(e,t){return R(()=>e.dark===null?t.dark.isActive:e.dark)}var yb=Me({name:"QCard",props:{...wn,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=Se(),r=xn(e,n),o=R(()=>"q-card"+(r.value===!0?" q-card--dark q-dark":"")+(e.bordered===!0?" q-card--bordered":"")+(e.square===!0?" q-card--square no-border-radius":"")+(e.flat===!0?" q-card--flat no-shadow":""));return()=>P(e.tag,{class:o.value},pt(t.default))}}),Yn=Me({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(e,{slots:t}){const n=R(()=>`q-card__section q-card__section--${e.horizontal===!0?"horiz row no-wrap":"vert"}`);return()=>P(e.tag,{class:n.value},pt(t.default))}}),bb=Me({name:"QCardActions",props:{...Gc,vertical:Boolean},setup(e,{slots:t}){const n=Jc(e),r=R(()=>`q-card__actions ${n.value} q-card__actions--${e.vertical===!0?"vert column":"horiz row"}`);return()=>P("div",{class:r.value},pt(t.default))}});const _b={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},ks={xs:2,sm:4,md:8,lg:16,xl:24};var Ma=Me({name:"QSeparator",props:{...wn,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=Se(),n=xn(e,t.proxy.$q),r=R(()=>e.vertical===!0?"vertical":"horizontal"),o=R(()=>` q-separator--${r.value}`),s=R(()=>e.inset!==!1?`${o.value}-${_b[e.inset]}`:""),i=R(()=>`q-separator${o.value}${s.value}`+(e.color!==void 0?` bg-${e.color}`:"")+(n.value===!0?" q-separator--dark":"")),l=R(()=>{const a={};if(e.size!==void 0&&(a[e.vertical===!0?"width":"height"]=e.size),e.spaced!==!1){const c=e.spaced===!0?`${ks.md}px`:e.spaced in ks?`${ks[e.spaced]}px`:e.spaced,u=e.vertical===!0?["Left","Right"]:["Top","Bottom"];a[`margin${u[0]}`]=a[`margin${u[1]}`]=c}return a});return()=>P("hr",{class:i.value,style:l.value,"aria-orientation":r.value})}});let As,Ur=0;const Ie=new Array(256);for(let e=0;e<256;e++)Ie[e]=(e+256).toString(16).substring(1);const wb=(()=>{const e=typeof crypto!="undefined"?crypto:typeof window!="undefined"?window.crypto||window.msCrypto:void 0;if(e!==void 0){if(e.randomBytes!==void 0)return e.randomBytes;if(e.getRandomValues!==void 0)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return t=>{const n=[];for(let r=t;r>0;r--)n.push(Math.floor(Math.random()*256));return n}})(),$a=4096;function li(){(As===void 0||Ur+16>$a)&&(Ur=0,As=wb($a));const e=Array.prototype.slice.call(As,Ur,Ur+=16);return e[6]=e[6]&15|64,e[8]=e[8]&63|128,Ie[e[0]]+Ie[e[1]]+Ie[e[2]]+Ie[e[3]]+"-"+Ie[e[4]]+Ie[e[5]]+"-"+Ie[e[6]]+Ie[e[7]]+"-"+Ie[e[8]]+Ie[e[9]]+"-"+Ie[e[10]]+Ie[e[11]]+Ie[e[12]]+Ie[e[13]]+Ie[e[14]]+Ie[e[15]]}function xb(e){return e==null?null:e}function qa(e,t){return e==null?t===!0?`f_${li()}`:null:e}function Eb({getValue:e,required:t=!0}={}){if(Xt.value===!0){const n=de(e!==void 0?xb(e()):null);return t===!0&&n.value===null&&Dt(()=>{n.value=`f_${li()}`}),e!==void 0&&be(e,r=>{n.value=qa(r,t)}),n}return e!==void 0?R(()=>qa(e(),t)):de(`f_${li()}`)}const Na=/^on[A-Z]/;function Cb(){const{attrs:e,vnode:t}=Se(),n={listeners:de({}),attributes:de({})};function r(){const o={},s={};for(const i in e)i!=="class"&&i!=="style"&&Na.test(i)===!1&&(o[i]=e[i]);for(const i in t.props)Na.test(i)===!0&&(s[i]=t.props[i]);n.attributes.value=o,n.listeners.value=s}return Nu(r),r(),n}function Sb({validate:e,resetValidation:t,requiresQForm:n}){const r=et(Qp,!1);if(r!==!1){const{props:o,proxy:s}=Se();Object.assign(s,{validate:e,resetValidation:t}),be(()=>o.disable,i=>{i===!0?(typeof t=="function"&&t(),r.unbindComponent(s)):r.bindComponent(s)}),Dt(()=>{o.disable!==!0&&r.bindComponent(s)}),ct(()=>{o.disable!==!0&&r.unbindComponent(s)})}else n===!0&&console.error("Parent QForm not found on useFormChild()!")}const Va=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,ja=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,Ua=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,Hr=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,zr=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,Ts={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>Va.test(e),hexaColor:e=>ja.test(e),hexOrHexaColor:e=>Ua.test(e),rgbColor:e=>Hr.test(e),rgbaColor:e=>zr.test(e),rgbOrRgbaColor:e=>Hr.test(e)||zr.test(e),hexOrRgbColor:e=>Va.test(e)||Hr.test(e),hexaOrRgbaColor:e=>ja.test(e)||zr.test(e),anyColor:e=>Ua.test(e)||Hr.test(e)||zr.test(e)},kb=[!0,!1,"ondemand"],Ab={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],default:!1,validator:e=>kb.includes(e)}};function Tb(e,t){const{props:n,proxy:r}=Se(),o=de(!1),s=de(null),i=de(!1);Sb({validate:x,resetValidation:g});let l=0,a;const c=R(()=>n.rules!==void 0&&n.rules!==null&&n.rules.length!==0),u=R(()=>n.disable!==!0&&c.value===!0&&t.value===!1),f=R(()=>n.error===!0||o.value===!0),d=R(()=>typeof n.errorMessage=="string"&&n.errorMessage.length!==0?n.errorMessage:s.value);be(()=>n.modelValue,()=>{i.value=!0,u.value===!0&&n.lazyRules===!1&&w()});function p(){n.lazyRules!=="ondemand"&&u.value===!0&&i.value===!0&&w()}be(()=>n.reactiveRules,O=>{O===!0?a===void 0&&(a=be(()=>n.rules,p,{immediate:!0,deep:!0})):a!==void 0&&(a(),a=void 0)},{immediate:!0}),be(()=>n.lazyRules,p),be(e,O=>{O===!0?i.value=!0:u.value===!0&&n.lazyRules!=="ondemand"&&w()});function g(){l++,t.value=!1,i.value=!1,o.value=!1,s.value=null,w.cancel()}function x(O=n.modelValue){if(n.disable===!0||c.value===!1)return!0;const v=++l,y=t.value!==!0?()=>{i.value=!0}:()=>{},E=(I,D)=>{I===!0&&y(),o.value=I,s.value=D||null,t.value=!1},F=[];for(let I=0;I<n.rules.length;I++){const D=n.rules[I];let M;if(typeof D=="function"?M=D(O,Ts):typeof D=="string"&&Ts[D]!==void 0&&(M=Ts[D](O)),M===!1||typeof M=="string")return E(!0,M),!1;M!==!0&&M!==void 0&&F.push(M)}return F.length===0?(E(!1),!0):(t.value=!0,Promise.all(F).then(I=>{if(I===void 0||Array.isArray(I)===!1||I.length===0)return v===l&&E(!1),!0;const D=I.find(M=>M===!1||typeof M=="string");return v===l&&E(D!==void 0,D),D===void 0},I=>(v===l&&(console.error(I),E(!0)),!1)))}const w=_c(x,0);return ct(()=>{a!==void 0&&a(),w.cancel()}),Object.assign(r,{resetValidation:g,validate:x}),_n(r,"hasError",()=>f.value),{isDirtyModel:i,hasRules:c,hasError:f,errorMessage:d,validate:x,resetValidation:g}}function ai(e){return e!=null&&(""+e).length!==0}const Rb={...wn,...Ab,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String},Pb={...Rb,maxlength:[Number,String]},Ob=["update:modelValue","clear","focus","blur"];function Fb({requiredForAttr:e=!0,tagProp:t,changeEvent:n=!1}={}){const{props:r,proxy:o}=Se(),s=xn(r,o.$q),i=Eb({required:e,getValue:()=>r.for});return{requiredForAttr:e,changeEvent:n,tag:t===!0?R(()=>r.tag):{value:"label"},isDark:s,editable:R(()=>r.disable!==!0&&r.readonly!==!0),innerLoading:de(!1),focused:de(!1),hasPopupOpen:!1,splitAttrs:Cb(),targetUid:i,rootRef:de(null),targetRef:de(null),controlRef:de(null)}}function Bb(e){const{props:t,emit:n,slots:r,attrs:o,proxy:s}=Se(),{$q:i}=s;let l=null;e.hasValue===void 0&&(e.hasValue=R(()=>ai(t.modelValue))),e.emitValue===void 0&&(e.emitValue=N=>{n("update:modelValue",N)}),e.controlEvents===void 0&&(e.controlEvents={onFocusin:_,onFocusout:C}),Object.assign(e,{clearValue:L,onControlFocusin:_,onControlFocusout:C,focus:D}),e.computedCounter===void 0&&(e.computedCounter=R(()=>{if(t.counter!==!1){const N=typeof t.modelValue=="string"||typeof t.modelValue=="number"?(""+t.modelValue).length:Array.isArray(t.modelValue)===!0?t.modelValue.length:0,ne=t.maxlength!==void 0?t.maxlength:t.maxValues;return N+(ne!==void 0?" / "+ne:"")}}));const{isDirtyModel:a,hasRules:c,hasError:u,errorMessage:f,resetValidation:d}=Tb(e.focused,e.innerLoading),p=e.floatingLabel!==void 0?R(()=>t.stackLabel===!0||e.focused.value===!0||e.floatingLabel.value===!0):R(()=>t.stackLabel===!0||e.focused.value===!0||e.hasValue.value===!0),g=R(()=>t.bottomSlots===!0||t.hint!==void 0||c.value===!0||t.counter===!0||t.error!==null),x=R(()=>t.filled===!0?"filled":t.outlined===!0?"outlined":t.borderless===!0?"borderless":t.standout?"standout":"standard"),w=R(()=>`q-field row no-wrap items-start q-field--${x.value}`+(e.fieldClass!==void 0?` ${e.fieldClass.value}`:"")+(t.rounded===!0?" q-field--rounded":"")+(t.square===!0?" q-field--square":"")+(p.value===!0?" q-field--float":"")+(v.value===!0?" q-field--labeled":"")+(t.dense===!0?" q-field--dense":"")+(t.itemAligned===!0?" q-field--item-aligned q-item-type":"")+(e.isDark.value===!0?" q-field--dark":"")+(e.getControl===void 0?" q-field--auto-height":"")+(e.focused.value===!0?" q-field--focused":"")+(u.value===!0?" q-field--error":"")+(u.value===!0||e.focused.value===!0?" q-field--highlighted":"")+(t.hideBottomSpace!==!0&&g.value===!0?" q-field--with-bottom":"")+(t.disable===!0?" q-field--disabled":t.readonly===!0?" q-field--readonly":"")),O=R(()=>"q-field__control relative-position row no-wrap"+(t.bgColor!==void 0?` bg-${t.bgColor}`:"")+(u.value===!0?" text-negative":typeof t.standout=="string"&&t.standout.length!==0&&e.focused.value===!0?` ${t.standout}`:t.color!==void 0?` text-${t.color}`:"")),v=R(()=>t.labelSlot===!0||t.label!==void 0),y=R(()=>"q-field__label no-pointer-events absolute ellipsis"+(t.labelColor!==void 0&&u.value!==!0?` text-${t.labelColor}`:"")),E=R(()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:p.value,modelValue:t.modelValue,emitValue:e.emitValue})),F=R(()=>{const N={};return e.targetUid.value&&(N.for=e.targetUid.value),t.disable===!0&&(N["aria-disabled"]="true"),N});function I(){const N=document.activeElement;let ne=e.targetRef!==void 0&&e.targetRef.value;ne&&(N===null||N.id!==e.targetUid.value)&&(ne.hasAttribute("tabindex")===!0||(ne=ne.querySelector("[tabindex]")),ne&&ne!==N&&ne.focus({preventScroll:!0}))}function D(){Ui(I)}function M(){Yy(I);const N=document.activeElement;N!==null&&e.rootRef.value.contains(N)&&N.blur()}function _(N){l!==null&&(clearTimeout(l),l=null),e.editable.value===!0&&e.focused.value===!1&&(e.focused.value=!0,n("focus",N))}function C(N,ne){l!==null&&clearTimeout(l),l=setTimeout(()=>{l=null,!(document.hasFocus()===!0&&(e.hasPopupOpen===!0||e.controlRef===void 0||e.controlRef.value===null||e.controlRef.value.contains(document.activeElement)!==!1))&&(e.focused.value===!0&&(e.focused.value=!1,n("blur",N)),ne!==void 0&&ne())})}function L(N){it(N),i.platform.is.mobile!==!0?(e.targetRef!==void 0&&e.targetRef.value||e.rootRef.value).focus():e.rootRef.value.contains(document.activeElement)===!0&&document.activeElement.blur(),t.type==="file"&&(e.inputRef.value.value=null),n("update:modelValue",null),e.changeEvent===!0&&n("change",null),n("clear",t.modelValue),Qe(()=>{const ne=a.value;d(),a.value=ne})}function b(N){[13,32].includes(N.keyCode)&&L(N)}function U(){const N=[];return r.prepend!==void 0&&N.push(P("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:Kt},r.prepend())),N.push(P("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},T())),u.value===!0&&t.noErrorIcon===!1&&N.push(J("error",[P(Bt,{name:i.iconSet.field.error,color:"negative"})])),t.loading===!0||e.innerLoading.value===!0?N.push(J("inner-loading-append",r.loading!==void 0?r.loading():[P(Er,{color:t.color})])):t.clearable===!0&&e.hasValue.value===!0&&e.editable.value===!0&&N.push(J("inner-clearable-append",[P(Bt,{class:"q-field__focusable-action",name:t.clearIcon||i.iconSet.field.clear,tabindex:0,role:"button","aria-hidden":"false","aria-label":i.lang.label.clear,onKeyup:b,onClick:L})])),r.append!==void 0&&N.push(P("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:Kt},r.append())),e.getInnerAppend!==void 0&&N.push(J("inner-append",e.getInnerAppend())),e.getControlChild!==void 0&&N.push(e.getControlChild()),N}function T(){const N=[];return t.prefix!==void 0&&t.prefix!==null&&N.push(P("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),e.getShadowControl!==void 0&&e.hasShadow.value===!0&&N.push(e.getShadowControl()),e.getControl!==void 0?N.push(e.getControl()):r.rawControl!==void 0?N.push(r.rawControl()):r.control!==void 0&&N.push(P("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0},r.control(E.value))),v.value===!0&&N.push(P("div",{class:y.value},pt(r.label,t.label))),t.suffix!==void 0&&t.suffix!==null&&N.push(P("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),N.concat(pt(r.default))}function Y(){let N,ne;u.value===!0?f.value!==null?(N=[P("div",{role:"alert"},f.value)],ne=`q--slot-error-${f.value}`):(N=pt(r.error),ne="q--slot-error"):(t.hideHint!==!0||e.focused.value===!0)&&(t.hint!==void 0?(N=[P("div",t.hint)],ne=`q--slot-hint-${t.hint}`):(N=pt(r.hint),ne="q--slot-hint"));const ue=t.counter===!0||r.counter!==void 0;if(t.hideBottomSpace===!0&&ue===!1&&N===void 0)return;const ce=P("div",{key:ne,class:"q-field__messages col"},N);return P("div",{class:"q-field__bottom row items-start q-field__bottom--"+(t.hideBottomSpace!==!0?"animated":"stale"),onClick:Kt},[t.hideBottomSpace===!0?ce:P(go,{name:"q-transition--field-message"},()=>ce),ue===!0?P("div",{class:"q-field__counter"},r.counter!==void 0?r.counter():e.computedCounter.value):null])}function J(N,ne){return ne===null?null:P("div",{key:N,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},ne)}let H=!1;return Lo(()=>{H=!0}),$u(()=>{H===!0&&t.autofocus===!0&&s.focus()}),t.autofocus===!0&&Dt(()=>{s.focus()}),ct(()=>{l!==null&&clearTimeout(l)}),Object.assign(s,{focus:D,blur:M}),function(){const ne=e.getControl===void 0&&r.control===void 0?{...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus===!0||void 0,...F.value}:F.value;return P(e.tag.value,{ref:e.rootRef,class:[w.value,o.class],style:o.style,...ne},[r.before!==void 0?P("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:Kt},r.before()):null,P("div",{class:"q-field__inner relative-position col self-stretch"},[P("div",{ref:e.controlRef,class:O.value,tabindex:-1,...e.controlEvents},U()),g.value===!0?Y():null]),r.after!==void 0?P("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:Kt},r.after()):null])}}const Ha={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},So={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},Df=Object.keys(So);Df.forEach(e=>{So[e].regex=new RegExp(So[e].pattern)});const Lb=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+Df.join("")+"])|(.)","g"),za=/[.*+?^${}()|[\]\\]/g,Pe=String.fromCharCode(1),Db={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function Ib(e,t,n,r){let o,s,i,l,a,c;const u=de(null),f=de(p());function d(){return e.autogrow===!0||["textarea","text","search","url","tel","password"].includes(e.type)}be(()=>e.type+e.autogrow,x),be(()=>e.mask,_=>{if(_!==void 0)w(f.value,!0);else{const C=D(f.value);x(),e.modelValue!==C&&t("update:modelValue",C)}}),be(()=>e.fillMask+e.reverseFillMask,()=>{u.value===!0&&w(f.value,!0)}),be(()=>e.unmaskedValue,()=>{u.value===!0&&w(f.value)});function p(){if(x(),u.value===!0){const _=F(D(e.modelValue));return e.fillMask!==!1?M(_):_}return e.modelValue}function g(_){if(_<o.length)return o.slice(-_);let C="",L=o;const b=L.indexOf(Pe);if(b!==-1){for(let U=_-L.length;U>0;U--)C+=Pe;L=L.slice(0,b)+C+L.slice(b)}return L}function x(){if(u.value=e.mask!==void 0&&e.mask.length!==0&&d(),u.value===!1){l=void 0,o="",s="";return}const _=Ha[e.mask]===void 0?e.mask:Ha[e.mask],C=typeof e.fillMask=="string"&&e.fillMask.length!==0?e.fillMask.slice(0,1):"_",L=C.replace(za,"\\$&"),b=[],U=[],T=[];let Y=e.reverseFillMask===!0,J="",H="";_.replace(Lb,(ce,q,he,Re,Le)=>{if(Re!==void 0){const ee=So[Re];T.push(ee),H=ee.negate,Y===!0&&(U.push("(?:"+H+"+)?("+ee.pattern+"+)?(?:"+H+"+)?("+ee.pattern+"+)?"),Y=!1),U.push("(?:"+H+"+)?("+ee.pattern+")?")}else if(he!==void 0)J="\\"+(he==="\\"?"":he),T.push(he),b.push("([^"+J+"]+)?"+J+"?");else{const ee=q!==void 0?q:Le;J=ee==="\\"?"\\\\\\\\":ee.replace(za,"\\\\$&"),T.push(ee),b.push("([^"+J+"]+)?"+J+"?")}});const N=new RegExp("^"+b.join("")+"("+(J===""?".":"[^"+J+"]")+"+)?"+(J===""?"":"["+J+"]*")+"$"),ne=U.length-1,ue=U.map((ce,q)=>q===0&&e.reverseFillMask===!0?new RegExp("^"+L+"*"+ce):q===ne?new RegExp("^"+ce+"("+(H===""?".":H)+"+)?"+(e.reverseFillMask===!0?"$":L+"*")):new RegExp("^"+ce));i=T,l=ce=>{const q=N.exec(e.reverseFillMask===!0?ce:ce.slice(0,T.length+1));q!==null&&(ce=q.slice(1).join(""));const he=[],Re=ue.length;for(let Le=0,ee=ce;Le<Re;Le++){const k=ue[Le].exec(ee);if(k===null)break;ee=ee.slice(k.shift().length),he.push(...k)}return he.length!==0?he.join(""):ce},o=T.map(ce=>typeof ce=="string"?ce:Pe).join(""),s=o.split(Pe).join(C)}function w(_,C,L){const b=r.value,U=b.selectionEnd,T=b.value.length-U,Y=D(_);C===!0&&x();const J=F(Y),H=e.fillMask!==!1?M(J):J,N=f.value!==H;b.value!==H&&(b.value=H),N===!0&&(f.value=H),document.activeElement===b&&Qe(()=>{if(H===s){const ue=e.reverseFillMask===!0?s.length:0;b.setSelectionRange(ue,ue,"forward");return}if(L==="insertFromPaste"&&e.reverseFillMask!==!0){const ue=b.selectionEnd;let ce=U-1;for(let q=a;q<=ce&&q<ue;q++)o[q]!==Pe&&ce++;v.right(b,ce);return}if(["deleteContentBackward","deleteContentForward"].indexOf(L)!==-1){const ue=e.reverseFillMask===!0?U===0?H.length>J.length?1:0:Math.max(0,H.length-(H===s?0:Math.min(J.length,T)+1))+1:U;b.setSelectionRange(ue,ue,"forward");return}if(e.reverseFillMask===!0)if(N===!0){const ue=Math.max(0,H.length-(H===s?0:Math.min(J.length,T+1)));ue===1&&U===1?b.setSelectionRange(ue,ue,"forward"):v.rightReverse(b,ue)}else{const ue=H.length-T;b.setSelectionRange(ue,ue,"backward")}else if(N===!0){const ue=Math.max(0,o.indexOf(Pe),Math.min(J.length,U)-1);v.right(b,ue)}else{const ue=U-1;v.right(b,ue)}});const ne=e.unmaskedValue===!0?D(H):H;String(e.modelValue)!==ne&&(e.modelValue!==null||ne!=="")&&n(ne,!0)}function O(_,C,L){const b=F(D(_.value));C=Math.max(0,o.indexOf(Pe),Math.min(b.length,C)),a=C,_.setSelectionRange(C,L,"forward")}const v={left(_,C){const L=o.slice(C-1).indexOf(Pe)===-1;let b=Math.max(0,C-1);for(;b>=0;b--)if(o[b]===Pe){C=b,L===!0&&C++;break}if(b<0&&o[C]!==void 0&&o[C]!==Pe)return v.right(_,0);C>=0&&_.setSelectionRange(C,C,"backward")},right(_,C){const L=_.value.length;let b=Math.min(L,C+1);for(;b<=L;b++)if(o[b]===Pe){C=b;break}else o[b-1]===Pe&&(C=b);if(b>L&&o[C-1]!==void 0&&o[C-1]!==Pe)return v.left(_,L);_.setSelectionRange(C,C,"forward")},leftReverse(_,C){const L=g(_.value.length);let b=Math.max(0,C-1);for(;b>=0;b--)if(L[b-1]===Pe){C=b;break}else if(L[b]===Pe&&(C=b,b===0))break;if(b<0&&L[C]!==void 0&&L[C]!==Pe)return v.rightReverse(_,0);C>=0&&_.setSelectionRange(C,C,"backward")},rightReverse(_,C){const L=_.value.length,b=g(L),U=b.slice(0,C+1).indexOf(Pe)===-1;let T=Math.min(L,C+1);for(;T<=L;T++)if(b[T-1]===Pe){C=T,C>0&&U===!0&&C--;break}if(T>L&&b[C-1]!==void 0&&b[C-1]!==Pe)return v.leftReverse(_,L);_.setSelectionRange(C,C,"forward")}};function y(_){t("click",_),c=void 0}function E(_){if(t("keydown",_),xc(_)===!0||_.altKey===!0)return;const C=r.value,L=C.selectionStart,b=C.selectionEnd;if(_.shiftKey||(c=void 0),_.keyCode===37||_.keyCode===39){_.shiftKey&&c===void 0&&(c=C.selectionDirection==="forward"?L:b);const U=v[(_.keyCode===39?"right":"left")+(e.reverseFillMask===!0?"Reverse":"")];if(_.preventDefault(),U(C,c===L?b:L),_.shiftKey){const T=C.selectionStart;C.setSelectionRange(Math.min(c,T),Math.max(c,T),"forward")}}else _.keyCode===8&&e.reverseFillMask!==!0&&L===b?(v.left(C,L),C.setSelectionRange(C.selectionStart,b,"backward")):_.keyCode===46&&e.reverseFillMask===!0&&L===b&&(v.rightReverse(C,b),C.setSelectionRange(L,C.selectionEnd,"forward"))}function F(_){if(_==null||_==="")return"";if(e.reverseFillMask===!0)return I(_);const C=i;let L=0,b="";for(let U=0;U<C.length;U++){const T=_[L],Y=C[U];if(typeof Y=="string")b+=Y,T===Y&&L++;else if(T!==void 0&&Y.regex.test(T))b+=Y.transform!==void 0?Y.transform(T):T,L++;else return b}return b}function I(_){const C=i,L=o.indexOf(Pe);let b=_.length-1,U="";for(let T=C.length-1;T>=0&&b!==-1;T--){const Y=C[T];let J=_[b];if(typeof Y=="string")U=Y+U,J===Y&&b--;else if(J!==void 0&&Y.regex.test(J))do U=(Y.transform!==void 0?Y.transform(J):J)+U,b--,J=_[b];while(L===T&&J!==void 0&&Y.regex.test(J));else return U}return U}function D(_){return typeof _!="string"||l===void 0?typeof _=="number"?l(""+_):_:l(_)}function M(_){return s.length-_.length<=0?_:e.reverseFillMask===!0&&_.length!==0?s.slice(0,-_.length)+_:_+s.slice(_.length)}return{innerValue:f,hasMask:u,moveCursorForPaste:O,updateMaskValue:w,onMaskedKeydown:E,onMaskedClick:y}}const Hi={name:String};function L0(e){return R(()=>({type:"hidden",name:e.name,value:e.modelValue}))}function If(e={}){return(t,n,r)=>{t[n](P("input",{class:"hidden"+(r||""),...e.value}))}}function Mb(e){return R(()=>e.name||e.for)}function $b(e,t){function n(){const r=e.modelValue;try{const o="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(r)===r&&("length"in r?Array.from(r):[r]).forEach(s=>{o.items.add(s)}),{files:o.files}}catch{return{files:void 0}}}return R(t===!0?()=>{if(e.type==="file")return n()}:n)}const qb=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,Nb=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,Vb=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,jb=/[a-z0-9_ -]$/i;function Ub(e){return function(n){if(n.type==="compositionend"||n.type==="change"){if(n.target.qComposing!==!0)return;n.target.qComposing=!1,e(n)}else n.type==="compositionupdate"&&n.target.qComposing!==!0&&typeof n.data=="string"&&(Be.is.firefox===!0?jb.test(n.data)===!1:qb.test(n.data)===!0||Nb.test(n.data)===!0||Vb.test(n.data)===!0)===!0&&(n.target.qComposing=!0)}}var Hb=Me({name:"QInput",inheritAttrs:!1,props:{...Pb,...Db,...Hi,modelValue:[String,Number,FileList],shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...Ob,"paste","change","keydown","click","animationend"],setup(e,{emit:t,attrs:n}){const{proxy:r}=Se(),{$q:o}=r,s={};let i=NaN,l,a,c=null,u;const f=de(null),d=Mb(e),{innerValue:p,hasMask:g,moveCursorForPaste:x,updateMaskValue:w,onMaskedKeydown:O,onMaskedClick:v}=Ib(e,t,J,f),y=$b(e,!0),E=R(()=>ai(p.value)),F=Ub(T),I=Fb({changeEvent:!0}),D=R(()=>e.type==="textarea"||e.autogrow===!0),M=R(()=>D.value===!0||["text","search","url","tel","password"].includes(e.type)),_=R(()=>{const q={...I.splitAttrs.listeners.value,onInput:T,onPaste:U,onChange:N,onBlur:ne,onFocus:bo};return q.onCompositionstart=q.onCompositionupdate=q.onCompositionend=F,g.value===!0&&(q.onKeydown=O,q.onClick=v),e.autogrow===!0&&(q.onAnimationend=Y),q}),C=R(()=>{const q={tabindex:0,"data-autofocus":e.autofocus===!0||void 0,rows:e.type==="textarea"?6:void 0,"aria-label":e.label,name:d.value,...I.splitAttrs.attributes.value,id:I.targetUid.value,maxlength:e.maxlength,disabled:e.disable===!0,readonly:e.readonly===!0};return D.value===!1&&(q.type=e.type),e.autogrow===!0&&(q.rows=1),q});be(()=>e.type,()=>{f.value&&(f.value.value=e.modelValue)}),be(()=>e.modelValue,q=>{if(g.value===!0){if(a===!0&&(a=!1,String(q)===i))return;w(q)}else p.value!==q&&(p.value=q,e.type==="number"&&s.hasOwnProperty("value")===!0&&(l===!0?l=!1:delete s.value));e.autogrow===!0&&Qe(H)}),be(()=>e.autogrow,q=>{q===!0?Qe(H):f.value!==null&&n.rows>0&&(f.value.style.height="auto")}),be(()=>e.dense,()=>{e.autogrow===!0&&Qe(H)});function L(){Ui(()=>{const q=document.activeElement;f.value!==null&&f.value!==q&&(q===null||q.id!==I.targetUid.value)&&f.value.focus({preventScroll:!0})})}function b(){f.value!==null&&f.value.select()}function U(q){if(g.value===!0&&e.reverseFillMask!==!0){const he=q.target;x(he,he.selectionStart,he.selectionEnd)}t("paste",q)}function T(q){if(!q||!q.target)return;if(e.type==="file"){t("update:modelValue",q.target.files);return}const he=q.target.value;if(q.target.qComposing===!0){s.value=he;return}if(g.value===!0)w(he,!1,q.inputType);else if(J(he),M.value===!0&&q.target===document.activeElement){const{selectionStart:Re,selectionEnd:Le}=q.target;Re!==void 0&&Le!==void 0&&Qe(()=>{q.target===document.activeElement&&he.indexOf(q.target.value)===0&&q.target.setSelectionRange(Re,Le)})}e.autogrow===!0&&H()}function Y(q){t("animationend",q),H()}function J(q,he){u=()=>{c=null,e.type!=="number"&&s.hasOwnProperty("value")===!0&&delete s.value,e.modelValue!==q&&i!==q&&(i=q,he===!0&&(a=!0),t("update:modelValue",q),Qe(()=>{i===q&&(i=NaN)})),u=void 0},e.type==="number"&&(l=!0,s.value=q),e.debounce!==void 0?(c!==null&&clearTimeout(c),s.value=q,c=setTimeout(u,e.debounce)):u()}function H(){requestAnimationFrame(()=>{const q=f.value;if(q!==null){const he=q.parentNode.style,{scrollTop:Re}=q,{overflowY:Le,maxHeight:ee}=o.platform.is.firefox===!0?{}:window.getComputedStyle(q),k=Le!==void 0&&Le!=="scroll";k===!0&&(q.style.overflowY="hidden"),he.marginBottom=q.scrollHeight-1+"px",q.style.height="1px",q.style.height=q.scrollHeight+"px",k===!0&&(q.style.overflowY=parseInt(ee,10)<q.scrollHeight?"auto":"hidden"),he.marginBottom="",q.scrollTop=Re}})}function N(q){F(q),c!==null&&(clearTimeout(c),c=null),u!==void 0&&u(),t("change",q.target.value)}function ne(q){q!==void 0&&bo(q),c!==null&&(clearTimeout(c),c=null),u!==void 0&&u(),l=!1,a=!1,delete s.value,e.type!=="file"&&setTimeout(()=>{f.value!==null&&(f.value.value=p.value!==void 0?p.value:"")})}function ue(){return s.hasOwnProperty("value")===!0?s.value:p.value!==void 0?p.value:""}ct(()=>{ne()}),Dt(()=>{e.autogrow===!0&&H()}),Object.assign(I,{innerValue:p,fieldClass:R(()=>`q-${D.value===!0?"textarea":"input"}`+(e.autogrow===!0?" q-textarea--autogrow":"")),hasShadow:R(()=>e.type!=="file"&&typeof e.shadowText=="string"&&e.shadowText.length!==0),inputRef:f,emitValue:J,hasValue:E,floatingLabel:R(()=>E.value===!0&&(e.type!=="number"||isNaN(p.value)===!1)||ai(e.displayValue)),getControl:()=>P(D.value===!0?"textarea":"input",{ref:f,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...C.value,..._.value,...e.type!=="file"?{value:ue()}:y.value}),getShadowControl:()=>P("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(D.value===!0?"":" text-no-wrap")},[P("span",{class:"invisible"},ue()),P("span",e.shadowText)])});const ce=Bb(I);return Object.assign(r,{focus:L,select:b,getNativeElement:()=>f.value}),_n(r,"nativeEl",()=>f.value),ce}});function Mf(e,t){const n=de(null),r=R(()=>e.disable===!0?null:P("span",{ref:n,class:"no-outline",tabindex:-1}));function o(s){const i=t.value;s!==void 0&&s.type.indexOf("key")===0?i!==null&&document.activeElement!==i&&i.contains(document.activeElement)===!0&&i.focus():n.value!==null&&(s===void 0||i!==null&&i.contains(s.target)===!0)&&n.value.focus()}return{refocusTargetEl:r,refocusTarget:o}}var $f={xs:30,sm:35,md:40,lg:50,xl:60};const zb=P("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[P("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),P("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]);var Wb=Me({name:"QRadio",props:{...wn,...Pr,...Hi,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:r}=Se(),o=xn(e,r.$q),s=Or(e,$f),i=de(null),{refocusTargetEl:l,refocusTarget:a}=Mf(e,i),c=R(()=>le(e.modelValue)===le(e.val)),u=R(()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(e.disable===!0?" disabled":"")+(o.value===!0?" q-radio--dark":"")+(e.dense===!0?" q-radio--dense":"")+(e.leftLabel===!0?" reverse":"")),f=R(()=>{const y=e.color!==void 0&&(e.keepColor===!0||c.value===!0)?` text-${e.color}`:"";return`q-radio__inner relative-position q-radio__inner--${c.value===!0?"truthy":"falsy"}${y}`}),d=R(()=>(c.value===!0?e.checkedIcon:e.uncheckedIcon)||null),p=R(()=>e.disable===!0?-1:e.tabindex||0),g=R(()=>{const y={type:"radio"};return e.name!==void 0&&Object.assign(y,{".checked":c.value===!0,"^checked":c.value===!0?"checked":void 0,name:e.name,value:e.val}),y}),x=If(g);function w(y){y!==void 0&&(it(y),a(y)),e.disable!==!0&&c.value!==!0&&n("update:modelValue",e.val,y)}function O(y){(y.keyCode===13||y.keyCode===32)&&it(y)}function v(y){(y.keyCode===13||y.keyCode===32)&&w(y)}return Object.assign(r,{set:w}),()=>{const y=d.value!==null?[P("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[P(Bt,{class:"q-radio__icon",name:d.value})])]:[zb];e.disable!==!0&&x(y,"unshift"," q-radio__native q-ma-none q-pa-none");const E=[P("div",{class:f.value,style:s.value,"aria-hidden":"true"},y)];l.value!==null&&E.push(l.value);const F=e.label!==void 0?ln(t.default,[e.label]):pt(t.default);return F!==void 0&&E.push(P("div",{class:"q-radio__label q-anchor--skip"},F)),P("div",{ref:i,class:u.value,tabindex:p.value,role:"radio","aria-label":e.label,"aria-checked":c.value===!0?"true":"false","aria-disabled":e.disable===!0?"true":void 0,onClick:w,onKeydown:O,onKeyup:v},E)}}});const qf={...wn,...Pr,...Hi,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>e==="tf"||e==="ft"},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},Nf=["update:modelValue"];function Vf(e,t){const{props:n,slots:r,emit:o,proxy:s}=Se(),{$q:i}=s,l=xn(n,i),a=de(null),{refocusTargetEl:c,refocusTarget:u}=Mf(n,a),f=Or(n,$f),d=R(()=>n.val!==void 0&&Array.isArray(n.modelValue)),p=R(()=>{const b=le(n.val);return d.value===!0?n.modelValue.findIndex(U=>le(U)===b):-1}),g=R(()=>d.value===!0?p.value!==-1:le(n.modelValue)===le(n.trueValue)),x=R(()=>d.value===!0?p.value===-1:le(n.modelValue)===le(n.falseValue)),w=R(()=>g.value===!1&&x.value===!1),O=R(()=>n.disable===!0?-1:n.tabindex||0),v=R(()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(n.disable===!0?" disabled":"")+(l.value===!0?` q-${e}--dark`:"")+(n.dense===!0?` q-${e}--dense`:"")+(n.leftLabel===!0?" reverse":"")),y=R(()=>{const b=g.value===!0?"truthy":x.value===!0?"falsy":"indet",U=n.color!==void 0&&(n.keepColor===!0||(e==="toggle"?g.value===!0:x.value!==!0))?` text-${n.color}`:"";return`q-${e}__inner relative-position non-selectable q-${e}__inner--${b}${U}`}),E=R(()=>{const b={type:"checkbox"};return n.name!==void 0&&Object.assign(b,{".checked":g.value,"^checked":g.value===!0?"checked":void 0,name:n.name,value:d.value===!0?n.val:n.trueValue}),b}),F=If(E),I=R(()=>{const b={tabindex:O.value,role:e==="toggle"?"switch":"checkbox","aria-label":n.label,"aria-checked":w.value===!0?"mixed":g.value===!0?"true":"false"};return n.disable===!0&&(b["aria-disabled"]="true"),b});function D(b){b!==void 0&&(it(b),u(b)),n.disable!==!0&&o("update:modelValue",M(),b)}function M(){if(d.value===!0){if(g.value===!0){const b=n.modelValue.slice();return b.splice(p.value,1),b}return n.modelValue.concat([n.val])}if(g.value===!0){if(n.toggleOrder!=="ft"||n.toggleIndeterminate===!1)return n.falseValue}else if(x.value===!0){if(n.toggleOrder==="ft"||n.toggleIndeterminate===!1)return n.trueValue}else return n.toggleOrder!=="ft"?n.trueValue:n.falseValue;return n.indeterminateValue}function _(b){(b.keyCode===13||b.keyCode===32)&&it(b)}function C(b){(b.keyCode===13||b.keyCode===32)&&D(b)}const L=t(g,w);return Object.assign(s,{toggle:D}),()=>{const b=L();n.disable!==!0&&F(b,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const U=[P("div",{class:y.value,style:f.value,"aria-hidden":"true"},b)];c.value!==null&&U.push(c.value);const T=n.label!==void 0?ln(r.default,[n.label]):pt(r.default);return T!==void 0&&U.push(P("div",{class:`q-${e}__label q-anchor--skip`},T)),P("div",{ref:a,class:v.value,...I.value,onClick:D,onKeydown:_,onKeyup:C},U)}}const Kb=P("div",{key:"svg",class:"q-checkbox__bg absolute"},[P("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[P("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),P("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]);var Qb=Me({name:"QCheckbox",props:qf,emits:Nf,setup(e){function t(n,r){const o=R(()=>(n.value===!0?e.checkedIcon:r.value===!0?e.indeterminateIcon:e.uncheckedIcon)||null);return()=>o.value!==null?[P("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[P(Bt,{class:"q-checkbox__icon",name:o.value})])]:[Kb]}return Vf("checkbox",t)}}),Gb=Me({name:"QToggle",props:{...qf,icon:String,iconColor:String},emits:Nf,setup(e){function t(n,r){const o=R(()=>(n.value===!0?e.checkedIcon:r.value===!0?e.indeterminateIcon:e.uncheckedIcon)||e.icon),s=R(()=>n.value===!0?e.iconColor:null);return()=>[P("div",{class:"q-toggle__track"}),P("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},o.value!==void 0?[P(Bt,{name:o.value,color:s.value})]:void 0)]}return Vf("toggle",t)}});const jf={radio:Wb,checkbox:Qb,toggle:Gb},Jb=Object.keys(jf);var Xb=Me({name:"QOptionGroup",props:{...wn,modelValue:{required:!0},options:{type:Array,validator:e=>e.every(t=>"value"in t&&"label"in t)},name:String,type:{type:String,default:"radio",validator:e=>Jb.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{proxy:{$q:r}}=Se(),o=Array.isArray(e.modelValue);e.type==="radio"?o===!0&&console.error("q-option-group: model should not be array"):o===!1&&console.error("q-option-group: model should be array in your case");const s=xn(e,r),i=R(()=>jf[e.type]),l=R(()=>"q-option-group q-gutter-x-sm"+(e.inline===!0?" q-option-group--inline":"")),a=R(()=>{const u={role:"group"};return e.type==="radio"&&(u.role="radiogroup",e.disable===!0&&(u["aria-disabled"]="true")),u});function c(u){t("update:modelValue",u)}return()=>P("div",{class:l.value,...a.value},e.options.map((u,f)=>{const d=n["label-"+f]!==void 0?()=>n["label-"+f](u):n.label!==void 0?()=>n.label(u):void 0;return P("div",[P(i.value,{modelValue:e.modelValue,val:u.value,name:u.name===void 0?e.name:u.name,disable:e.disable||u.disable,label:d===void 0?u.label:null,leftLabel:u.leftLabel===void 0?e.leftLabel:u.leftLabel,color:u.color===void 0?e.color:u.color,checkedIcon:u.checkedIcon,uncheckedIcon:u.uncheckedIcon,dark:u.dark||s.value,size:u.size===void 0?e.size:u.size,dense:e.dense,keepColor:u.keepColor===void 0?e.keepColor:u.keepColor,"onUpdate:modelValue":c},d)])}))}}),Yb=Me({name:"DialogPluginComponent",props:{...wn,title:String,message:String,prompt:Object,options:Object,progress:[Boolean,Object],html:Boolean,ok:{type:[String,Object,Boolean],default:!0},cancel:[String,Object,Boolean],focus:{type:String,default:"ok",validator:e=>["ok","cancel","none"].includes(e)},stackButtons:Boolean,color:String,cardClass:[String,Array,Object],cardStyle:[String,Array,Object]},emits:["ok","hide"],setup(e,{emit:t}){const{proxy:n}=Se(),{$q:r}=n,o=xn(e,r),s=de(null),i=de(e.prompt!==void 0?e.prompt.model:e.options!==void 0?e.options.model:void 0),l=R(()=>"q-dialog-plugin"+(o.value===!0?" q-dialog-plugin--dark q-dark":"")+(e.progress!==!1?" q-dialog-plugin--progress":"")),a=R(()=>e.color||(o.value===!0?"amber":"primary")),c=R(()=>e.progress===!1?null:kt(e.progress)===!0?{component:e.progress.spinner||Er,props:{color:e.progress.color||a.value}}:{component:Er,props:{color:a.value}}),u=R(()=>e.prompt!==void 0||e.options!==void 0),f=R(()=>{if(u.value!==!0)return{};const{model:T,isValid:Y,items:J,...H}=e.prompt!==void 0?e.prompt:e.options;return H}),d=R(()=>kt(e.ok)===!0||e.ok===!0?r.lang.label.ok:e.ok),p=R(()=>kt(e.cancel)===!0||e.cancel===!0?r.lang.label.cancel:e.cancel),g=R(()=>e.prompt!==void 0?e.prompt.isValid!==void 0&&e.prompt.isValid(i.value)!==!0:e.options!==void 0?e.options.isValid!==void 0&&e.options.isValid(i.value)!==!0:!1),x=R(()=>({color:a.value,label:d.value,ripple:!1,disable:g.value,...kt(e.ok)===!0?e.ok:{flat:!0},"data-autofocus":e.focus==="ok"&&u.value!==!0||void 0,onClick:y})),w=R(()=>({color:a.value,label:p.value,ripple:!1,...kt(e.cancel)===!0?e.cancel:{flat:!0},"data-autofocus":e.focus==="cancel"&&u.value!==!0||void 0,onClick:E}));be(()=>e.prompt&&e.prompt.model,I),be(()=>e.options&&e.options.model,I);function O(){s.value.show()}function v(){s.value.hide()}function y(){t("ok",le(i.value)),v()}function E(){v()}function F(){t("hide")}function I(T){i.value=T}function D(T){g.value!==!0&&e.prompt.type!=="textarea"&&_r(T,13)===!0&&y()}function M(T,Y){return e.html===!0?P(Yn,{class:T,innerHTML:Y}):P(Yn,{class:T},()=>Y)}function _(){return[P(Hb,{color:a.value,dense:!0,autofocus:!0,dark:o.value,...f.value,modelValue:i.value,"onUpdate:modelValue":I,onKeyup:D})]}function C(){return[P(Xb,{color:a.value,options:e.options.items,dark:o.value,...f.value,modelValue:i.value,"onUpdate:modelValue":I})]}function L(){const T=[];return e.cancel&&T.push(P(Ys,w.value)),e.ok&&T.push(P(Ys,x.value)),P(bb,{class:e.stackButtons===!0?"items-end":"",vertical:e.stackButtons,align:"right"},()=>T)}function b(){const T=[];return e.title&&T.push(M("q-dialog__title",e.title)),e.progress!==!1&&T.push(P(Yn,{class:"q-dialog__progress"},()=>P(c.value.component,c.value.props))),e.message&&T.push(M("q-dialog__message",e.message)),e.prompt!==void 0?T.push(P(Yn,{class:"scroll q-dialog-plugin__form"},_)):e.options!==void 0&&T.push(P(Ma,{dark:o.value}),P(Yn,{class:"scroll q-dialog-plugin__form"},C),P(Ma,{dark:o.value})),(e.ok||e.cancel)&&T.push(L()),T}function U(){return[P(yb,{class:[l.value,e.cardClass],style:e.cardStyle,dark:o.value},b)]}return Object.assign(n,{show:O,hide:v}),()=>P(vb,{ref:s,onHide:F},U)}});function Uf(e,t){for(const n in t)n!=="spinner"&&Object(t[n])===t[n]?(e[n]=Object(e[n])!==e[n]?{}:{...e[n]},Uf(e[n],t[n])):e[n]=t[n]}function Zb(e,t,n){return r=>{let o,s;const i=t===!0&&r.component!==void 0;if(i===!0){const{component:v,componentProps:y}=r;o=typeof v=="string"?n.component(v):v,s=y||{}}else{const{class:v,style:y,...E}=r;o=e,s=E,v!==void 0&&(E.cardClass=v),y!==void 0&&(E.cardStyle=y)}let l,a=!1;const c=de(null),u=Bi(!1,"dialog"),f=v=>{if(c.value!==null&&c.value[v]!==void 0){c.value[v]();return}const y=l.$.subTree;if(y&&y.component){if(y.component.proxy&&y.component.proxy[v]){y.component.proxy[v]();return}if(y.component.subTree&&y.component.subTree.component&&y.component.subTree.component.proxy&&y.component.subTree.component.proxy[v]){y.component.subTree.component.proxy[v]();return}}console.error("[Quasar] Incorrectly defined Dialog component")},d=[],p=[],g={onOk(v){return d.push(v),g},onCancel(v){return p.push(v),g},onDismiss(v){return d.push(v),p.push(v),g},hide(){return f("hide"),g},update(v){if(l!==null){if(i===!0)Object.assign(s,v);else{const{class:y,style:E,...F}=v;y!==void 0&&(F.cardClass=y),E!==void 0&&(F.cardStyle=E),Uf(s,F)}l.$forceUpdate()}return g}},x=v=>{a=!0,d.forEach(y=>{y(v)})},w=()=>{O.unmount(u),tf(u),O=null,l=null,a!==!0&&p.forEach(v=>{v()})};let O=kc({name:"QGlobalDialog",setup:()=>()=>P(o,{...s,ref:c,onOk:x,onHide:w,onVnodeMounted(...v){typeof s.onVnodeMounted=="function"&&s.onVnodeMounted(...v),Qe(()=>f("show"))}})},n);return l=O.mount(u),g}}var e0={install({$q:e,parentApp:t}){e.dialog=this.create=Zb(Yb,!0,t)}},t0={config:{},lang:jy,plugins:{Dialog:e0,Notify:sf}},n0=function(){return Boolean(window.location.hostname==="localhost"||window.location.hostname==="[::1]"||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/))},ui;typeof window!="undefined"&&(typeof Promise!="undefined"?ui=new Promise(function(e){return window.addEventListener("load",e)}):ui={then:function(e){return window.addEventListener("load",e)}});function r0(e,t){t===void 0&&(t={});var n=t.registrationOptions;n===void 0&&(n={}),delete t.registrationOptions;var r=function(o){for(var s=[],i=arguments.length-1;i-- >0;)s[i]=arguments[i+1];t&&t[o]&&t[o].apply(t,s)};"serviceWorker"in navigator&&ui.then(function(){n0()?(o0(e,r,n),navigator.serviceWorker.ready.then(function(o){r("ready",o)}).catch(function(o){return kr(r,o)})):(Hf(e,r,n),navigator.serviceWorker.ready.then(function(o){r("ready",o)}).catch(function(o){return kr(r,o)}))})}function kr(e,t){navigator.onLine||e("offline"),e("error",t)}function Hf(e,t,n){navigator.serviceWorker.register(e,n).then(function(r){if(t("registered",r),r.waiting){t("updated",r);return}r.onupdatefound=function(){t("updatefound",r);var o=r.installing;o.onstatechange=function(){o.state==="installed"&&(navigator.serviceWorker.controller?t("updated",r):t("cached",r))}}}).catch(function(r){return kr(t,r)})}function o0(e,t,n){fetch(e).then(function(r){r.status===404?(t("error",new Error("Service worker not found at "+e)),Wa()):r.headers.get("content-type").indexOf("javascript")===-1?(t("error",new Error("Expected "+e+" to have javascript content-type, but received "+r.headers.get("content-type"))),Wa()):Hf(e,t,n)}).catch(function(r){return kr(t,r)})}function Wa(){"serviceWorker"in navigator&&navigator.serviceWorker.ready.then(function(e){e.unregister()}).catch(function(e){return kr(emit,e)})}r0("/sw.js",{ready(){},registered(){},cached(){},updatefound(){},updated(e){console.log("New content is available; auto-updating...");const t=()=>{"serviceWorker"in navigator&&e.waiting&&e.waiting.postMessage({type:"SKIP_WAITING"}),setTimeout(()=>{try{const n=globalThis;if(n&&n.location&&n.location.reload)n.location.reload();else{const r=n.location.href;n.location.href=r}}catch(n){console.error("\u91CD\u65B0\u8F09\u5165\u9801\u9762\u6642\u767C\u751F\u932F\u8AA4:",n);const r=globalThis;r.location.href=r.location.href}},1e3)};ke(()=>import("./quasar.client.af0c72d5.js"),[]).then(({Notify:n})=>{n.create({message:"\u6B63\u5728\u66F4\u65B0\u5230\u6700\u65B0\u7248\u672C...",icon:"cloud_download",color:"primary",position:"top",timeout:2e3,actions:[{label:"\u7ACB\u5373\u66F4\u65B0",color:"white",handler:t}]}),setTimeout(t,3e3)}).catch(()=>{t()})},offline(){},error(){}});const s0="/";async function i0({app:e,router:t,store:n},r){let o=!1;const s=a=>{try{return t.resolve(a).href}catch{}return Object(a)===a?null:a},i=a=>{if(o=!0,typeof a=="string"&&/^https?:\/\//.test(a)){window.location.href=a;return}const c=s(a);c!==null&&(window.location.href=c)},l=window.location.href.replace(window.location.origin,"");for(let a=0;o===!1&&a<r.length;a++)try{await r[a]({app:e,router:t,store:n,ssrContext:null,redirect:i,urlPath:l,publicPath:s0})}catch(c){if(c&&c.url){i(c.url);return}console.error("[Quasar] boot error:",c);return}o!==!0&&(e.use(t),e.mount("#q-app"))}Vy(yc,t0).then(e=>{const[t,n]=Promise.allSettled!==void 0?["allSettled",r=>r.map(o=>{if(o.status==="rejected"){console.error("[Quasar] boot error:",o.reason);return}return o.value.default})]:["all",r=>r.map(o=>o.default)];return Promise[t]([ke(()=>Promise.resolve().then(function(){return qy}),void 0)]).then(r=>{const o=n(r).filter(s=>typeof s=="function");i0(e,o)})});export{um as $,fn as A,h0 as B,Me as C,w0 as D,Xt as E,ln as F,y0 as G,th as H,rc as I,sc as J,a0 as K,Ud as L,Ge as M,Ih as N,ud as O,lc as P,pt as Q,S0 as R,b0 as S,go as T,O0 as U,br as V,F0 as W,Ke as X,ob as Y,sb as Z,B0 as _,ct as a,_c as a$,Cg as a0,Zs as a1,yb as a2,Yn as a3,Hb as a4,Bt as a5,bb as a6,Gb as a7,Ys as a8,Of as a9,ht as aA,d0 as aB,u0 as aC,Ma as aD,Mh as aE,T0 as aF,Hg as aG,_r as aH,Us as aI,Pb as aJ,Ob as aK,Bb as aL,Fb as aM,Pr as aN,Or as aO,Sg as aP,qg as aQ,Gy as aR,zy as aS,Jy as aT,nb as aU,mb as aV,Da as aW,La as aX,Zy as aY,pb as aZ,Mg as a_,Zc as aa,it as ab,Ui as ac,Qp as ad,ji as ae,sf as af,e0 as ag,Op as ah,Be as ai,g0 as aj,Lp as ak,v0 as al,Kt as am,bo as an,Fp as ao,Dp as ap,Wy as aq,wn as ar,Ky as as,xn as at,Hy as au,Qy as av,Uy as aw,k0 as ax,cb as ay,f0 as az,Dt as b,Hi as b0,Mb as b1,ai as b2,fs as b3,Ub as b4,xc as b5,vb as b6,_n as b7,lt as b8,so as b9,Fl as bA,L0 as bB,If as bC,R0 as bD,P0 as bE,p0 as ba,Lg as bb,Dg as bc,C0 as bd,mi as be,pi as bf,_0 as bg,li as bh,A0 as bi,Qg as bj,fa as bk,Gg as bl,c0 as bm,Yc as bn,Hs as bo,E0 as bp,x0 as bq,kt as br,m0 as bs,Qb as bt,Kp as bu,Er as bv,Zp as bw,Il as bx,em as by,Ht as bz,R as c,Nu as d,Si as e,$u as f,Io as g,P as h,et as i,yn as j,Fo as k,yc as l,bn as m,Qe as n,Lo as o,Au as p,zd as q,de as r,yu as s,Se as t,le as u,Kr as v,be as w,Jd as x,Ae as y,gp as z};
