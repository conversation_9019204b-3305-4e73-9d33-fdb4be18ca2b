import{C as Ce,c as we,h as Ae,Q as De,k as ve,r as b,b as ke,w as ie,I as t,J as x,L as d,a2 as T,M as r,a3 as W,P as e,O as l,az as s,aC as E,aA as h,A as p,aB as S,a8 as ee,be as M,N as P,b6 as qe,bf as Ee,a5 as Ne,aD as Pe,a6 as xe}from"./index.43a5c8e9.js";import{Q as Y,b as ue}from"./QSelect.5be6abd0.js";import{Q as $e,a as Re}from"./QItem.68221b4d.js";import{Q as Ve}from"./QSpinnerDots.70047834.js";import{Q as Se}from"./QLinearProgress.46c3b050.js";import{Q as Qe}from"./QPage.48be0cc6.js";import{Q as ze,a as pe,b as Le,c as me,u as Me}from"./QTabPanels.53d33689.js";import{u as he,_ as Ie}from"./IndexPage.c697ef24.js";import{u as Ue}from"./useLotteryAnalysis.9339f52c.js";import{L as ge}from"./lotto.b1d9e5c1.js";import{p as C}from"./padding.dd505b59.js";import{Q as ye}from"./QPagination.0eb733af.js";import{Q as Te}from"./QSpace.c6e7a6da.js";import{_ as Fe}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.d4e46236.js";import"./selection.ed72df40.js";import"./QResizeObserver.949ee671.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.3bc9b060.js";var Oe=Ce({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,square:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(te,{slots:_}){const y=we(()=>{const k=["unelevated","outline","flat","rounded","square","push","stretch","glossy"].filter(N=>te[N]===!0).map(N=>`q-btn-group--${N}`).join(" ");return`q-btn-group row no-wrap${k.length!==0?" "+k:""}`+(te.spread===!0?" q-btn-group--spread":" inline")});return()=>Ae("div",{class:y.value},De(_.default))}});const je={class:"row q-gutter-y-md"},Ge={class:"col-12 col-sm-5 draw-title text-center"},He={class:"text-period"},We={class:"text-draw-date"},Je={class:"col-12 col-sm-7 self-center"},Ze={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"row"},st={class:"col-auto text-h6"},lt={key:0,class:"row q-my-md justify-center"},ut={class:"row q-col-gutter-md"},at={key:0,class:"col-12 q-my-sm"},ot={class:"row q-col-gutter-md items-center text-h6"},rt={class:"col-12 col-sm-4"},it={class:"col-12 col-sm-6"},nt={class:"row q-col-gutter-md"},dt={class:"col-auto"},ct={class:"col-12 col-sm-2 text-center"},pt={key:1,class:"row justify-center"},mt={class:"row q-mb-sm"},vt={class:"col text-h6 text-bold"},_t={class:"row q-col-gutter-sm"},bt={class:"row items-center"},ft={class:"ball"},gt={class:"row q-mb-sm"},yt={class:"col text-h6 text-bold"},wt={class:"row q-col-gutter-sm"},ht={class:"row items-center"},Ft={class:"ball"},Bt={class:"row q-mb-sm"},Ct={class:"col text-h6 text-bold"},At={class:"row q-col-gutter-sm"},Dt={class:"ball"},kt={class:"row q-col-gutter-xs"},qt={class:"row items-center"},Et={class:"ball tail-number"},Nt={class:"row q-mt-md"},Pt={class:"row q-mt-xl"},xt={class:"row q-gutter-y-md"},$t={class:"col-12 col-sm-4 draw-title text-center"},Rt={class:"text-period"},Vt={class:"text-draw-date"},St={class:"col-12 col-sm-6 self-center"},Qt={class:"row justify-center"},zt={key:0,class:"col-auto"},Lt=ve({__name:"BallFollowResult",props:{maxNumber:{},isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},results:{},occurrenceResults:{},pageSize:{}},emits:["view-details"],setup(te){const _=te,y=b("1"),k=b([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21\u4EE5\u4E0A",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21\u4EE5\u4E0A",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21\u4EE5\u4E0A",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21\u4EE5\u4E0A",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21\u4EE5\u4E0A",value:5}]),N=b(1),j=b("count"),se=b([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),B=b(1),A=b({pageItems:[],targetNumAppearances:new Map,tailNumAppearances:new Map,totalCount:0,totalPages:0}),J=b(!1);ke(async()=>{J.value=!1,ae(),I(),J.value=!0}),ie(()=>N.value,()=>{!J.value||I()}),ie(()=>j.value,()=>{!J.value||I()}),ie(()=>B.value,()=>{!J.value||G()});const ae=()=>{N.value=1,B.value=1},o=b([]),f=v=>{const u=[],a=Array.from(v);for(let i=1;i<=_.maxNumber;i++)a.includes(i)||u.push(i);return u},D=b([]),q=b(new Map),I=()=>{D.value=[],B.value=1,q.value.clear();const v=new Map,u=new Map;for(const m of _.results){let w=m.consecutiveHits>=N.value;if(w&&D.value.push(m),j.value==="count")for(const F of m.targetNumbers){let g=m.consecutiveHits;if(w){v.set(F,(v.get(F)||0)+g);const X=F%10;u.set(X,(u.get(X)||0)+g)}else for(const X of m.targetNumbers)q.value.set(X,(q.value.get(X)||0)+g)}else if(j.value==="group")for(const F of m.targetNumbers)if(w){v.set(F,(v.get(F)||0)+1);const g=F%10;u.set(g,(u.get(g)||0)+1)}else q.value.set(F,(q.value.get(F)||0)+1)}const a=Array.from(v.entries()).sort((m,w)=>w[1]-m[1]);A.value.targetNumAppearances=new Map(a),o.value=f(A.value.targetNumAppearances.keys()),q.value=new Map(Array.from(q.value.entries()).filter(m=>o.value.includes(m[0])).sort((m,w)=>w[1]-m[1]));for(const m of o.value)q.value.has(m)||q.value.set(m,0);const i=Array.from(u.entries()).sort((m,w)=>w[1]-m[1]);A.value.tailNumAppearances=new Map(i),G()},G=()=>{const v=(B.value-1)*_.pageSize,u=v+_.pageSize;A.value.pageItems=D.value.slice(v,u),A.value.totalCount=D.value.length,A.value.totalPages=Math.ceil(D.value.length/_.pageSize)},Q=b([..._.drawResults].reverse()),z=b("asc"),U=v=>{v==="desc"?(z.value="desc",Q.value=[..._.drawResults]):(z.value="asc",Q.value=[..._.drawResults].reverse())},c=v=>{var a,i;if(!((i=(a=_.predictResult)==null?void 0:a.draw_number_size)!=null&&i.length))return!1;const u=[..._.predictResult.draw_number_size];return!_.isSuperLotto&&_.predictResult.special_number&&u.push(_.predictResult.special_number),u.includes(v)},$=v=>{var u;return!((u=_.predictResult)!=null&&u.special_number)||_.isSuperLotto?!1:_.predictResult.special_number===v};return(v,u)=>(t(),x(T,{class:"q-mt-md"},{default:d(()=>[r(W,null,{default:d(()=>[r(ze,{modelValue:y.value,"onUpdate:modelValue":u[0]||(u[0]=a=>y.value=a),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:d(()=>[r(pe,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),r(pe,{name:"2",label:"\u9810\u6E2C\u7D50\u679C"}),r(pe,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),v.predictResult.period?(t(),x(T,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:d(()=>[r(W,null,{default:d(()=>[e("div",je,[e("div",Ge,[u[8]||(u[8]=e("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),e("div",He,"\u7B2C "+l(v.predictResult.period)+" \u671F",1),e("div",We," \u958B\u734E\u65E5\u671F\uFF1A"+l(v.predictResult.draw_date),1)]),e("div",Je,[e("div",Ze,[(t(!0),s(h,null,E(v.predictResult.draw_number_size,a=>(t(),s("div",{key:a,class:"col-auto"},[(t(),s("div",{class:"ball",key:a},l(p(C)(a)),1))]))),128)),v.predictResult.special_number?(t(),s("div",Ke,[(t(),s("div",{class:"ball special-number",key:v.predictResult.special_number},l(p(C)(v.predictResult.special_number)),1))])):S("",!0)])])])]),_:1})]),_:1})):S("",!0),y.value==="1"||y.value==="2"?(t(),s(h,{key:1},[e("div",Xe,[u[9]||(u[9]=e("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),e("div",Ye,[r(Y,{outlined:"",dense:"",modelValue:N.value,"onUpdate:modelValue":u[1]||(u[1]=a=>N.value=a),options:k.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),e("div",et,[r(Y,{outlined:"",dense:"",modelValue:j.value,"onUpdate:modelValue":u[2]||(u[2]=a=>j.value=a),options:se.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),e("div",tt,[e("div",st," \u5171 "+l(A.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)])],64)):S("",!0),r(Le,{modelValue:y.value,"onUpdate:modelValue":u[7]||(u[7]=a=>y.value=a)},{default:d(()=>[r(me,{name:"1"},{default:d(()=>[A.value.totalPages>1?(t(),s("div",lt,[r(ye,{modelValue:B.value,"onUpdate:modelValue":u[3]||(u[3]=a=>B.value=a),max:A.value.totalPages,input:!0},null,8,["modelValue","max"])])):S("",!0),e("div",ut,[A.value.pageItems.length===0?(t(),s("div",at,[r(T,{flat:"",bordered:""},{default:d(()=>[r(W,null,{default:d(()=>u[10]||(u[10]=[e("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):S("",!0),(t(!0),s(h,null,E(A.value.pageItems,(a,i)=>(t(),s("div",{key:i,class:"col-12 q-my-sm"},[r(T,{flat:"",bordered:""},{default:d(()=>[r(W,null,{default:d(()=>[e("div",ot,[e("div",rt,[e("div",null,[u[11]||(u[11]=P(" \u958B\u51FA ")),(t(!0),s(h,null,E(a.firstNumbers,m=>(t(),x(ue,{key:m,color:"primary","text-color":"white",size:"lg",dense:""},{default:d(()=>[P(l(p(C)(m)),1)]),_:2},1024))),128)),P(" \u4E0B"+l(a.gap)+"\u671F\u958B ",1),(t(!0),s(h,null,E(a.secondNumbers,m=>(t(),x(ue,{key:m,color:"secondary","text-color":"white",size:"lg",dense:""},{default:d(()=>[P(l(p(C)(m)),1)]),_:2},1024))),128)),P(" \u518D\u4E0B"+l(a.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(t(!0),s(h,null,E(a.targetNumbers,m=>(t(),x(ue,{key:m,color:"accent","text-color":"white",size:"lg",dense:""},{default:d(()=>[P(l(p(C)(m)),1)]),_:2},1024))),128))])]),e("div",it,[e("div",nt,[e("div",dt," \u5DF2\u9023\u7E8C\u62D6\u51FA"+l(a.consecutiveHits)+"\u6B21 ",1)])]),e("div",ct,[r(ee,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:m=>v.$emit("view-details",a)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),A.value.totalPages>1?(t(),s("div",pt,[r(ye,{modelValue:B.value,"onUpdate:modelValue":u[4]||(u[4]=a=>B.value=a),max:A.value.totalPages,input:!0},null,8,["modelValue","max"])])):S("",!0)]),_:1}),r(me,{name:"2"},{default:d(()=>{var a;return[e("div",mt,[e("label",vt," \u9810\u6E2C\u865F\u78BC\uFF08"+l(((a=Array.from(A.value.targetNumAppearances.keys()))==null?void 0:a.length)||0)+"\u7B46\uFF09 ",1)]),r(T,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:d(()=>[e("div",_t,[(t(!0),s(h,null,E(A.value.targetNumAppearances.keys(),i=>(t(),s("div",{class:M(["col-4 col-md-2",{predict:c(i),"predict-special-number":$(i)}]),key:i},[e("div",bt,[e("span",ft,l(p(C)(i)),1),P(" ("+l(A.value.targetNumAppearances.get(i))+"\u6B21) ",1)])],2))),128))])]),_:1}),e("div",gt,[e("label",yt," \u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u9810\u6E2C\u6B21\u6578\u6392\u5E8F\uFF08"+l(o.value.length)+"\u7B46) ",1)]),r(T,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:d(()=>[e("div",wt,[(t(!0),s(h,null,E(q.value.entries(),([i,m])=>(t(),s("div",{class:M(["col-4 col-md-2",{predict:c(i),"predict-special-number":$(i)}]),key:i},[e("div",ht,[e("span",Ft,l(p(C)(i)),1),P(" ("+l(m)+"\u6B21) ",1)])],2))),128))])]),_:1}),e("div",Bt,[e("label",Ct," \u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u5927\u5C0F\u6392\u5E8F\uFF08"+l(o.value.length)+"\u7B46) ",1)]),r(T,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:d(()=>[e("div",At,[(t(!0),s(h,null,E(o.value,i=>(t(),s("div",{class:M(["col-3 col-md-1",{predict:c(i),"predict-special-number":$(i)}]),key:i},[e("span",Dt,l(p(C)(i)),1)],2))),128))])]),_:1}),u[12]||(u[12]=e("div",{class:"row q-mb-sm"},[e("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u5C3E\u6578\u78BC")],-1)),r(T,{flat:"",bordered:"",class:"q-pa-sm text-subtitle1 appearance"},{default:d(()=>[e("div",kt,[(t(!0),s(h,null,E(A.value.tailNumAppearances.keys(),i=>{var m;return t(),s("div",{class:M(["col-4 col-md-2",{predict:(m=v.predictResult.tailSet)==null?void 0:m.includes(i)}]),key:i},[e("div",qt,[e("span",Et,l(i),1),P(" ("+l(A.value.tailNumAppearances.get(i))+"\u6B21) ",1)])],2)}),128))])]),_:1})]}),_:1}),r(me,{name:"3"},{default:d(()=>[e("div",Nt,[r(Oe,{spread:""},{default:d(()=>[r(ee,{type:"button",icon:"arrow_downward",label:"\u7531\u820A\u5230\u65B0",color:"primary","text-color":"white",class:M({"bg-secondary":z.value==="asc"}),onClick:u[5]||(u[5]=a=>U("asc"))},null,8,["class"]),r(ee,{type:"button",icon:"arrow_upward",label:"\u7531\u65B0\u5230\u820A",color:"primary","text-color":"white",class:M({"bg-secondary":z.value==="desc"}),onClick:u[6]||(u[6]=a=>U("desc"))},null,8,["class"])]),_:1})]),e("div",Pt,[(t(!0),s(h,null,E(Q.value,a=>(t(),x(T,{bordered:"",key:a.period,class:"ball-card full-width q-mb-md"},{default:d(()=>[r(W,{class:"q-py-md"},{default:d(()=>[e("div",xt,[e("div",$t,[e("div",Rt,"\u7B2C "+l(a.period)+" \u671F",1),e("div",Vt," \u958B\u734E\u65E5\u671F\uFF1A"+l(a.draw_date),1)]),e("div",St,[e("div",Qt,[(t(!0),s(h,null,E(a.draw_number_size,i=>(t(),s("div",{key:i,class:"col-auto"},[(t(),s("div",{class:"ball",key:i},l(p(C)(i)),1))]))),128)),a.special_number?(t(),s("div",zt,[(t(),s("div",{class:"ball special-number",key:a.special_number},l(p(C)(a.special_number)),1))])):S("",!0)])])])]),_:2},1024)]),_:2},1024))),128))])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const Mt={class:"text-h6 q-mb-md"},It={class:"text-h6 q-mb-md"},Ut={class:"row text-h6 q-mb-md"},Tt={class:"col-auto"},Ot={class:"row balls"},jt={key:0,class:"col-auto"},Gt={class:"row text-h6 q-mb-md"},Ht={class:"col-auto"},Wt={class:"row balls"},Jt={key:0,class:"col-auto"},Zt={key:0,class:"row text-h6"},Kt={class:"col-auto"},Xt={class:"row balls"},Yt={key:0,class:"col-auto"},es={key:1,class:"predict-section"},ts={class:"row text-h6 q-mt-sm"},ss={class:"col-auto"},ls={key:0,class:"predict-period"},us={class:"predict-date"},as={class:"row balls"},os={key:0,class:"col-auto"},rs={key:1,class:"col-auto pending-draw"},is=ve({__name:"BallFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(te,{emit:_}){const y=te,k=he(),N=we({get:()=>y.modelValue,set:o=>j("update:modelValue",o)}),j=_,se=o=>{if(!o)return"";const f=y.results.find(D=>D.period==o);return f==null?void 0:f.draw_date},B=o=>{if(!o)return{numbers:[],specialNumber:0};const f=y.results.find(D=>D.period==o);return{numbers:f==null?void 0:f.draw_number_size,specialNumber:f!=null&&f.special_number?Number(f.special_number):0}},A=o=>{var z,U,c;if(!o)return[];const f=o.firstNumbers.join(",")+"-"+o.secondNumbers.join(",")+"-"+o.gap+"-"+o.targetGap,D=(U=(z=y.occurrences.get(f))==null?void 0:z.periods)!=null?U:[],q=D.filter($=>$.targetPeriod===void 0||$.targetPeriod===null||$.targetPeriod.trim()===""),I=D.filter($=>$.targetPeriod!==void 0&&$.targetPeriod!==null&&$.targetPeriod.trim()!==""),G=(c=o.consecutiveHits)!=null?c:0;let Q=[];return G>0&&I.length>0&&(Q=I.sort((v,u)=>{const a=parseInt(v.targetPeriod),i=parseInt(u.targetPeriod);return isNaN(a)||isNaN(i)?v.targetPeriod.localeCompare(u.targetPeriod):i-a}).slice(0,G)),q.length>0&&(Q=[...q,...Q]),Q},J=o=>o.targetPeriod===void 0,ae=()=>{j("update:modelValue",!1)};return(o,f)=>(t(),x(qe,{modelValue:N.value,"onUpdate:modelValue":f[0]||(f[0]=D=>N.value=D)},{default:d(()=>[r(T,{style:{"max-width":"100%",width:"800px"}},{default:d(()=>[r(W,{class:"row items-center"},{default:d(()=>[f[1]||(f[1]=e("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),r(Te),r(ee,{icon:"close",flat:"",round:"",dense:"",onClick:ae})]),_:1}),r(W,{class:"q-pa-md"},{default:d(()=>{var D,q,I,G,Q,z,U;return[e("div",Mt,[f[2]||(f[2]=P(" \u958B\u51FA ")),(t(!0),s(h,null,E((D=o.selectedDetail)==null?void 0:D.firstNumbers,c=>(t(),x(ue,{key:c,color:"primary","text-color":"white",class:"text-h6"},{default:d(()=>[P(l(p(C)(c)),1)]),_:2},1024))),128)),P(" \u4E0B"+l((q=o.selectedDetail)==null?void 0:q.gap)+"\u671F\u958B ",1),(t(!0),s(h,null,E((I=o.selectedDetail)==null?void 0:I.secondNumbers,c=>(t(),x(ue,{key:c,color:"secondary","text-color":"white",class:"text-h6"},{default:d(()=>[P(l(p(C)(c)),1)]),_:2},1024))),128)),P(" \u518D\u4E0B "+l((G=o.selectedDetail)==null?void 0:G.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(t(!0),s(h,null,E((Q=o.selectedDetail)==null?void 0:Q.targetNumbers,c=>(t(),x(ue,{key:c,color:"accent","text-color":"white",class:"text-h6"},{default:d(()=>[P(l(p(C)(c)),1)]),_:2},1024))),128))]),e("div",It," \u5DF2\u9023\u7E8C\u62D6\u51FA"+l((U=(z=o.selectedDetail)==null?void 0:z.consecutiveHits)!=null?U:0)+"\u6B21 ",1),e("div",null,[(t(!0),s(h,null,E(A(o.selectedDetail),(c,$)=>(t(),x(T,{key:$,class:"q-mb-md",style:Ee({"background-color":J(c)?"#e8f5e8":"#fefefe"})},{default:d(()=>[r(W,null,{default:d(()=>{var v,u,a,i,m;return[e("div",Ut,[e("div",Tt,[e("div",null,"\u7B2C "+l(c.firstPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+l(se(c.firstPeriod)),1)]),e("div",Ot,[(t(!0),s(h,null,E(B(c.firstPeriod).numbers,(w,F)=>{var g;return t(),s("div",{class:"col-auto",key:F},[e("div",{class:M(["ball",{"first-num":(g=o.selectedDetail)==null?void 0:g.firstNumbers.includes(w)}])},l(p(C)(w)),3)])}),128)),B(c.firstPeriod).specialNumber?(t(),s("div",jt,[e("div",{class:M(["ball special-number",{"first-num":((v=o.selectedDetail)==null?void 0:v.firstNumbers.includes(B(c.firstPeriod).specialNumber))&&!p(k).isSuperLotto}])},l(p(C)(B(c.firstPeriod).specialNumber)),3)])):S("",!0)])]),e("div",Gt,[e("div",Ht,[e("div",null,"\u7B2C "+l(c.secondPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+l(se(c.secondPeriod)),1)]),e("div",Wt,[(t(!0),s(h,null,E(B(c.secondPeriod).numbers,(w,F)=>{var g;return t(),s("div",{class:"col-auto",key:F},[e("div",{class:M(["ball",{"second-num":(g=o.selectedDetail)==null?void 0:g.secondNumbers.includes(w)}])},l(p(C)(w)),3)])}),128)),B(c.secondPeriod).specialNumber?(t(),s("div",Jt,[e("div",{class:M(["ball special-number",{"second-num":((u=o.selectedDetail)==null?void 0:u.secondNumbers.includes(B(c.secondPeriod).specialNumber))&&!p(k).isSuperLotto}])},l(p(C)(B(c.secondPeriod).specialNumber)),3)])):S("",!0)])]),c.targetPeriod?(t(),s("div",Zt,[e("div",Kt,[e("div",null,"\u7B2C "+l(c.targetPeriod)+" \u671F",1),e("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+l((a=se(c.targetPeriod))!=null?a:"\u672A\u958B\u734E"),1)]),e("div",Xt,[(t(!0),s(h,null,E(B(c.targetPeriod).numbers,(w,F)=>{var g;return t(),s("div",{class:"col-auto",key:F},[e("div",{class:M(["ball",{"target-num":(g=o.selectedDetail)==null?void 0:g.targetNumbers.includes(w)}])},l(p(C)(w)),3)])}),128)),B(c.targetPeriod).specialNumber?(t(),s("div",Yt,[e("div",{class:M(["ball special-number",{"target-num":((i=o.selectedDetail)==null?void 0:i.targetNumbers.includes(B(c.targetPeriod).specialNumber))&&!p(k).isSuperLotto}])},l(p(C)(B(c.targetPeriod).specialNumber)),3)])):S("",!0)])])):(t(),s("div",es,[e("div",ts,[e("div",ss,[o.predictResult.period?(t(),s("div",ls," \u7B2C "+l(o.predictResult.period)+" \u671F ",1)):S("",!0),e("div",us,[o.predictResult.period?(t(),s(h,{key:0},[P(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+l(o.predictResult.draw_date),1)],64)):(t(),s(h,{key:1},[P(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),e("div",as,[o.predictResult.period?(t(),s(h,{key:0},[(t(!0),s(h,null,E(o.predictResult.draw_number_size,(w,F)=>{var g;return t(),s("div",{class:"col-auto",key:F},[e("div",{class:M(["ball",{"target-num":(g=o.selectedDetail)==null?void 0:g.targetNumbers.includes(w)}])},l(p(C)(w)),3)])}),128)),o.predictResult.special_number?(t(),s("div",os,[e("div",{class:M(["ball special-number",{"target-num":((m=o.selectedDetail)==null?void 0:m.targetNumbers.includes(o.predictResult.special_number))&&!p(k).isSuperLotto}])},l(p(C)(o.predictResult.special_number)),3)])):S("",!0)],64)):(t(),s("div",rs,[r(Ne,{name:"schedule",size:"lg"}),f[3]||(f[3]=e("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var ns=Fe(is,[["__scopeId","data-v-21f33910"]]);const ds={class:"row lto-ref q-mb-sm"},cs={class:"col-12 col-sm-4 self-center text-h6"},ps={class:"col-12 col-sm-6 self-center text-subtitle1"},ms={class:"row balls"},vs={class:"ball"},_s={key:0,class:"col-auto"},bs={class:"row q-mb-md"},fs={class:"col"},gs={key:1,class:"row q-mb-md"},ys={class:"row q-mb-md"},ws={class:"col-12 col-sm-4 q-pa-sm"},hs={class:"col-12 col-sm-4 q-pa-sm"},Fs={class:"col-12 col-sm-4 q-pa-sm"},Bs={class:"row q-mb-md"},Cs={class:"col-12 col-sm-4"},As={class:"q-pa-sm"},Ds={class:"col-12 col-sm-4"},ks={class:"q-pa-sm"},qs={class:"col-12 col-sm-4"},Es={class:"q-pa-sm"},Ns={class:"text-center q-mb-sm"},Ps=ve({__name:"BallFollowPage",setup(te){const _=Me(),y=he(),k=b(y.getLotto),N=b(!1),j=()=>{N.value=!0},se=()=>{N.value=!1},B=()=>{N.value=!1};ie(()=>y.getLotto,R=>{R&&(k.value=R)}),ie(()=>{var R;return(R=k.value)==null?void 0:R.period},()=>{F.value=[]});const{analyzeWithProgress:A,stopAnalyzer:J,setConfig:ae,setResults:o}=Ue(),f=b([]),D=b(new Map),q=b(!1),I=b(null),G=R=>{I.value=R,q.value=!0},Q=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],z=b(1),U=b(1),c=b(1);let $=b(Array.from({length:991},(R,n)=>({label:`${n+10}\u671F`,value:n+10})));const v=b(50),u=(R,n,Z)=>{const K=parseInt(R,10);(K<10||K>1e3)&&Z(),n(()=>{$.value=Array.from({length:991},(H,le)=>le+10).filter(H=>H.toString().startsWith(R)).map(H=>({label:`${H.toString()}\u671F`,value:H}))})},a=Array.from({length:21},(R,n)=>({label:`${n+10}\u671F`,value:n+10})),i=b(20),m=Array.from({length:15},(R,n)=>({label:`\u4E0B${n+1}\u671F`,value:n+1})),w=b(1),F=b([]),g=b({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map});let X=1;const _e=b(49),ce=b(!1),Be=async()=>{var R,n,Z;try{_.startCalculating(),f.value=[],X=w.value,_e.value=y.getMaxNumber,ce.value=y.isSuperLotto;const K=await ge.getLottoList({draw_type:y.getDrawType,date_end:(n=(R=k.value)==null?void 0:R.draw_date)!=null?n:"",limit:v.value});F.value=K.data;const H=await ge.getLottoPredict({draw_type:y.getDrawType,draw_date:(Z=y.getLotto)==null?void 0:Z.draw_date,ahead_count:X});if(g.value=H.data,g.value.period){const L=new Set,O=[...g.value.draw_number_size];!ce.value&&g.value.special_number&&O.push(g.value.special_number);for(const de of O)L.add(de%10);const V=Array.from(L).sort((de,fe)=>de===0?1:fe===0?-1:de-fe);g.value.tailSet=V}const le=F.value.map(L=>{const O=[...L.draw_number_size];return L.special_number&&!y.isSuperLotto&&O.push(L.special_number),{numbers:[...O],period:String(L.period)}}).reverse();o(le),ae({firstGroupSize:z.value,secondGroupSize:U.value,targetGroupSize:c.value,maxRange:i.value,lookAheadCount:w.value});let oe=Date.now();const ne=8,re=await A(async L=>{const O=Date.now();O-oe>=ne&&(await _.updateProgress(L),oe=O)},L=>{_.addWarning(L)});f.value=re.data,D.value=re.occurrences}catch(K){console.error("\u6578\u64DA\u8F09\u5165\u5931\u6557:",K)}finally{be()}},be=()=>{J(),_.stopCalculating()};return(R,n)=>(t(),x(Qe,{class:"justify-center"},{default:d(()=>[r(T,null,{default:d(()=>[r(W,null,{default:d(()=>{var Z,K,H,le,oe,ne,re,L,O;return[(Z=p(y).getLotto)!=null&&Z.draw_date?(t(),s(h,{key:0},[e("div",ds,[e("div",cs,[e("div",null,l(p(y).getDrawLabel),1),n[7]||(n[7]=e("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),e("span",null,l((K=k.value)==null?void 0:K.period),1),e("span",null,"\uFF08"+l((H=k.value)==null?void 0:H.draw_date)+"\uFF09",1)]),e("div",ps,[e("div",ms,[(t(!0),s(h,null,E((le=k.value)==null?void 0:le.draw_number_size,V=>(t(),s("div",{class:"col-auto",key:V},[e("div",vs,l(p(C)(V)),1)]))),128)),(oe=k.value)!=null&&oe.special_number?(t(),s("div",_s,[(t(),s("div",{class:"ball special-number",key:(ne=k.value)==null?void 0:ne.special_number},l(p(C)((re=k.value)==null?void 0:re.special_number)),1))])):S("",!0)])])]),e("div",bs,[e("div",fs,[N.value?(t(),x(ee,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:B})):(t(),x(ee,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:j}))])])],64)):(t(),s("div",gs,n[8]||(n[8]=[e("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),r(Pe,{class:"q-mb-md"}),!N.value&&((L=p(y).getLotto)==null?void 0:L.draw_date)?(t(),s(h,{key:2},[n[14]||(n[14]=e("div",{class:"row q-mb-md"},[e("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7248\u8DEF\u5206\u6790\u8A2D\u5B9A ")],-1)),e("div",ys,[n[9]||(n[9]=e("div",{class:"col-12 text-h6 text-weight-bold"},"\u62D6\u724C\u7D44\u5408",-1)),e("div",ws,[r(Y,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":n[0]||(n[0]=V=>z.value=V),options:Q,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",hs,[r(Y,{outlined:"",dense:"",modelValue:U.value,"onUpdate:modelValue":n[1]||(n[1]=V=>U.value=V),options:Q,"emit-value":"","map-options":""},null,8,["modelValue"])]),e("div",Fs,[r(Y,{outlined:"",dense:"",modelValue:c.value,"onUpdate:modelValue":n[2]||(n[2]=V=>c.value=V),options:Q,"emit-value":"","map-options":""},null,8,["modelValue"])])]),e("div",Bs,[e("div",Cs,[n[11]||(n[11]=e("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),e("div",As,[r(Y,{outlined:"",dense:"",modelValue:v.value,"onUpdate:modelValue":n[3]||(n[3]=V=>v.value=V),options:p($),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:u,"emit-value":"","map-options":""},{"no-option":d(()=>[r($e,null,{default:d(()=>[r(Re,{class:"text-grey"},{default:d(()=>n[10]||(n[10]=[P(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),e("div",Ds,[n[12]||(n[12]=e("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),e("div",ks,[r(Y,{outlined:"",dense:"",modelValue:i.value,"onUpdate:modelValue":n[4]||(n[4]=V=>i.value=V),options:p(a),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),e("div",qs,[n[13]||(n[13]=e("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),e("div",Es,[r(Y,{outlined:"",dense:"",modelValue:w.value,"onUpdate:modelValue":n[5]||(n[5]=V=>w.value=V),options:p(m),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),r(xe,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:d(()=>[p(_).isCalculating?(t(),x(ee,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",onClick:be,class:"text-h6 q-mr-md"})):S("",!0),r(ee,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:Be,loading:p(_).isCalculating},{loading:d(()=>[r(Ve)]),_:1},8,["loading"])]),_:1})],64)):(t(),x(Ie,{key:3,"draw-type-query":p(y).drawType,"date-query":((O=k.value)==null?void 0:O.draw_date)||"","is-select-ref":!0,onSelectRef:se},null,8,["draw-type-query","date-query"]))]}),_:1}),p(_).isCalculating?(t(),x(W,{key:0},{default:d(()=>[e("div",Ns,l(p(_).progressMessage),1),r(Se,{rounded:"",size:"md",value:p(_).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):S("",!0)]),_:1}),!p(_).isCalculating&&F.value.length>0?(t(),x(Lt,{key:0,"max-number":_e.value,"is-super-lotto":ce.value,"draw-results":F.value,"predict-result":g.value,results:f.value,"occurrence-results":D.value,"page-size":50,onViewDetails:G},null,8,["max-number","is-super-lotto","draw-results","predict-result","results","occurrence-results"])):S("",!0),r(ns,{modelValue:q.value,"onUpdate:modelValue":n[6]||(n[6]=Z=>q.value=Z),results:F.value,"selected-detail":I.value,occurrences:D.value,"predict-result":g.value},null,8,["modelValue","results","selected-detail","occurrences","predict-result"])]),_:1}))}});var Xs=Fe(Ps,[["__scopeId","data-v-d50c406a"]]);export{Xs as default};
