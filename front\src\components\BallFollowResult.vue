<template>
  <q-card class="q-mt-md">
    <q-card-section>
      <!-- Tabs -->
      <q-tabs
        v-model="tab"
        dense
        align="justify"
        class="text-h6"
        active-color="primary"
        indicator-color="primary"
      >
        <q-tab name="1" label="分析結果" />
        <q-tab name="2" label="預測結果" />
        <q-tab name="3" label="開獎結果" />
      </q-tabs>

      <!-- 預測結果 -->
      <q-card
        bordered
        class="ball-card full-width q-my-lg"
        v-if="predictResult.period"
      >
        <q-card-section>
          <div class="row q-gutter-y-md">
            <div class="col-12 col-sm-5 draw-title text-center">
              <div class="text-h6">預測開獎結果</div>
              <div class="text-period">第 {{ predictResult.period }} 期</div>
              <div class="text-draw-date">
                開獎日期：{{ predictResult.draw_date }}
              </div>
            </div>

            <div class="col-12 col-sm-7 self-center">
              <div class="row justify-center">
                <template
                  v-for="number in predictResult.draw_number_size"
                  :key="number"
                >
                  <div class="col-auto">
                    <div class="ball" :key="number">
                      {{ paddingZero(number) }}
                    </div>
                  </div>
                </template>
                <div class="col-auto" v-if="predictResult.special_number">
                  <div
                    class="ball special-number"
                    :key="predictResult.special_number"
                  >
                    {{ paddingZero(predictResult.special_number) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Filter -->
      <template v-if="tab === '1' || tab === '2'">
        <!-- 篩選 -->
        <div class="row q-my-md q-gutter-sm items-center">
          <div class="col-sm-auto text-h6">篩選</div>
          <!-- 準確次數 -->
          <div class="col-sm-auto">
            <q-select
              outlined
              dense
              v-model="accuracy"
              :options="accuracyOpts"
              map-options
              emit-value
            />
          </div>
          <!-- 準確率 -->
          <div class="col-sm-auto">
            <q-select
              outlined
              dense
              v-model="accuracyRate"
              :options="accuracyRateOpts"
              map-options
              emit-value
            />
          </div>
          <!-- 統計方式 -->
          <div class="col-sm-auto">
            <q-select
              outlined
              dense
              v-model="statisticType"
              :options="statisticTypeOpts"
              map-options
              emit-value
            />
          </div>
        </div>
        <!-- 資料筆數 -->
        <div class="row">
          <div class="col-auto text-h6">
            共 {{ paginatedResults.totalCount }} 筆資料
          </div>
        </div>
      </template>

      <q-tab-panels v-model="tab">
        <!-- 分析結果 -->
        <q-tab-panel name="1">
          <!-- 上方分頁 -->
          <div
            class="row q-my-md justify-center"
            v-if="paginatedResults.totalPages > 1"
          >
            <q-pagination
              v-model="currentPage"
              :max="paginatedResults.totalPages"
              :input="true"
            />
          </div>

          <!-- 資料顯示 -->
          <div class="row q-col-gutter-md">
            <!-- 無符合條件結果提示 -->
            <div
              class="col-12 q-my-sm"
              v-if="paginatedResults.pageItems.length === 0"
            >
              <q-card flat bordered>
                <q-card-section>
                  <div class="text-h6 text-center">沒有符合條件的結果</div>
                </q-card-section>
              </q-card>
            </div>

            <!-- 統計資料 -->
            <template
              v-for="(stat, index) in paginatedResults.pageItems"
              :key="index"
            >
              <div class="col-12 q-my-sm">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="row q-col-gutter-md items-center text-h6">
                      <!-- 開出 -->
                      <div class="col-12 col-sm-4">
                        <div>
                          開出
                          <q-chip
                            v-for="num in stat.firstNumbers"
                            :key="num"
                            color="primary"
                            text-color="white"
                            size="lg"
                            dense
                          >
                            {{ paddingZero(num) }}
                          </q-chip>
                          下{{ stat.gap }}期開
                          <q-chip
                            v-for="num in stat.secondNumbers"
                            :key="num"
                            color="secondary"
                            text-color="white"
                            size="lg"
                            dense
                          >
                            {{ paddingZero(num) }}
                          </q-chip>
                          再下{{ stat.targetGap }}期預測拖出
                          <q-chip
                            v-for="num in stat.targetNumbers"
                            :key="num"
                            color="accent"
                            text-color="white"
                            size="lg"
                            dense
                          >
                            {{ paddingZero(num) }}
                          </q-chip>
                        </div>
                      </div>
                      <!-- 數據 -->
                      <div class="col-12 col-sm-6">
                        <div class="row q-col-gutter-md">
                          <div class="col-auto">
                            已連續拖出{{ stat.consecutiveHits }}次
                          </div>
                        </div>
                      </div>
                      <!-- 檢視詳細 -->
                      <div class="col-12 col-sm-2 text-center">
                        <q-btn
                          dense
                          color="primary"
                          icon="visibility"
                          label="檢視詳情"
                          class="text-subtitle1 text-bold"
                          @click="$emit('view-details', stat)"
                        />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </template>
          </div>

          <!-- 下方分頁 -->
          <div
            class="row justify-center"
            v-if="paginatedResults.totalPages > 1"
          >
            <q-pagination
              v-model="currentPage"
              :max="paginatedResults.totalPages"
              :input="true"
            />
          </div>
        </q-tab-panel>

        <!-- 預測結果 -->
        <q-tab-panel name="2">
          <!-- 預測結果 -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold">
              預測號碼（{{
                Array.from(paginatedResults.targetNumAppearances.keys())
                  ?.length || 0
              }}筆）
            </label>
          </div>
          <q-card
            flat
            bordered
            class="q-pa-sm q-mb-md text-subtitle1 appearance"
          >
            <div class="row q-col-gutter-sm">
              <div
                class="col-4 col-md-2"
                :class="{
                  predict: isPredictNumber(number),
                  'predict-special-number': isPredictSpecialNumber(number),
                }"
                v-for="number in paginatedResults.targetNumAppearances.keys()"
                :key="number"
              >
                <div class="row items-center">
                  <span class="ball">{{ paddingZero(number) }}</span>
                  ({{ paginatedResults.targetNumAppearances.get(number) }}次)
                </div>
              </div>
            </div>
          </q-card>
          <!-- 未出現號碼(依出現次數排序) -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold">
              未出現號碼 - 依預測次數排序（{{ nonAppearedNumbers.length }}筆)
            </label>
          </div>
          <q-card
            flat
            bordered
            class="q-pa-sm q-mb-md text-subtitle1 appearance"
          >
            <div class="row q-col-gutter-sm">
              <div
                class="col-4 col-md-2"
                :class="{
                  predict: isPredictNumber(number),
                  'predict-special-number': isPredictSpecialNumber(number),
                }"
                v-for="[number, count] in nonResultFrequencyMap.entries()"
                :key="number"
              >
                <div class="row items-center">
                  <span class="ball">{{ paddingZero(number) }}</span>
                  ({{ count }}次)
                </div>
              </div>
            </div>
          </q-card>

          <!-- 未出現號碼(依大小排序) -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold">
              未出現號碼 - 依大小排序（{{ nonAppearedNumbers.length }}筆)
            </label>
          </div>
          <q-card
            flat
            bordered
            class="q-pa-sm q-mb-md text-subtitle1 appearance"
          >
            <div class="row q-col-gutter-sm">
              <div
                class="col-3 col-md-1"
                :class="{
                  predict: isPredictNumber(number),
                  'predict-special-number': isPredictSpecialNumber(number),
                }"
                v-for="number in nonAppearedNumbers"
                :key="number"
              >
                <span class="ball">
                  {{ paddingZero(number) }}
                </span>
              </div>
            </div>
          </q-card>
          <!-- 尾數碼出現次數 -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold">預測尾數碼</label>
          </div>
          <q-card flat bordered class="q-pa-sm text-subtitle1 appearance">
            <div class="row q-col-gutter-xs">
              <div
                class="col-4 col-md-2"
                :class="{ predict: predictResult.tailSet?.includes(tailNum) }"
                v-for="tailNum in paginatedResults.tailNumAppearances.keys()"
                :key="tailNum"
              >
                <div class="row items-center">
                  <span class="ball tail-number">{{ tailNum }}</span>
                  ({{ paginatedResults.tailNumAppearances.get(tailNum) }}次)
                </div>
              </div>
            </div>
          </q-card>
        </q-tab-panel>

        <!-- 開獎結果 -->
        <q-tab-panel name="3">
          <div class="row q-mt-md">
            <!-- 排序 -->
            <q-btn-group spread>
              <q-btn
                type="button"
                icon="arrow_downward"
                label="由舊到新"
                color="primary"
                text-color="white"
                :class="{ 'bg-secondary': resultSortType === 'asc' }"
                @click="sortDrawResults('asc')"
              />
              <q-btn
                type="button"
                icon="arrow_upward"
                label="由新到舊"
                color="primary"
                text-color="white"
                :class="{ 'bg-secondary': resultSortType === 'desc' }"
                @click="sortDrawResults('desc')"
              />
            </q-btn-group>
          </div>
          <div class="row q-mt-xl">
            <q-card
              bordered
              v-for="item in sortedResults"
              :key="item.period"
              class="ball-card full-width q-mb-md"
            >
              <q-card-section class="q-py-md">
                <div class="row q-gutter-y-md">
                  <div class="col-12 col-sm-4 draw-title text-center">
                    <div class="text-period">第 {{ item.period }} 期</div>
                    <div class="text-draw-date">
                      開獎日期：{{ item.draw_date }}
                    </div>
                  </div>

                  <div class="col-12 col-sm-6 self-center">
                    <div class="row justify-center">
                      <template
                        v-for="number in item.draw_number_size"
                        :key="number"
                      >
                        <div class="col-auto">
                          <div class="ball" :key="number">
                            {{ paddingZero(number) }}
                          </div>
                        </div>
                      </template>
                      <div class="col-auto" v-if="item.special_number">
                        <div
                          class="ball special-number"
                          :key="item.special_number"
                        >
                          {{ paddingZero(item.special_number) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </q-card-section>
  </q-card>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { Occurrence, StatResult } from '@/models/types';
import { LottoItem } from '@/api/modules/lotto';
import { paddingZero } from '@/utils';

const props = defineProps<{
  maxNumber: number;
  isSuperLotto: boolean;
  drawResults: LottoItem[];
  predictResult: LottoItem;
  results: StatResult[];
  occurrenceResults: Map<string, Occurrence>;
  pageSize: number;
}>();

// Tab
const tab = ref('1');

// 連續拖出次數
const accuracyOpts = ref([
  { label: '已連續拖出 1 次以上', value: 1 },
  { label: '已連續拖出 2 次以上', value: 2 },
  { label: '已連續拖出 3 次以上', value: 3 },
  { label: '已連續拖出 4 次以上', value: 4 },
  { label: '已連續拖出 5 次以上', value: 5 },
]);

const accuracy = ref(1);

const accuracyRate = ref(0.5);
const accuracyRateOpts = ref([
  { label: '準確率 100%', value: 1 },
  { label: '準確率 90% 以上', value: 0.9 },
  { label: '準確率 80% 以上', value: 0.8 },
  { label: '準確率 70% 以上', value: 0.7 },
  { label: '準確率 60% 以上', value: 0.6 },
  { label: '準確率 50% 以上', value: 0.5 },
]);


const statisticType = ref('count');
const statisticTypeOpts = ref([
  { label: '準確次數統計', value: 'count' },
  { label: '預測組數統計', value: 'group' },
]);


defineEmits(['view-details']);

const currentPage = ref(1);
const paginatedResults = ref({
  pageItems: [] as StatResult[],
  targetNumAppearances: new Map(),
  tailNumAppearances: new Map(),
  totalCount: 0,
  totalPages: 0,
});

const isInit = ref(false);
onMounted(async () => {
  isInit.value = false;
  init();

  filterResults();
  isInit.value = true;
});

watch(() => accuracy.value, () => {
  if (!isInit.value) return;

  filterResults();
});

watch(() => accuracyRate.value, () => {
  if (!isInit.value) return;

  filterResults();
});

watch(() => statisticType.value, () => {
  if (!isInit.value) return;

  filterResults();
});

watch(() => currentPage.value, () => {
  if (!isInit.value) return;

  pageChange();
});

const init = () => {
  accuracy.value = 1;
  currentPage.value = 1;
};

// const getStatOccurrence = (stat: StatResult) => {
//   const key =
//     stat.firstNumbers.join(',') +
//     '-' +
//     stat.secondNumbers.join(',') +
//     '-' +
//     stat.gap +
//     '-' +
//     stat.targetGap;

//   return props.occurrenceResults.get(key)?.count || 0;
// };

// 未出現號碼
const nonAppearedNumbers = ref<number[]>([]);
const getNonAppearedNumbers = (appeared: Iterable<number>) => {
  const nonAppeared = [];
  const appearedArray = Array.from(appeared);

  for (let i = 1; i <= props.maxNumber; i++) {
    if (!appearedArray.includes(i)) {
      nonAppeared.push(i);
    }
  }

  return nonAppeared;
};

const currentResults = ref<StatResult[]>([]);
const nonResultFrequencyMap = ref(new Map<number, number>());
const filterResults = () => {
  currentResults.value = [];
  currentPage.value = 1;
  nonResultFrequencyMap.value.clear();

  const frequencyMap = new Map<number, number>();
  const tailMap = new Map<number, number>();

  for (const result of props.results) {
    let isFilter = result.consecutiveHits >= accuracy.value;
    if (isFilter) {
      currentResults.value.push(result);
    }

    if (statisticType.value === 'count') {
      for (const number of result.targetNumbers) {
        let count = result.consecutiveHits;
        if (isFilter) {
          frequencyMap.set(number, (frequencyMap.get(number) || 0) + count);

          const tail = number % 10;
          tailMap.set(tail, (tailMap.get(tail) || 0) + count);
        } else {
          for (const number of result.targetNumbers) {
            nonResultFrequencyMap.value.set(
              number,
              (nonResultFrequencyMap.value.get(number) || 0) + count
            );
          }
        }
      }
    } else if (statisticType.value === 'group') {
      for (const number of result.targetNumbers) {
        if (isFilter) {
          frequencyMap.set(number, (frequencyMap.get(number) || 0) + 1);

          const tail = number % 10;
          tailMap.set(tail, (tailMap.get(tail) || 0) + 1);
        } else {
          nonResultFrequencyMap.value.set(
            number,
            (nonResultFrequencyMap.value.get(number) || 0) + 1
          );
        }
      }
    }
  }

  // for (const result of results.value) {
  //   for (const num of result.targetNumbers) {
  //     frequencyMap.set(num, (frequencyMap.get(num) || 0) + 1);

  //     const tail = num % 10;
  //     tailMap.set(tail, (tailMap.get(tail) || 0) + 1);
  //   }
  // }

  // 將 Map 轉換為陣列並依照出現次數排序（由大到小）
  const sortedEntries = Array.from(frequencyMap.entries()).sort(
    (a, b) => b[1] - a[1]
  );
  // 轉回 Map（如果你還需要以 Map 儲存）
  paginatedResults.value.targetNumAppearances = new Map(sortedEntries);

  // 未出現號碼
  nonAppearedNumbers.value = getNonAppearedNumbers(
    paginatedResults.value.targetNumAppearances.keys()
  );
  nonResultFrequencyMap.value = new Map(
    Array.from(nonResultFrequencyMap.value.entries())
    .filter(entry => nonAppearedNumbers.value.includes(entry[0]))
    .sort(
      (a, b) => b[1] - a[1]
    )
  );
  for (const number of nonAppearedNumbers.value) {
    if (!nonResultFrequencyMap.value.has(number)) {
      nonResultFrequencyMap.value.set(number, 0);
    }
  }

  const sortedTailEntries = Array.from(tailMap.entries()).sort(
    (a, b) => b[1] - a[1]
  );
  paginatedResults.value.tailNumAppearances = new Map(sortedTailEntries);

  pageChange();
};

const pageChange = () => {
  const start = (currentPage.value - 1) * props.pageSize;
  const end = start + props.pageSize;
  paginatedResults.value.pageItems = currentResults.value.slice(start, end);
  paginatedResults.value.totalCount = currentResults.value.length;
  paginatedResults.value.totalPages = Math.ceil(
    currentResults.value.length / props.pageSize
  );
};

// 開獎結果排序
const sortedResults = ref<LottoItem[]>([...props.drawResults].reverse());
const resultSortType = ref('asc');
const sortDrawResults = (order: 'asc' | 'desc') => {
  if (order === 'desc') {
    resultSortType.value = 'desc';
    sortedResults.value = [...props.drawResults];
  } else {
    resultSortType.value = 'asc';
    sortedResults.value = [...props.drawResults].reverse();
  }
};

const isPredictNumber = (number: number) => {
  if (!props.predictResult?.draw_number_size?.length) {
    return false;
  }

  const predictNumbers = [...props.predictResult.draw_number_size];
  if (!props.isSuperLotto && props.predictResult.special_number) {
    predictNumbers.push(props.predictResult.special_number);
  }

  return predictNumbers.includes(number);
};

// 是否是預測獎號的特別號
const isPredictSpecialNumber = (number: number) => {
  if (!props.predictResult?.special_number || props.isSuperLotto) {
    return false;
  }

  return props.predictResult.special_number === number;
};
</script>
