var w=Object.defineProperty;var b=(t,s,e)=>s in t?w(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e;var o=(t,s,e)=>(b(t,typeof s!="symbol"?s+"":s,e),e);import{r as f,c as z}from"./index.43a5c8e9.js";class C{constructor(s){o(this,"worker",null);o(this,"isAnalyzing",!1);o(this,"warningCallback",null);o(this,"progressCallback",null);this.drawResults=s}async analyzeBatch(s){if(this.isAnalyzing)throw new Error("Analysis is already in progress");return this.isAnalyzing=!0,new Promise((e,n)=>{try{this.worker=new Worker("/assets/analyzer.worker.2045078f.js",{type:"module"}),this.worker.onmessage=i=>{const{type:u,data:a,occurrences:g,matchData:k}=i.data;switch(u){case"progress":this.progressCallback&&this.progressCallback(a);break;case"warning":this.warningCallback&&this.warningCallback(a),console.warn("Worker \u8B66\u544A:",a);break;case"log":console.log(a);break;case"debug":console.log("\u8A08\u7B97\u7D71\u8A08\u4FE1\u606F:",a);break;case"complete":this.progressCallback&&this.progressCallback(a),this.cleanupWorker(),this.isAnalyzing=!1,e({data:a,occurrences:g,matchData:k});break;case"error":this.cleanupWorker(),this.isAnalyzing=!1,n(new Error(a.message));break}},this.worker.postMessage({type:"init",data:{results:JSON.stringify(this.drawResults),config:JSON.stringify(s)}})}catch(i){this.cleanupWorker(),this.isAnalyzing=!1,n(i)}})}setProgressCallback(s){s&&(this.progressCallback=s)}setWarningCallback(s){this.warningCallback=s}cleanupWorker(){this.worker&&(this.worker.terminate(),this.worker=null)}stopWorker(){this.cleanupWorker(),this.isAnalyzing=!1}}const W=t=>{const s=f(t||[]),e=f({firstGroupSize:1,secondGroupSize:1,targetGroupSize:1,maxRange:20,lookAheadCount:1}),n=z(()=>new C(s.value));return{drawResults:s,analysisConfig:e,analyzeWithProgress:async(r,l)=>(n.value.setProgressCallback(r),l&&n.value.setWarningCallback(l),n.value.analyzeBatch(e.value)),stopAnalyzer:()=>{n.value.stopWorker()},init:(r,l)=>{e.value={...e.value,...r},s.value=l},setConfig:r=>{e.value={...e.value,...r}},setResults:r=>{s.value=r},getTailSet:(r,l)=>{const h=new Set,p=[...r.draw_number_size];!l&&r.special_number&&p.push(r.special_number);for(const c of p)h.add(c%10);return Array.from(h).sort((c,y)=>c===0?1:y===0?-1:c-y)}}};export{W as u};
