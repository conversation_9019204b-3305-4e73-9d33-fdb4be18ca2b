<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <div class="text-h4 text-center q-mb-lg">分析報表</div>

        <!-- 分析方法選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6">分析方法</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedMethod"
                :options="analysisMethodOptions"
                map-options
                emit-value
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 彩種選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">彩種選擇</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedLottoType"
                :options="lottoTypeOptions"
                emit-value
                map-options
                @update:model-value="onLottoTypeChange"
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">參考日期</div>
            <div class="q-pa-sm">
              <q-input
                outlined
                dense
                v-model="referenceDate"
                type="date"
                mask="YYYY/MM/DD"
                :max="maxDate"
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 動態參數設定區域 -->
        <div class="row q-mb-lg" v-if="selectedMethod">
          <div class="col-12">
            <div class="text-h6 text-weight-bold q-mb-md">參數設定</div>

            <!-- 版路分析參數 -->
            <template v-if="selectedMethod === 'ball-follow'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="ballFollowParams.num3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="ballFollowParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="ballFollowParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="ballFollowParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>

              <!-- 篩選條件 -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">準確次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="accuracy"
                      :options="accuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">準確率</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="accuracyRate"
                      :options="accuracyRateOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 尾數分析參數 -->
            <template v-if="selectedMethod === 'tail'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="tailParams.num3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="tailParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="tailParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="tailParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 綜合分析參數 -->
            <template v-if="selectedMethod === 'pattern'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">版路拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.comb3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="patternParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="patternParams.period"
                      :options="periodOptions"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="patternParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="patternParams.ahead"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
          <q-btn
            type="button"
            label="中斷計算"
            color="negative"
            @click="stopBatchAnalysis"
            class="text-h6 q-mr-md"
            v-if="isCalculating"
          />
          <q-btn
            type="button"
            label="產生報表"
            color="positive"
            class="text-h6 q-px-lg q-py-sm"
            @click="startBatchAnalysis"
            :loading="isCalculating"
            :disable="!canStartAnalysis"
          >
            <template v-slot:loading>
              <q-spinner-dots />
            </template>
          </q-btn>
        </q-card-actions>
      </q-card-section>

      <!-- 進度條 -->
      <q-card-section v-if="isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <!-- 結果顯示區域 -->
    <q-card v-if="batchResults.length > 0" class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 text-weight-bold q-mb-md">分析結果</div>
        <div class="row q-mb-md">
          <div class="col-12 col-sm-6">
            <div class="text-subtitle1">
              分析方法：{{ getMethodName(selectedMethod) }}
            </div>
            <div class="text-subtitle1">
              彩種：{{ getLottoTypeName(selectedLottoType) }}
            </div>
          </div>
          <div class="col-12 col-sm-6 text-right">
            <q-btn
              color="primary"
              icon="download"
              :label="`下載 ${outputFormat.toUpperCase()} 檔案`"
              @click="downloadResults"
              :disable="batchResults.length === 0"
            />
          </div>
        </div>


      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Notify } from 'quasar';
import * as XLSX from 'xlsx';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { useLotteryAnalysis } from '@/composables/useLotteryAnalysis';
import { handleError } from '@/utils';
import { AnalysisParameters, BallFollowParameters, TailParameters } from '@/models/batch-analysis';
import { StatResult, Occurrence } from '@/models/types';

const analysis = useLotteryAnalysis();

// 分析方法選項
const analysisMethodOptions = [
  { label: '版路分析', value: 'ball-follow' },
  // { label: '尾數分析', value: 'tail' },
  // { label: '綜合分析', value: 'pattern' }
];

// 彩種選項
const lottoTypeOptions = [
  { label: '威力彩', value: 'super_lotto638' },
  { label: '大樂透', value: 'lotto649' },
  { label: '今彩539', value: 'daily539' },
  { label: '六合彩', value: 'lotto_hk' }
];

// 響應式數據
const selectedMethod = ref('ball-follow');
const selectedLottoType = ref('super_lotto638');
const referenceDate = ref('');
const outputFormat = ref('excel');
const isCalculating = ref(false);
const progress = ref(0);
const progressMessage = ref('');
const batchResults = ref<BatchAnalysisResult[]>([]);

// 篩選條件
const accuracy = ref(1);
const accuracyOpts = ref([
  { label: '已連續命中 1 次以上', value: 1 },
  { label: '已連續命中 2 次以上', value: 2 },
  { label: '已連續命中 3 次以上', value: 3 },
  { label: '已連續命中 4 次以上', value: 4 },
  { label: '已連續命中 5 次以上', value: 5 },
]);

const accuracyRate = ref(0.5);
const accuracyRateOpts = ref([
  { label: '準確率 100%', value: 1 },
  { label: '準確率 90% 以上', value: 0.9 },
  { label: '準確率 80% 以上', value: 0.8 },
  { label: '準確率 70% 以上', value: 0.7 },
  { label: '準確率 60% 以上', value: 0.6 },
  { label: '準確率 50% 以上', value: 0.5 },
]);

// 批量分析結果類型定義
interface BatchAnalysisResult {
  date: string;
  period: string;
  analysisType: string;
  ballFollowResults?: StatResult[];
  tailResults?: StatResult[];
  ballFollowOccurrences?: Map<string, Occurrence>;
  tailOccurrences?: Map<string, Occurrence>;
  predictNumbers?: number[];
  actualNumbers?: number[];
  matches?: number[];
  // 新增詳細分析結果
  targetNumAppearances?: Map<number, number>;
  nonAppearedNumbers?: number[];
  nonAppearedByFrequency?: Map<number, number>;
  tailNumAppearances?: Map<number, number>;
  // 預測響應數據
  predictResponse?: {
    draw_date?: string;
    period?: string;
    draw_number_size?: number[];
    special_number?: number;
  };
}

// 當前日期（用於限制參考期號）
const maxDate = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// 檢查是否可以開始分析
const canStartAnalysis = computed(() => {
  return selectedMethod.value &&
         selectedLottoType.value &&
         referenceDate.value &&
         !isCalculating.value;
});

// 版路分析參數
const ballFollowParams = ref({
  num1: 1,
  num2: 1,
  num3: 1,
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1
});

// 尾數分析參數
const tailParams = ref({
  num1: 1,
  num2: 1,
  num3: 1,
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1
});

// 綜合分析參數
const patternParams = ref({
  comb1: 1,
  comb2: 1,
  comb3: 1,
  tailComb1: 1,
  tailComb2: 1,
  tailComb3: 1,
  period: 50,
  maxRange: 20,
  ahead: 1
});

// 獎號組合
const combOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
];
// 尾數組合
const tailCombOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
  { label: '四星組合', value: 4 },
  { label: '五星組合', value: 5 },
];

const periodNumOpts = ref(
  Array.from({ length: 491 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const maxRangeOpts = ref(
  Array.from({ length: 21 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const aheadNumOpts = ref(
  Array.from({ length: 15 }, (_, i) => ({
    label: `下${i + 1}期`,
    value: i + 1,
  }))
);

const periodOptions = ref(
  Array.from({ length: 991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

// 結果表格欄位
// const resultColumns = {}

// 方法
const getMethodName = (method: string) => {
  const methodMap: Record<string, string> = {
    'ball-follow': '版路分析',
    'tail': '尾數分析',
    'pattern': '綜合分析'
  };
  return methodMap[method] || method;
};

const getLottoTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'super_lotto638': '威力彩',
    'lotto649': '大樂透',
    'daily539': '今彩539',
    'lotto_hk': '六合彩'
  };
  return typeMap[type] || type;
};

const onLottoTypeChange = () => {
  // 當彩種改變時，可以在這裡更新相關設定
};

const getAnalysisParameters = (): AnalysisParameters => {
  switch (selectedMethod.value) {
    case 'ball-follow':
      return {
        comb1: ballFollowParams.value.num1,
        comb2: ballFollowParams.value.num2,
        comb3: ballFollowParams.value.num3,
        periodNum: ballFollowParams.value.periodNum,
        maxRange: ballFollowParams.value.maxRange,
        aheadNum: ballFollowParams.value.aheadNum
      };
    case 'tail':
      return {
        tailComb1: tailParams.value.num1,
        tailComb2: tailParams.value.num2,
        tailComb3: tailParams.value.num3,
        periodNum: tailParams.value.periodNum,
        maxRange: tailParams.value.maxRange,
        aheadNum: tailParams.value.aheadNum
      };
    case 'pattern':
      return {
        comb1: patternParams.value.comb1,
        comb2: patternParams.value.comb2,
        comb3: patternParams.value.comb3,
        tailComb1: patternParams.value.tailComb1,
        tailComb2: patternParams.value.tailComb2,
        tailComb3: patternParams.value.tailComb3,
        period: patternParams.value.period,
        maxRange: patternParams.value.maxRange,
        ahead: patternParams.value.ahead
      };
    default:
      // Return default ball-follow parameters as fallback
      return {
        comb1: 1,
        comb2: 1,
        comb3: 1,
        tailComb1: 1,
        tailComb2: 1,
        tailComb3: 1,
        periodNum: 50,
        maxRange: 20,
        aheadNum: 1
      };
  }
};

const isSuperLotto = ref(false);

// 輔助函數：從分析結果中提取預測號碼和詳細統計
const extractDetailedAnalysis = (results: StatResult[], maxNumber = 49, accuracyFilter = 1) => {
  // 篩選符合條件的結果
  const filteredResults = results.filter(result =>
    result.consecutiveHits >= accuracyFilter
  );

  const targetNumAppearances = new Map<number, number>();
  const tailNumAppearances = new Map<number, number>();
  const nonAppearedFrequency = new Map<number, number>();

  // 統計預測號碼出現次數
  filteredResults.forEach(result => {
    result.targetNumbers.forEach(num => {
      targetNumAppearances.set(num, (targetNumAppearances.get(num) || 0) + 1);
      // 統計尾數
      const tail = num % 10;
      tailNumAppearances.set(tail, (tailNumAppearances.get(tail) || 0) + 1);
    });
  });

  // 計算未出現號碼
  const nonAppearedNumbers: number[] = [];
  for (let i = 1; i <= maxNumber; i++) {
    if (!targetNumAppearances.has(i)) {
      nonAppearedNumbers.push(i);
      // 統計未出現號碼在所有結果中的頻率
      const frequency = results.filter(result => result.targetNumbers.includes(i)).length;
      nonAppearedFrequency.set(i, frequency);
    }
  }

  // 取前10個最高機率的預測號碼
  const topPredictNumbers = Array.from(targetNumAppearances.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([num]) => num);

  return {
    predictNumbers: topPredictNumbers,
    targetNumAppearances,
    nonAppearedNumbers,
    nonAppearedByFrequency: nonAppearedFrequency,
    tailNumAppearances
  };
};

// 輔助函數：找出預測號碼與實際號碼的匹配
const findMatches = (predictNumbers: number[], actualNumbers: number[]): number[] => {
  return predictNumbers.filter(num => actualNumbers.includes(num));
};

// 輔助函數：獲取彩種的最大號碼
const getMaxNumberForLottoType = (lottoType: string): number => {
  switch (lottoType) {
    case 'super_lotto638':
      return 38;
    case 'lotto649':
      return 49;
    case 'daily539':
      return 39;
    case 'lotto_hk':
      return 49;
    default:
      return 49;
  }
};

// 輔助函數：從分析結果中提取預測號碼（向後兼容）
const extractPredictNumbers = (results: StatResult[]): number[] => {
  const analysis = extractDetailedAnalysis(results);
  return analysis.predictNumbers;
};



// 輔助函數：將號碼按每10個一列格式化
const formatNumbersInRows = (numbers: number[]): string[] => {
  const rows: string[] = [];
  for (let i = 0; i < numbers.length; i += 10) {
    const rowNumbers = numbers.slice(i, i + 10);
    rows.push(rowNumbers.map(n => n.toString().padStart(2, '0')).join(' '));
  }
  return rows;
};

// 輔助函數：將號碼按每10個一列格式化，並標註命中號碼
const formatNumbersWithMatches = (numbers: number[], matches: number[], isForTail = false): string[] => {
  const rows: string[] = [];
  for (let i = 0; i < numbers.length; i += 10) {
    const rowNumbers = numbers.slice(i, i + 10);
    const formattedNumbers = rowNumbers.map(n => {
      const numStr = isForTail ? n.toString() : n.toString().padStart(2, '0');
      return matches.includes(n) ? `*${numStr}*` : numStr; // 用*標記命中號碼
    });
    rows.push(formattedNumbers.join(' '));
  }
  return rows;
};

// 輔助函數：將號碼分成每10個一列的陣列
const formatNumbersIntoRows = (numbers: number[], isForTail = false): string[][] => {
  const rows: string[][] = [];
  for (let i = 0; i < numbers.length; i += 10) {
    const rowNumbers = numbers.slice(i, i + 10);
    const formattedNumbers = rowNumbers.map(n =>
      isForTail ? n.toString() : n.toString().padStart(2, '0')
    );
    rows.push(formattedNumbers);
  }
  return rows;
};

// 輔助函數：應用Excel樣式
const applyExcelStyles = (worksheet: XLSX.WorkSheet, rowCount: number) => {
  // 設置列寬
  const colWidths = [
    { wch: 10 }, // 期數
    { wch: 12 }, // 日期
    { wch: 30 }, // 預測號碼
    { wch: 30 }, // 實際號碼
    { wch: 30 }, // 命中號碼
    { wch: 10 }  // 命中數量
  ];
  worksheet['!cols'] = colWidths;

  // 設置行高
  const rowHeights = Array(rowCount).fill({ hpt: 20 });
  worksheet['!rows'] = rowHeights;
};

// 輔助函數：應用紅色字體樣式給匹配的號碼（使用標記方式）
const applyRedFontForMatches = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][], type: 'predict' | 'tail') => {
  console.log('開始應用紅色字體標記，類型:', type);
  console.log('批量結果數量:', batchResults.value.length);

  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    // 跳過空白日期行（換列對齊的行）
    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) {
      console.log('找不到對應結果，日期:', dateCell);
      continue;
    }

    console.log('處理日期:', dateCell, '實際號碼:', result.actualNumbers);

    // 檢查每個號碼是否與實際開獎號碼匹配
    for (let colIndex = 1; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        let isMatch = false;

        if (type === 'predict') {
          // 預測號碼：直接比較
          const numberToCheck = parseInt(cellValue.toString().replace(/^0+/, ''), 10);
          isMatch = result.actualNumbers.includes(numberToCheck);
          console.log('檢查預測號碼:', numberToCheck, '是否匹配:', isMatch);
        } else {
          // 尾數：比較尾數
          const tailToCheck = parseInt(cellValue.toString(), 10);
          const actualTails = result.actualNumbers.map(n => n % 10);
          isMatch = actualTails.includes(tailToCheck);
          console.log('檢查尾數:', tailToCheck, '實際尾數:', actualTails, '是否匹配:', isMatch);
        }

        if (isMatch) {
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
          console.log('標記命中號碼，儲存格:', cellRef, '值:', cellValue);

          // 使用簡單標記方式（在值後面加上符號）
          const markedValue = `${cellValue}★`;

          // 更新工作表數據
          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }

  console.log('紅色字體標記應用完成');
};


const startBatchAnalysis = async () => {
  if (!canStartAnalysis.value) {
    Notify.create({
      type: 'warning',
      message: '請完整填寫所有必要參數'
    });
    return;
  }

  try {
    isCalculating.value = true;
    isSuperLotto.value = selectedLottoType.value === 'super_lotto638';
    progress.value = 0;
    progressMessage.value = '準備開始...';
    batchResults.value = []; // 清空之前的結果

    // 準備API請求參數 - 獲取要分析的期數
    const params = getAnalysisParameters();
    const analysisCount = 'periodNum' in params ? params.periodNum :
                         'period' in params ? params.period : 50;

    const response = await LOTTO_API.getLottoList({
      draw_type: selectedLottoType.value,
      date_end: referenceDate.value,
      limit: analysisCount
    });
    // 將結果反轉，讓時間由舊到新排列
    const referenceResults = response.data.reverse();

    progressMessage.value = '正在進行分析...';

    // 進行分析
    let count = 0;
    for (const result of referenceResults) {
      // 更新進度條
      progress.value = count / referenceResults.length;
      progressMessage.value = `分析中... ${++count}/${referenceResults.length}`;

      // 獲取分析用的歷史數據
      const historyResponse = await LOTTO_API.getLottoList({
        draw_type: selectedLottoType.value,
        date_end: result.draw_date,
        limit: analysisCount
      });

      // 獲取預測期的實際開獎結果
      const params = getAnalysisParameters();
      const aheadCount = 'aheadNum' in params ? params.aheadNum :
                        'ahead' in params ? params.ahead : 1;
      const predictResponse = await LOTTO_API.getLottoPredict({
        draw_type: selectedLottoType.value,
        draw_date: result.draw_date,
        ahead_count: aheadCount
      });

      // 組合實際開獎號碼（包含特別號）
      let actualNumbers: number[] = [];
      if (predictResponse.data) {
        actualNumbers = [...(predictResponse.data.draw_number_size || [])];
        // 如果有特別號且不是威力彩，則添加特別號
        if (!isSuperLotto.value && predictResponse.data.special_number) {
          actualNumbers.push(predictResponse.data.special_number);
        }
      }

      let batchResult: BatchAnalysisResult = {
        date: result.draw_date,
        period: result.period,
        analysisType: selectedMethod.value,
        actualNumbers: actualNumbers,
        // 保存預測響應數據以供Excel使用
        predictResponse: predictResponse.data
      };

      switch (selectedMethod.value) {
        case 'ball-follow':
          const ballFollowAnalysis = await doRdCalculating(historyResponse.data);
          batchResult.ballFollowResults = ballFollowAnalysis.data;
          batchResult.ballFollowOccurrences = ballFollowAnalysis.occurrences;

          // 獲取彩種的最大號碼
          const maxNumber = getMaxNumberForLottoType(selectedLottoType.value);
          const detailedAnalysis = extractDetailedAnalysis(
            ballFollowAnalysis.data,
            maxNumber,
            accuracy.value
          );

          batchResult.predictNumbers = detailedAnalysis.predictNumbers;
          batchResult.targetNumAppearances = detailedAnalysis.targetNumAppearances;
          batchResult.nonAppearedNumbers = detailedAnalysis.nonAppearedNumbers;
          batchResult.nonAppearedByFrequency = detailedAnalysis.nonAppearedByFrequency;
          batchResult.tailNumAppearances = detailedAnalysis.tailNumAppearances;
          batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
          break;
        case 'tail':
          const tailAnalysis = await doTailRdCalculating(historyResponse.data);
          batchResult.tailResults = tailAnalysis.data;
          batchResult.tailOccurrences = tailAnalysis.occurrences;

          const tailDetailedAnalysis = extractDetailedAnalysis(
            tailAnalysis.data,
            10, // 尾數最大為9
            accuracy.value
          );

          batchResult.predictNumbers = tailDetailedAnalysis.predictNumbers;
          batchResult.targetNumAppearances = tailDetailedAnalysis.targetNumAppearances;
          batchResult.nonAppearedNumbers = tailDetailedAnalysis.nonAppearedNumbers;
          batchResult.nonAppearedByFrequency = tailDetailedAnalysis.nonAppearedByFrequency;
          batchResult.tailNumAppearances = tailDetailedAnalysis.tailNumAppearances;
          batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
          break;
        case 'pattern':
          const ballAnalysis = await doRdCalculating(historyResponse.data);
          const tailAnalysis2 = await doTailRdCalculating(historyResponse.data);
          batchResult.ballFollowResults = ballAnalysis.data;
          batchResult.ballFollowOccurrences = ballAnalysis.occurrences;
          batchResult.tailResults = tailAnalysis2.data;
          batchResult.tailOccurrences = tailAnalysis2.occurrences;

          // 獲取彩種的最大號碼
          const maxNumber2 = getMaxNumberForLottoType(selectedLottoType.value);

          // 版路分析詳細統計
          const ballDetailedAnalysis = extractDetailedAnalysis(
            ballAnalysis.data,
            maxNumber2,
            accuracy.value
          );

          // 尾數分析詳細統計
          const tailDetailedAnalysis2 = extractDetailedAnalysis(
            tailAnalysis2.data,
            10,
            accuracy.value
          );

          // 綜合分析的預測號碼需要結合版路和尾數分析
          const ballPredictNumbers = ballDetailedAnalysis.predictNumbers;
          const tailPredictNumbers = tailDetailedAnalysis2.predictNumbers;
          batchResult.predictNumbers = [...new Set([...ballPredictNumbers, ...tailPredictNumbers])];

          // 合併統計數據
          batchResult.targetNumAppearances = ballDetailedAnalysis.targetNumAppearances;
          batchResult.nonAppearedNumbers = ballDetailedAnalysis.nonAppearedNumbers;
          batchResult.nonAppearedByFrequency = ballDetailedAnalysis.nonAppearedByFrequency;
          batchResult.tailNumAppearances = ballDetailedAnalysis.tailNumAppearances;

          batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
          break;
      }

      batchResults.value.push(batchResult);
    }

    progressMessage.value = '分析完成！';
    progress.value = 1;

    Notify.create({
      type: 'positive',
      position: 'top',
      message: '分析完成！'
    });

  } catch (error) {
    handleError(error);
  } finally {
    isCalculating.value = false;
  }
};

const stopBatchAnalysis = () => {
  isCalculating.value = false;
  progressMessage.value = '分析已中斷';

  Notify.create({
    type: 'warning',
    message: '分析已中斷'
  });
};

// 獎號拖牌
const doRdCalculating = async (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as BallFollowParameters;
  const serializedDrawResults = drawResults.map((item: LottoItem) => {
    const numbers = [...item.draw_number_size];
    if (!isSuperLotto.value && item.special_number) {
      // 除威力彩，其餘有彩種皆須加入特別號
      numbers.push(item.special_number);
    }
    // 返回一個純粹的對象
    return {
      numbers: [...numbers], // 確保創建新數組
      period: String(item.period), // 確保 period 是字符串
    };
  });

  // 設置配置
  analysis.init(
    {
      firstGroupSize: params.comb1 || 1,
      secondGroupSize: params.comb2 || 1,
      targetGroupSize: params.comb3 || 1,
      maxRange: params.maxRange || 20,
      lookAheadCount: params.aheadNum || 1,
    },
    serializedDrawResults
  );

  return await analysis.analyzeWithProgress();
};

// 尾數拖牌
const doTailRdCalculating = async (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as TailParameters;

  // 創建數據結構
  const serializedTailResults = drawResults.map((item: LottoItem) => {
    const numbers = new Set<number>();

    for (let number of item.draw_number_size) {
      numbers.add(number % 10);
    }

    if (!isSuperLotto.value && item.special_number) {
      numbers.add(item.special_number % 10);
    }

    const sorted = Array.from(numbers).sort((a, b) => {
      if (a === 0) return 1;
      if (b === 0) return -1;
      return a - b;
    });

    return {
      period: String(item.period),
      numbers: [...sorted],
    };
  });

  analysis.init(
    {
      firstGroupSize: params.tailComb1 || 1,
      secondGroupSize: params.tailComb2 || 1,
      targetGroupSize: params.tailComb3 || 1,
      maxRange: params.maxRange || 20,
      lookAheadCount: params.aheadNum || 1,
    },
    serializedTailResults
  );

  return await analysis.analyzeWithProgress();
};

const downloadResults = () => {
  // if (batchResults.value.length === 0) {
  //   Notify.create({
  //     type: 'warning',
  //     message: '沒有可下載的結果'
  //   });
  //   return;
  // }

  try {
      downloadExcel();
  } catch (error) {
    console.error('下載失敗:', error);
    Notify.create({
      type: 'negative',
      message: '檔案下載失敗'
    });
  }
};

const downloadExcel = () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    // 創建工作簿
    const workbook = XLSX.utils.book_new();

    // 根據分析方法創建不同的工作表
    switch (selectedMethod.value) {
      case 'ball-follow':
        createPredictNumbersSheet(workbook);
        createNonAppearedByFrequencySheet(workbook);
        createNonAppearedBySizeSheet(workbook);
        createTailNumbersSheet(workbook);
        createActualNumbersSheet(workbook);
        break;
      case 'tail':
        createTailSheet(workbook);
        createActualNumbersSheet(workbook);
        break;
      case 'pattern':
        createPatternSheet(workbook);
        break;
    }

    // 生成檔案名稱
    const methodName = getMethodName(selectedMethod.value);
    const lottoName = getLottoTypeName(selectedLottoType.value);
    const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const fileName = `${methodName}_${lottoName}_${dateStr}.xlsx`;

    // 下載檔案
    XLSX.writeFile(workbook, fileName);

    Notify.create({
      type: 'positive',
      message: '報表下載成功！'
    });
  } catch (error) {
    console.error('Excel生成失敗:', error);
    Notify.create({
      type: 'negative',
      message: 'Excel檔案生成失敗'
    });
  }
};



// 創建預測號碼統計工作表
const createPredictNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測號碼統計']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedNumbers);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]); // 空白日期，對齊號碼
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎號碼相符的預測號碼
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  XLSX.utils.book_append_sheet(workbook, worksheet, '預測號碼');
};

// 創建未出現號碼工作表 - 依預測次數排序
const createNonAppearedByFrequencySheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['未出現號碼 - 依預測次數排序']);
  sheetData.push([]);
  sheetData.push(['日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 按預測次數排序未出現號碼
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedByFreq);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  XLSX.utils.book_append_sheet(workbook, worksheet, '未出現號碼-預測次數');
};

// 創建未出現號碼工作表 - 依大小排序
const createNonAppearedBySizeSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['未出現號碼 - 依大小排序']);
  sheetData.push([]);
  sheetData.push(['日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedBySize);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  XLSX.utils.book_append_sheet(workbook, worksheet, '未出現號碼-大小排序');
};

// 創建尾數統計工作表
const createTailNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按統計次數排序（次數高的在前）
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])  // 按次數降序排列
        .map(([tail]) => tail);       // 只取尾數值

      // 每10個尾數一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎尾數相符的預測尾數
  applyRedFontForMatches(worksheet, sheetData, 'tail');

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數統計');
};

// 創建實際開獎號碼工作表
const createActualNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  // 獲取預測參數
  const params = getAnalysisParameters();
  const aheadCount = 'aheadNum' in params ? params.aheadNum :
                    'ahead' in params ? params.ahead : 1;

  sheetData.push([`實際開獎號碼 (預測${aheadCount}期後)`]);
  sheetData.push([]);

  // 檢查實際數據中是否有特別號
  const hasSpecialNumber = batchResults.value.some(result => {
    if (!result.actualNumbers || result.actualNumbers.length === 0) return false;

    // 根據彩種判斷是否應該有特別號
    if (selectedLottoType.value === 'super_lotto638') {
      return false; // 威力彩沒有特別號
    } else if (selectedLottoType.value === 'daily539') {
      return false; // 今彩539沒有特別號
    } else {
      // 大樂透和六合彩有特別號，檢查實際數據長度
      return result.actualNumbers.length > 6;
    }
  });

  // 創建表頭
  const headers = ['分析日期', '預測日期'];

  // 根據彩種決定號碼欄位數量
  let maxNumbers = 6; // 預設6個號碼
  if (selectedLottoType.value === 'super_lotto638') {
    maxNumbers = 6; // 威力彩6個號碼
  } else if (selectedLottoType.value === 'lotto649') {
    maxNumbers = 6; // 大樂透6個號碼
  } else if (selectedLottoType.value === 'daily539') {
    maxNumbers = 5; // 今彩539只有5個號碼
  } else if (selectedLottoType.value === 'lotto_hk') {
    maxNumbers = 6; // 六合彩6個號碼
  }

  // 添加一般號碼欄位
  for (let i = 1; i <= maxNumbers; i++) {
    headers.push(`號碼${i}`);
  }

  // 只有在實際有特別號的情況下才添加特別號欄位
  if (hasSpecialNumber) {
    headers.push('特別號');
  }

  sheetData.push(headers);

  // 添加數據行
  batchResults.value.forEach(result => {
    // 從predictResponse中獲取正確的預測日期
    let predictDate = '';
    let isDrawn = false;

    if (result.predictResponse) {
      if (result.predictResponse.draw_date) {
        predictDate = result.predictResponse.draw_date;
        // 檢查是否已開獎（有期號表示已開獎）
        isDrawn = !!result.predictResponse.period;
      } else {
        predictDate = '尚未開獎';
      }
    } else {
      predictDate = '尚未開獎';
    }

    const row: (string | number)[] = [result.date, predictDate];

    // 分離一般號碼和特別號
    let normalNumbers: number[] = [];
    let specialNumber: number | undefined;

    if (result.actualNumbers && result.actualNumbers.length > 0 && isDrawn) {
      if (isSuperLotto.value) {
        // 威力彩：所有號碼都是一般號碼
        normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
      } else {
        // 其他彩種：最後一個是特別號
        if (result.actualNumbers.length > maxNumbers) {
          normalNumbers = result.actualNumbers.slice(0, -1).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[result.actualNumbers.length - 1];
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      }
    }

    // 添加一般號碼
    for (let i = 0; i < maxNumbers; i++) {
      if (i < normalNumbers.length) {
        row.push(normalNumbers[i].toString().padStart(2, '0'));
      } else if (!isDrawn && predictDate !== '尚未開獎') {
        row.push('尚未開獎'); // 如果有預測日期但還沒開獎
      } else {
        row.push(''); // 如果完全沒有數據
      }
    }

    // 添加特別號
    if (hasSpecialNumber) {
      if (specialNumber !== undefined) {
        row.push(specialNumber.toString().padStart(2, '0'));
      } else if (!isDrawn && predictDate !== '尚未開獎') {
        row.push('尚未開獎'); // 如果有預測日期但還沒開獎
      } else {
        row.push(''); // 如果完全沒有數據
      }
    }

    sheetData.push(row);
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }  // 預測日期欄
  ];
  for (let i = 0; i < maxNumbers + (hasSpecialNumber ? 1 : 0); i++) {
    colWidths.push({ wch: 8 }); // 號碼欄
  }
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '實際開獎號碼');
};

// 創建尾數分析工作表
const createTailSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  // 標題行
  const methodName = getMethodName(selectedMethod.value);
  const lottoName = getLottoTypeName(selectedLottoType.value);
  sheetData.push([`${methodName} - ${lottoName}`]);
  sheetData.push([]); // 空行

  // 表頭
  const headers = ['日期', '預測尾數', '實際尾數', '命中尾數', '命中數量'];
  sheetData.push(headers);

  // 數據行
  batchResults.value.forEach(result => {
    const predictNumbers = (result.predictNumbers || []).slice(0, 10);
    const actualNumbers = result.actualNumbers || [];
    const matches = result.matches || [];

    // 轉換為尾數
    const predictTails = predictNumbers.map(n => n % 10);
    const actualTails = [...new Set(actualNumbers.map(n => n % 10))];
    const matchTails = matches.map(n => n % 10);

    const predictRows = formatNumbersWithMatches(predictTails, matchTails, true); // true表示是尾數，不padding zero
    const actualRows = formatNumbersInRows(actualTails);
    const matchRows = formatNumbersInRows(matchTails);

    const maxRows = Math.max(predictRows.length, actualRows.length, matchRows.length, 1);

    for (let i = 0; i < maxRows; i++) {
      const row = [
        i === 0 ? result.date : '',
        predictRows[i] || '',
        actualRows[i] || '',
        matchRows[i] || '',
        i === 0 ? matchTails.length : ''
      ];
      sheetData.push(row);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 設置樣式
  applyExcelStyles(worksheet, sheetData.length);

  XLSX.utils.book_append_sheet(workbook, worksheet, methodName);
};

// 創建綜合分析工作表
const createPatternSheet = (workbook: XLSX.WorkBook) => {
  // 創建版路分析子表
  createPredictNumbersSheet(workbook);
  createNonAppearedByFrequencySheet(workbook);
  createNonAppearedBySizeSheet(workbook);
  createTailNumbersSheet(workbook);

  // 創建尾數分析子表
  createTailSheet(workbook);

  // 創建實際開獎號碼工作表
  createActualNumbersSheet(workbook);

  // 創建綜合分析總表
  const sheetData: (string | number)[][] = [];

  const methodName = getMethodName(selectedMethod.value);
  const lottoName = getLottoTypeName(selectedLottoType.value);
  sheetData.push([`${methodName} - ${lottoName} 綜合統計`]);
  sheetData.push([]); // 空行

  const headers = ['日期', '版路命中', '尾數命中', '總命中', '命中率'];
  sheetData.push(headers);

  batchResults.value.forEach(result => {
    const ballMatches = result.ballFollowResults ?
      findMatches(extractPredictNumbers(result.ballFollowResults), result.actualNumbers || []).length : 0;
    const tailMatches = result.tailResults ?
      findMatches(extractPredictNumbers(result.tailResults), result.actualNumbers || []).length : 0;
    const totalMatches = ballMatches + tailMatches;
    const hitRate = totalMatches > 0 ? ((totalMatches / ((result.actualNumbers || []).length * 2)) * 100).toFixed(2) + '%' : '0%';

    sheetData.push([
      result.date,
      ballMatches,
      tailMatches,
      totalMatches,
      hitRate
    ]);
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
  applyExcelStyles(worksheet, sheetData.length);

  XLSX.utils.book_append_sheet(workbook, worksheet, '綜合統計');
};

// 初始化
onMounted(() => {
  // 設定預設參考日期為今天
  referenceDate.value = new Date().toISOString().split('T')[0];
});
</script>

<style lang="scss" scoped>
.q-card {
  max-width: 1200px;
  margin: 0 auto;
}

.q-option-group {
  .q-radio {
    margin-right: 2rem;
  }
}
</style>
