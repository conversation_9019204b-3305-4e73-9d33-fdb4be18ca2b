import{Q as Me}from"./QSelect.5be6abd0.js";import{k as ls,r as Je,c as y0,b as os,I as jr,J as fn,L as qr,M as Se,a3 as Hn,P as ae,a4 as us,az as ln,aA as Gn,aB as st,a6 as cs,a8 as Xn,O as zn,a2 as O0,af as ft}from"./index.43a5c8e9.js";import{Q as hs}from"./QSpinnerDots.70047834.js";import{Q as xs}from"./QLinearProgress.46c3b050.js";import{Q as ds}from"./QPage.48be0cc6.js";import{L as $n}from"./lotto.b1d9e5c1.js";import{u as ps}from"./useLotteryAnalysis.9339f52c.js";import{h as ms}from"./error-handler.a349144b.js";import{_ as vs}from"./plugin-vue_export-helper.21dcd24c.js";import"./QItem.68221b4d.js";import"./position-engine.d4e46236.js";import"./selection.ed72df40.js";/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var vn={};vn.version="0.18.5";var pa=1252,gs=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],ma=function(e){gs.indexOf(e)!=-1&&(pa=e)};function _s(){ma(1252)}var Xt=function(e){ma(e)};function Es(){Xt(1200),_s()}function Ts(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var on=function(t){return String.fromCharCode(t)},D0=function(t){return String.fromCharCode(t)},lt,Zr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function zt(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0,l=0;l<e.length;)r=e.charCodeAt(l++),i=r>>2,n=e.charCodeAt(l++),s=(r&3)<<4|n>>4,a=e.charCodeAt(l++),f=(n&15)<<2|a>>6,o=a&63,isNaN(n)?f=o=64:isNaN(a)&&(o=64),t+=Zr.charAt(i)+Zr.charAt(s)+Zr.charAt(f)+Zr.charAt(o);return t}function zr(e){var t="",r=0,n=0,a=0,i=0,s=0,f=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)i=Zr.indexOf(e.charAt(l++)),s=Zr.indexOf(e.charAt(l++)),r=i<<2|s>>4,t+=String.fromCharCode(r),f=Zr.indexOf(e.charAt(l++)),n=(s&15)<<4|f>>2,f!==64&&(t+=String.fromCharCode(n)),o=Zr.indexOf(e.charAt(l++)),a=(f&3)<<6|o,o!==64&&(t+=String.fromCharCode(a));return t}var ye=function(){return typeof Buffer!="undefined"&&typeof process!="undefined"&&typeof process.versions!="undefined"&&!!process.versions.node}(),Kr=function(){if(typeof Buffer!="undefined"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function ct(e){return ye?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}function N0(e){return ye?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array!="undefined"?new Uint8Array(e):new Array(e)}var Pr=function(t){return ye?Kr(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function Rn(e){if(typeof ArrayBuffer=="undefined")return Pr(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=e.charCodeAt(n)&255;return t}function qt(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function ws(e){if(typeof Uint8Array=="undefined")throw new Error("Unsupported");return new Uint8Array(e)}var ar=ye?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Kr(t)}))}:function(e){if(typeof Uint8Array!="undefined"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(i){return Array.isArray(i)?i:[].slice.call(i)}))};function As(e){for(var t=[],r=0,n=e.length+250,a=ct(e.length+255),i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s<128)a[r++]=s;else if(s<2048)a[r++]=192|s>>6&31,a[r++]=128|s&63;else if(s>=55296&&s<57344){s=(s&1023)+64;var f=e.charCodeAt(++i)&1023;a[r++]=240|s>>8&7,a[r++]=128|s>>2&63,a[r++]=128|f>>6&15|(s&3)<<4,a[r++]=128|f&63}else a[r++]=224|s>>12&15,a[r++]=128|s>>6&63,a[r++]=128|s&63;r>n&&(t.push(a.slice(0,r)),r=0,a=ct(65535),n=65530)}return t.push(a.slice(0,r)),ar(t)}var bt=/\u0000/g,un=/[\u0001-\u0006]/g;function St(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function Lr(e,t){var r=""+e;return r.length>=t?r:We("0",t-r.length)+r}function i0(e,t){var r=""+e;return r.length>=t?r:We(" ",t-r.length)+r}function gn(e,t){var r=""+e;return r.length>=t?r:r+We(" ",t-r.length)}function Ss(e,t){var r=""+Math.round(e);return r.length>=t?r:We("0",t-r.length)+r}function Fs(e,t){var r=""+e;return r.length>=t?r:We("0",t-r.length)+r}var R0=Math.pow(2,32);function _t(e,t){if(e>R0||e<-R0)return Ss(e,t);var r=Math.round(e);return Fs(r,t)}function _n(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var I0=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Kn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function Cs(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "',e}var Ve={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"\u4E0A\u5348/\u4E0B\u5348 "hh"\u6642"mm"\u5206"ss"\u79D2 "'},k0={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},ys={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function En(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,s=1,f=0,o=1,l=0,u=0,x=Math.floor(a);l<t&&(x=Math.floor(a),f=x*s+i,u=x*l+o,!(a-x<5e-8));)a=1/(a-x),i=s,s=f,o=l,l=u;if(u>t&&(l>t?(u=o,f=i):(u=l,f=s)),!r)return[0,n*f,u];var d=Math.floor(n*f/u);return[d,n*f-d*u,u]}function cn(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),i=0,s=[],f={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(f.u)<1e-6&&(f.u=0),t&&t.date1904&&(n+=1462),f.u>.9999&&(f.u=0,++a==86400&&(f.T=a=0,++n,++f.D)),n===60)s=r?[1317,10,29]:[1900,2,29],i=3;else if(n===0)s=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),s=[o.getFullYear(),o.getMonth()+1,o.getDate()],i=o.getDay(),n<60&&(i=(i+6)%7),r&&(i=Ps(o,s))}return f.y=s[0],f.m=s[1],f.d=s[2],f.S=a%60,a=Math.floor(a/60),f.M=a%60,a=Math.floor(a/60),f.H=a,f.q=i,f}var va=new Date(1899,11,31,0,0,0),Os=va.getTime(),Ds=new Date(1900,2,1,0,0,0);function ga(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Ds&&(r+=24*60*60*1e3),(r-(Os+(e.getTimezoneOffset()-va.getTimezoneOffset())*6e4))/(24*60*60*1e3)}function s0(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function Ns(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function Rs(e){var t=e<0?12:11,r=s0(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Is(e){var t=s0(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function ks(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=Rs(e):t===10?r=e.toFixed(10).substr(0,12):r=Is(e),s0(Ns(r.toUpperCase()))}function r0(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):ks(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return et(14,ga(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Ps(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Ls(e,t,r,n){var a="",i=0,s=0,f=r.y,o,l=0;switch(e){case 98:f=r.y+543;case 121:switch(t.length){case 1:case 2:o=f%100,l=2;break;default:o=f%1e4,l=4;break}break;case 109:switch(t.length){case 1:case 2:o=r.m,l=t.length;break;case 3:return Kn[r.m-1][1];case 5:return Kn[r.m-1][0];default:return Kn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:o=r.d,l=t.length;break;case 3:return I0[r.q][0];default:return I0[r.q][1]}break;case 104:switch(t.length){case 1:case 2:o=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:o=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:o=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?Lr(r.S,t.length):(n>=2?s=n===3?1e3:100:s=n===1?10:1,i=Math.round(s*(r.S+r.u)),i>=60*s&&(i=0),t==="s"?i===0?"0":""+i/s:(a=Lr(i,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}l=t.length===3?1:2;break;case 101:o=f,l=1;break}var u=l>0?Lr(o,l):"";return u}function Qr(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var _a=/%/g;function Bs(e,t,r){var n=t.replace(_a,""),a=t.length-n.length;return Hr(e,n,r*Math.pow(10,2*a))+We("%",a)}function Ms(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Hr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Ea(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Ea(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),r.indexOf("e")===-1){var s=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,u){return o+l+u.substr(0,(a+i)%a)+"."+u.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Ta=/# (\?+)( ?)\/( ?)(\d+)/;function bs(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),s=a-i*n,f=n;return r+(i===0?"":""+i)+" "+(s===0?We(" ",e[1].length+1+e[4].length):i0(s,e[1].length)+e[2]+"/"+e[3]+Lr(f,e[4].length))}function Us(e,t,r){return r+(t===0?"":""+t)+We(" ",e[1].length+2+e[4].length)}var wa=/^#*0*\.([0#]+)/,Aa=/\).*[0#]/,Sa=/\(###\) ###\\?-####/;function pr(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function P0(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function L0(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Ws(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Vs(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Or(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Aa)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Or("n",n,r):"("+Or("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Ms(e,t,r);if(t.indexOf("%")!==-1)return Bs(e,t,r);if(t.indexOf("E")!==-1)return Ea(t,r);if(t.charCodeAt(0)===36)return"$"+Or(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+_t(o,t.length);if(t.match(/^[#?]+$/))return a=_t(r,0),a==="0"&&(a=""),a.length>t.length?a:pr(t.substr(0,t.length-a.length))+a;if(i=t.match(Ta))return bs(i,o,l);if(t.match(/^#+0+$/))return l+_t(o,t.length-t.indexOf("0"));if(i=t.match(wa))return a=P0(r,i[1].length).replace(/^([^\.]+)$/,"$1."+pr(i[1])).replace(/\.$/,"."+pr(i[1])).replace(/\.(\d*)$/,function(E,h){return"."+h+We("0",pr(i[1]).length-h.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+P0(o,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+Qr(_t(o,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Or(e,t,-r):Qr(""+(Math.floor(r)+Ws(r,i[1].length)))+"."+Lr(L0(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return Or(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=St(Or(e,t.replace(/[\\-]/g,""),r)),s=0,St(St(t.replace(/\\/g,"")).replace(/[0#]/g,function(E){return s<a.length?a.charAt(s++):E==="0"?"0":""}));if(t.match(Sa))return a=Or(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var u="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=En(o,Math.pow(10,s)-1,!1),a=""+l,u=Hr("n",i[1],f[1]),u.charAt(u.length-1)==" "&&(u=u.substr(0,u.length-1)+"0"),a+=u+i[2]+"/"+i[3],u=gn(f[2],s),u.length<i[4].length&&(u=pr(i[4].substr(i[4].length-u.length))+u),a+=u,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=En(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?i0(f[1],s)+i[2]+"/"+i[3]+gn(f[2],s):We(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=_t(r,0),t.length<=a.length?a:pr(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var x=t.indexOf(".")-s,d=t.length-a.length-x;return pr(t.substr(0,x)+a+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return s=L0(r,i[1].length),r<0?"-"+Or(e,t,-r):Qr(Vs(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(E){return"00,"+(E.length<3?Lr(0,3-E.length):"")+E})+"."+Lr(s,i[1].length);switch(t){case"###,##0.00":return Or(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var p=Qr(_t(o,0));return p!=="0"?l+p:"";case"###,###.00":return Or(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Or(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function Hs(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return Hr(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Gs(e,t,r){var n=t.replace(_a,""),a=t.length-n.length;return Hr(e,n,r*Math.pow(10,2*a))+We("%",a)}function Fa(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+Fa(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a),!r.match(/[Ee]/)){var s=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(s-r.length+i):r+="E+"+(s-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(f,o,l,u){return o+l+u.substr(0,(a+i)%a)+"."+u.substr(i)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Mr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(Aa)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Mr("n",n,r):"("+Mr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Hs(e,t,r);if(t.indexOf("%")!==-1)return Gs(e,t,r);if(t.indexOf("E")!==-1)return Fa(t,r);if(t.charCodeAt(0)===36)return"$"+Mr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,i,s,f,o=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+Lr(o,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:pr(t.substr(0,t.length-a.length))+a;if(i=t.match(Ta))return Us(i,o,l);if(t.match(/^#+0+$/))return l+Lr(o,t.length-t.indexOf("0"));if(i=t.match(wa))return a=(""+r).replace(/^([^\.]+)$/,"$1."+pr(i[1])).replace(/\.$/,"."+pr(i[1])),a=a.replace(/\.(\d*)$/,function(E,h){return"."+h+We("0",pr(i[1]).length-h.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return l+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return l+Qr(""+o);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Mr(e,t,-r):Qr(""+r)+"."+We("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Mr(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=St(Mr(e,t.replace(/[\\-]/g,""),r)),s=0,St(St(t.replace(/\\/g,"")).replace(/[0#]/g,function(E){return s<a.length?a.charAt(s++):E==="0"?"0":""}));if(t.match(Sa))return a=Mr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var u="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(i[4].length,7),f=En(o,Math.pow(10,s)-1,!1),a=""+l,u=Hr("n",i[1],f[1]),u.charAt(u.length-1)==" "&&(u=u.substr(0,u.length-1)+"0"),a+=u+i[2]+"/"+i[3],u=gn(f[2],s),u.length<i[4].length&&(u=pr(i[4].substr(i[4].length-u.length))+u),a+=u,a;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return s=Math.min(Math.max(i[1].length,i[4].length),7),f=En(o,Math.pow(10,s)-1,!0),l+(f[0]||(f[1]?"":"0"))+" "+(f[1]?i0(f[1],s)+i[2]+"/"+i[3]+gn(f[2],s):We(" ",2*s+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:pr(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),s=a.indexOf(".");var x=t.indexOf(".")-s,d=t.length-a.length-x;return pr(t.substr(0,x)+a+t.substr(t.length-d))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Mr(e,t,-r):Qr(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(E){return"00,"+(E.length<3?Lr(0,3-E.length):"")+E})+"."+Lr(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=Qr(""+o);return p!=="0"?l+p:"";default:if(t.match(/\.[0#?]*$/))return Mr(e,t.slice(0,t.lastIndexOf(".")),r)+pr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Hr(e,t,r){return(r|0)===r?Mr(e,t,r):Or(e,t,r)}function Xs(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Ca=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ya(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":_n(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"\u4E0A":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(Ca))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function zs(e,t,r,n){for(var a=[],i="",s=0,f="",o="t",l,u,x,d="H";s<e.length;)switch(f=e.charAt(s)){case"G":if(!_n(e,s))throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"G",v:"General"},s+=7;break;case'"':for(i="";(x=e.charCodeAt(++s))!==34&&s<e.length;)i+=String.fromCharCode(x);a[a.length]={t:"t",v:i},++s;break;case"\\":var p=e.charAt(++s),E=p==="("||p===")"?p:"t";a[a.length]={t:E,v:p},++s;break;case"_":a[a.length]={t:"t",v:" "},s+=2;break;case"@":a[a.length]={t:"T",v:t},++s;break;case"B":case"b":if(e.charAt(s+1)==="1"||e.charAt(s+1)==="2"){if(l==null&&(l=cn(t,r,e.charAt(s+1)==="2"),l==null))return"";a[a.length]={t:"X",v:e.substr(s,2)},o=f,s+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||l==null&&(l=cn(t,r),l==null))return"";for(i=f;++s<e.length&&e.charAt(s).toLowerCase()===f;)i+=f;f==="m"&&o.toLowerCase()==="h"&&(f="M"),f==="h"&&(f=d),a[a.length]={t:f,v:i},o=f;break;case"A":case"a":case"\u4E0A":var h={t:f,v:f};if(l==null&&(l=cn(t,r)),e.substr(s,3).toUpperCase()==="A/P"?(l!=null&&(h.v=l.H>=12?"P":"A"),h.t="T",d="h",s+=3):e.substr(s,5).toUpperCase()==="AM/PM"?(l!=null&&(h.v=l.H>=12?"PM":"AM"),h.t="T",s+=5,d="h"):e.substr(s,5).toUpperCase()==="\u4E0A\u5348/\u4E0B\u5348"?(l!=null&&(h.v=l.H>=12?"\u4E0B\u5348":"\u4E0A\u5348"),h.t="T",s+=5,d="h"):(h.t="t",++s),l==null&&h.t==="T")return"";a[a.length]=h,o=f;break;case"[":for(i=f;e.charAt(s++)!=="]"&&s<e.length;)i+=e.charAt(s);if(i.slice(-1)!=="]")throw'unterminated "[" block: |'+i+"|";if(i.match(Ca)){if(l==null&&(l=cn(t,r),l==null))return"";a[a.length]={t:"Z",v:i.toLowerCase()},o=i.charAt(1)}else i.indexOf("$")>-1&&(i=(i.match(/\$([^-\[\]]*)/)||[])[1]||"$",ya(e)||(a[a.length]={t:"t",v:i}));break;case".":if(l!=null){for(i=f;++s<e.length&&(f=e.charAt(s))==="0";)i+=f;a[a.length]={t:"s",v:i};break}case"0":case"#":for(i=f;++s<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(s))>-1;)i+=f;a[a.length]={t:"n",v:i};break;case"?":for(i=f;e.charAt(++s)===f;)i+=f;a[a.length]={t:f,v:i},o=f;break;case"*":++s,(e.charAt(s)==" "||e.charAt(s)=="*")&&++s;break;case"(":case")":a[a.length]={t:n===1?"t":f,v:f},++s;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(i=f;s<e.length&&"0123456789".indexOf(e.charAt(++s))>-1;)i+=e.charAt(s);a[a.length]={t:"D",v:i};break;case" ":a[a.length]={t:f,v:f},++s;break;case"$":a[a.length]={t:"t",v:"$"},++s;break;default:if(",$-+/():!^&'~{}<>=\u20ACacfijklopqrtuvwxzP".indexOf(f)===-1)throw new Error("unrecognized character "+f+" in "+e);a[a.length]={t:"t",v:f},++s;break}var g=0,y=0,F;for(s=a.length-1,o="t";s>=0;--s)switch(a[s].t){case"h":case"H":a[s].t=d,o="h",g<1&&(g=1);break;case"s":(F=a[s].v.match(/\.0+$/))&&(y=Math.max(y,F[0].length-1)),g<3&&(g=3);case"d":case"y":case"M":case"e":o=a[s].t;break;case"m":o==="s"&&(a[s].t="M",g<2&&(g=2));break;case"X":break;case"Z":g<1&&a[s].v.match(/[Hh]/)&&(g=1),g<2&&a[s].v.match(/[Mm]/)&&(g=2),g<3&&a[s].v.match(/[Ss]/)&&(g=3)}switch(g){case 0:break;case 1:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M),l.M>=60&&(l.M=0,++l.H);break;case 2:l.u>=.5&&(l.u=0,++l.S),l.S>=60&&(l.S=0,++l.M);break}var D="",M;for(s=0;s<a.length;++s)switch(a[s].t){case"t":case"T":case" ":case"D":break;case"X":a[s].v="",a[s].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[s].v=Ls(a[s].t.charCodeAt(0),a[s].v,l,y),a[s].t="t";break;case"n":case"?":for(M=s+1;a[M]!=null&&((f=a[M].t)==="?"||f==="D"||(f===" "||f==="t")&&a[M+1]!=null&&(a[M+1].t==="?"||a[M+1].t==="t"&&a[M+1].v==="/")||a[s].t==="("&&(f===" "||f==="n"||f===")")||f==="t"&&(a[M].v==="/"||a[M].v===" "&&a[M+1]!=null&&a[M+1].t=="?"));)a[s].v+=a[M].v,a[M]={v:"",t:";"},++M;D+=a[s].v,s=M-1;break;case"G":a[s].t="t",a[s].v=r0(t,r);break}var q="",ie,N;if(D.length>0){D.charCodeAt(0)==40?(ie=t<0&&D.charCodeAt(0)===45?-t:t,N=Hr("n",D,ie)):(ie=t<0&&n>1?-t:t,N=Hr("n",D,ie),ie<0&&a[0]&&a[0].t=="t"&&(N=N.substr(1),a[0].v="-"+a[0].v)),M=N.length-1;var V=a.length;for(s=0;s<a.length;++s)if(a[s]!=null&&a[s].t!="t"&&a[s].v.indexOf(".")>-1){V=s;break}var L=a.length;if(V===a.length&&N.indexOf("E")===-1){for(s=a.length-1;s>=0;--s)a[s]==null||"n?".indexOf(a[s].t)===-1||(M>=a[s].v.length-1?(M-=a[s].v.length,a[s].v=N.substr(M+1,a[s].v.length)):M<0?a[s].v="":(a[s].v=N.substr(0,M+1),M=-1),a[s].t="t",L=s);M>=0&&L<a.length&&(a[L].v=N.substr(0,M+1)+a[L].v)}else if(V!==a.length&&N.indexOf("E")===-1){for(M=N.indexOf(".")-1,s=V;s>=0;--s)if(!(a[s]==null||"n?".indexOf(a[s].t)===-1)){for(u=a[s].v.indexOf(".")>-1&&s===V?a[s].v.indexOf(".")-1:a[s].v.length-1,q=a[s].v.substr(u+1);u>=0;--u)M>=0&&(a[s].v.charAt(u)==="0"||a[s].v.charAt(u)==="#")&&(q=N.charAt(M--)+q);a[s].v=q,a[s].t="t",L=s}for(M>=0&&L<a.length&&(a[L].v=N.substr(0,M+1)+a[L].v),M=N.indexOf(".")+1,s=V;s<a.length;++s)if(!(a[s]==null||"n?(".indexOf(a[s].t)===-1&&s!==V)){for(u=a[s].v.indexOf(".")>-1&&s===V?a[s].v.indexOf(".")+1:0,q=a[s].v.substr(0,u);u<a[s].v.length;++u)M<N.length&&(q+=N.charAt(M++));a[s].v=q,a[s].t="t",L=s}}}for(s=0;s<a.length;++s)a[s]!=null&&"n?".indexOf(a[s].t)>-1&&(ie=n>1&&t<0&&s>0&&a[s-1].v==="-"?-t:t,a[s].v=Hr(a[s].t,a[s].v,ie),a[s].t="t");var X="";for(s=0;s!==a.length;++s)a[s]!=null&&(X+=a[s].v);return X}var B0=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function M0(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function $s(e,t){var r=Xs(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var i=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,i];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var s=r[0].match(B0),f=r[1].match(B0);return M0(t,s)?[n,r[0]]:M0(t,f)?[n,r[1]]:[n,r[s!=null&&f!=null?2:1]]}return[n,i]}function et(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Ve)[e],n==null&&(n=r.table&&r.table[k0[e]]||Ve[k0[e]]),n==null&&(n=ys[e]||"General");break}if(_n(n,0))return r0(t,r);t instanceof Date&&(t=ga(t,r.date1904));var a=$s(n,t);if(_n(a[1]))return r0(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return zs(a[1],t,r,a[0])}function Oa(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Ve[r]==null){t<0&&(t=r);continue}if(Ve[r]==e){t=r;break}}t<0&&(t=391)}return Ve[t]=e,t}function In(e){for(var t=0;t!=392;++t)e[t]!==void 0&&Oa(e[t],t)}function kn(){Ve=Cs()}var Da=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ks(e){var t=typeof e=="number"?Ve[e]:e;return t=t.replace(Da,"(\\d+)"),new RegExp("^"+t+"$")}function Ys(e,t,r){var n=-1,a=-1,i=-1,s=-1,f=-1,o=-1;(t.match(Da)||[]).forEach(function(x,d){var p=parseInt(r[d+1],10);switch(x.toLowerCase().charAt(0)){case"y":n=p;break;case"d":i=p;break;case"h":s=p;break;case"s":o=p;break;case"m":s>=0?f=p:a=p;break}}),o>=0&&f==-1&&a>=0&&(f=a,a=-1);var l=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);l.length==7&&(l="0"+l),l.length==8&&(l="20"+l);var u=("00"+(s>=0?s:0)).slice(-2)+":"+("00"+(f>=0?f:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return s==-1&&f==-1&&o==-1?l:n==-1&&a==-1&&i==-1?u:l+"T"+u}var js=function(){var e={};e.version="1.2.0";function t(){for(var N=0,V=new Array(256),L=0;L!=256;++L)N=L,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,N=N&1?-306674912^N>>>1:N>>>1,V[L]=N;return typeof Int32Array!="undefined"?new Int32Array(V):V}var r=t();function n(N){var V=0,L=0,X=0,K=typeof Int32Array!="undefined"?new Int32Array(4096):new Array(4096);for(X=0;X!=256;++X)K[X]=N[X];for(X=0;X!=256;++X)for(L=N[X],V=256+X;V<4096;V+=256)L=K[V]=L>>>8^N[L&255];var Y=[];for(X=1;X!=16;++X)Y[X-1]=typeof Int32Array!="undefined"?K.subarray(X*256,X*256+256):K.slice(X*256,X*256+256);return Y}var a=n(r),i=a[0],s=a[1],f=a[2],o=a[3],l=a[4],u=a[5],x=a[6],d=a[7],p=a[8],E=a[9],h=a[10],g=a[11],y=a[12],F=a[13],D=a[14];function M(N,V){for(var L=V^-1,X=0,K=N.length;X<K;)L=L>>>8^r[(L^N.charCodeAt(X++))&255];return~L}function q(N,V){for(var L=V^-1,X=N.length-15,K=0;K<X;)L=D[N[K++]^L&255]^F[N[K++]^L>>8&255]^y[N[K++]^L>>16&255]^g[N[K++]^L>>>24]^h[N[K++]]^E[N[K++]]^p[N[K++]]^d[N[K++]]^x[N[K++]]^u[N[K++]]^l[N[K++]]^o[N[K++]]^f[N[K++]]^s[N[K++]]^i[N[K++]]^r[N[K++]];for(X+=15;K<X;)L=L>>>8^r[(L^N[K++])&255];return~L}function ie(N,V){for(var L=V^-1,X=0,K=N.length,Y=0,te=0;X<K;)Y=N.charCodeAt(X++),Y<128?L=L>>>8^r[(L^Y)&255]:Y<2048?(L=L>>>8^r[(L^(192|Y>>6&31))&255],L=L>>>8^r[(L^(128|Y&63))&255]):Y>=55296&&Y<57344?(Y=(Y&1023)+64,te=N.charCodeAt(X++)&1023,L=L>>>8^r[(L^(240|Y>>8&7))&255],L=L>>>8^r[(L^(128|Y>>2&63))&255],L=L>>>8^r[(L^(128|te>>6&15|(Y&3)<<4))&255],L=L>>>8^r[(L^(128|te&63))&255]):(L=L>>>8^r[(L^(224|Y>>12&15))&255],L=L>>>8^r[(L^(128|Y>>6&63))&255],L=L>>>8^r[(L^(128|Y&63))&255]);return~L}return e.table=r,e.bstr=M,e.buf=q,e.str=ie,e}(),ke=function(){var t={};t.version="1.2.1";function r(c,_){for(var m=c.split("/"),v=_.split("/"),T=0,A=0,I=Math.min(m.length,v.length);T<I;++T){if(A=m[T].length-v[T].length)return A;if(m[T]!=v[T])return m[T]<v[T]?-1:1}return m.length-v.length}function n(c){if(c.charAt(c.length-1)=="/")return c.slice(0,-1).indexOf("/")===-1?c:n(c.slice(0,-1));var _=c.lastIndexOf("/");return _===-1?c:c.slice(0,_+1)}function a(c){if(c.charAt(c.length-1)=="/")return a(c.slice(0,-1));var _=c.lastIndexOf("/");return _===-1?c:c.slice(_+1)}function i(c,_){typeof _=="string"&&(_=new Date(_));var m=_.getHours();m=m<<6|_.getMinutes(),m=m<<5|_.getSeconds()>>>1,c.write_shift(2,m);var v=_.getFullYear()-1980;v=v<<4|_.getMonth()+1,v=v<<5|_.getDate(),c.write_shift(2,v)}function s(c){var _=c.read_shift(2)&65535,m=c.read_shift(2)&65535,v=new Date,T=m&31;m>>>=5;var A=m&15;m>>>=4,v.setMilliseconds(0),v.setFullYear(m+1980),v.setMonth(A-1),v.setDate(T);var I=_&31;_>>>=5;var U=_&63;return _>>>=6,v.setHours(_),v.setMinutes(U),v.setSeconds(I<<1),v}function f(c){Ar(c,0);for(var _={},m=0;c.l<=c.length-4;){var v=c.read_shift(2),T=c.read_shift(2),A=c.l+T,I={};switch(v){case 21589:m=c.read_shift(1),m&1&&(I.mtime=c.read_shift(4)),T>5&&(m&2&&(I.atime=c.read_shift(4)),m&4&&(I.ctime=c.read_shift(4))),I.mtime&&(I.mt=new Date(I.mtime*1e3));break}c.l=A,_[v]=I}return _}var o;function l(){return o||(o={})}function u(c,_){if(c[0]==80&&c[1]==75)return C0(c,_);if((c[0]|32)==109&&(c[1]|32)==105)return ts(c,_);if(c.length<512)throw new Error("CFB file size "+c.length+" < 512");var m=3,v=512,T=0,A=0,I=0,U=0,R=0,k=[],P=c.slice(0,512);Ar(P,0);var j=x(P);switch(m=j[0],m){case 3:v=512;break;case 4:v=4096;break;case 0:if(j[1]==0)return C0(c,_);default:throw new Error("Major Version: Expected 3 or 4 saw "+m)}v!==512&&(P=c.slice(0,v),Ar(P,28));var ee=c.slice(0,v);d(P,m);var le=P.read_shift(4,"i");if(m===3&&le!==0)throw new Error("# Directory Sectors: Expected 0 saw "+le);P.l+=4,I=P.read_shift(4,"i"),P.l+=4,P.chk("00100000","Mini Stream Cutoff Size: "),U=P.read_shift(4,"i"),T=P.read_shift(4,"i"),R=P.read_shift(4,"i"),A=P.read_shift(4,"i");for(var J=-1,se=0;se<109&&(J=P.read_shift(4,"i"),!(J<0));++se)k[se]=J;var ve=p(c,v);g(R,A,ve,v,k);var be=F(ve,I,k,v);be[I].name="!Directory",T>0&&U!==te&&(be[U].name="!MiniFAT"),be[k[0]].name="!FAT",be.fat_addrs=k,be.ssz=v;var Ue={},ur=[],kt=[],Pt=[];D(I,be,ve,ur,T,Ue,kt,U),E(kt,Pt,ur),ur.shift();var Lt={FileIndex:kt,FullPaths:Pt};return _&&_.raw&&(Lt.raw={header:ee,sectors:ve}),Lt}function x(c){if(c[c.l]==80&&c[c.l+1]==75)return[0,0];c.chk(we,"Header Signature: "),c.l+=16;var _=c.read_shift(2,"u");return[c.read_shift(2,"u"),_]}function d(c,_){var m=9;switch(c.l+=2,m=c.read_shift(2)){case 9:if(_!=3)throw new Error("Sector Shift: Expected 9 saw "+m);break;case 12:if(_!=4)throw new Error("Sector Shift: Expected 12 saw "+m);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+m)}c.chk("0600","Mini Sector Shift: "),c.chk("000000000000","Reserved: ")}function p(c,_){for(var m=Math.ceil(c.length/_)-1,v=[],T=1;T<m;++T)v[T-1]=c.slice(T*_,(T+1)*_);return v[m-1]=c.slice(m*_),v}function E(c,_,m){for(var v=0,T=0,A=0,I=0,U=0,R=m.length,k=[],P=[];v<R;++v)k[v]=P[v]=v,_[v]=m[v];for(;U<P.length;++U)v=P[U],T=c[v].L,A=c[v].R,I=c[v].C,k[v]===v&&(T!==-1&&k[T]!==T&&(k[v]=k[T]),A!==-1&&k[A]!==A&&(k[v]=k[A])),I!==-1&&(k[I]=v),T!==-1&&v!=k[v]&&(k[T]=k[v],P.lastIndexOf(T)<U&&P.push(T)),A!==-1&&v!=k[v]&&(k[A]=k[v],P.lastIndexOf(A)<U&&P.push(A));for(v=1;v<R;++v)k[v]===v&&(A!==-1&&k[A]!==A?k[v]=k[A]:T!==-1&&k[T]!==T&&(k[v]=k[T]));for(v=1;v<R;++v)if(c[v].type!==0){if(U=v,U!=k[U])do U=k[U],_[v]=_[U]+"/"+_[v];while(U!==0&&k[U]!==-1&&U!=k[U]);k[v]=-1}for(_[0]+="/",v=1;v<R;++v)c[v].type!==2&&(_[v]+="/")}function h(c,_,m){for(var v=c.start,T=c.size,A=[],I=v;m&&T>0&&I>=0;)A.push(_.slice(I*Y,I*Y+Y)),T-=Y,I=ot(m,I*4);return A.length===0?b(0):ar(A).slice(0,c.size)}function g(c,_,m,v,T){var A=te;if(c===te){if(_!==0)throw new Error("DIFAT chain shorter than expected")}else if(c!==-1){var I=m[c],U=(v>>>2)-1;if(!I)return;for(var R=0;R<U&&(A=ot(I,R*4))!==te;++R)T.push(A);g(ot(I,v-4),_-1,m,v,T)}}function y(c,_,m,v,T){var A=[],I=[];T||(T=[]);var U=v-1,R=0,k=0;for(R=_;R>=0;){T[R]=!0,A[A.length]=R,I.push(c[R]);var P=m[Math.floor(R*4/v)];if(k=R*4&U,v<4+k)throw new Error("FAT boundary crossed: "+R+" 4 "+v);if(!c[P])break;R=ot(c[P],k)}return{nodes:A,data:z0([I])}}function F(c,_,m,v){var T=c.length,A=[],I=[],U=[],R=[],k=v-1,P=0,j=0,ee=0,le=0;for(P=0;P<T;++P)if(U=[],ee=P+_,ee>=T&&(ee-=T),!I[ee]){R=[];var J=[];for(j=ee;j>=0;){J[j]=!0,I[j]=!0,U[U.length]=j,R.push(c[j]);var se=m[Math.floor(j*4/v)];if(le=j*4&k,v<4+le)throw new Error("FAT boundary crossed: "+j+" 4 "+v);if(!c[se]||(j=ot(c[se],le),J[j]))break}A[ee]={nodes:U,data:z0([R])}}return A}function D(c,_,m,v,T,A,I,U){for(var R=0,k=v.length?2:0,P=_[c].data,j=0,ee=0,le;j<P.length;j+=128){var J=P.slice(j,j+128);Ar(J,64),ee=J.read_shift(2),le=c0(J,0,ee-k),v.push(le);var se={name:le,type:J.read_shift(1),color:J.read_shift(1),L:J.read_shift(4,"i"),R:J.read_shift(4,"i"),C:J.read_shift(4,"i"),clsid:J.read_shift(16),state:J.read_shift(4,"i"),start:0,size:0},ve=J.read_shift(2)+J.read_shift(2)+J.read_shift(2)+J.read_shift(2);ve!==0&&(se.ct=M(J,J.l-8));var be=J.read_shift(2)+J.read_shift(2)+J.read_shift(2)+J.read_shift(2);be!==0&&(se.mt=M(J,J.l-8)),se.start=J.read_shift(4,"i"),se.size=J.read_shift(4,"i"),se.size<0&&se.start<0&&(se.size=se.type=0,se.start=te,se.name=""),se.type===5?(R=se.start,T>0&&R!==te&&(_[R].name="!StreamData")):se.size>=4096?(se.storage="fat",_[se.start]===void 0&&(_[se.start]=y(m,se.start,_.fat_addrs,_.ssz)),_[se.start].name=se.name,se.content=_[se.start].data.slice(0,se.size)):(se.storage="minifat",se.size<0?se.size=0:R!==te&&se.start!==te&&_[R]&&(se.content=h(se,_[R].data,(_[U]||{}).data))),se.content&&Ar(se.content,0),A[le]=se,I.push(se)}}function M(c,_){return new Date((Fr(c,_+4)/1e7*Math.pow(2,32)+Fr(c,_)/1e7-11644473600)*1e3)}function q(c,_){return l(),u(o.readFileSync(c),_)}function ie(c,_){var m=_&&_.type;switch(m||ye&&Buffer.isBuffer(c)&&(m="buffer"),m||"base64"){case"file":return q(c,_);case"base64":return u(Pr(zr(c)),_);case"binary":return u(Pr(c),_)}return u(c,_)}function N(c,_){var m=_||{},v=m.root||"Root Entry";if(c.FullPaths||(c.FullPaths=[]),c.FileIndex||(c.FileIndex=[]),c.FullPaths.length!==c.FileIndex.length)throw new Error("inconsistent CFB structure");c.FullPaths.length===0&&(c.FullPaths[0]=v+"/",c.FileIndex[0]={name:v,type:5}),m.CLSID&&(c.FileIndex[0].clsid=m.CLSID),V(c)}function V(c){var _="Sh33tJ5";if(!ke.find(c,"/"+_)){var m=b(4);m[0]=55,m[1]=m[3]=50,m[2]=54,c.FileIndex.push({name:_,type:2,content:m,size:4,L:69,R:69,C:69}),c.FullPaths.push(c.FullPaths[0]+_),L(c)}}function L(c,_){N(c);for(var m=!1,v=!1,T=c.FullPaths.length-1;T>=0;--T){var A=c.FileIndex[T];switch(A.type){case 0:v?m=!0:(c.FileIndex.pop(),c.FullPaths.pop());break;case 1:case 2:case 5:v=!0,isNaN(A.R*A.L*A.C)&&(m=!0),A.R>-1&&A.L>-1&&A.R==A.L&&(m=!0);break;default:m=!0;break}}if(!(!m&&!_)){var I=new Date(1987,1,19),U=0,R=Object.create?Object.create(null):{},k=[];for(T=0;T<c.FullPaths.length;++T)R[c.FullPaths[T]]=!0,c.FileIndex[T].type!==0&&k.push([c.FullPaths[T],c.FileIndex[T]]);for(T=0;T<k.length;++T){var P=n(k[T][0]);v=R[P],v||(k.push([P,{name:a(P).replace("/",""),type:1,clsid:He,ct:I,mt:I,content:null}]),R[P]=!0)}for(k.sort(function(le,J){return r(le[0],J[0])}),c.FullPaths=[],c.FileIndex=[],T=0;T<k.length;++T)c.FullPaths[T]=k[T][0],c.FileIndex[T]=k[T][1];for(T=0;T<k.length;++T){var j=c.FileIndex[T],ee=c.FullPaths[T];if(j.name=a(ee).replace("/",""),j.L=j.R=j.C=-(j.color=1),j.size=j.content?j.content.length:0,j.start=0,j.clsid=j.clsid||He,T===0)j.C=k.length>1?1:-1,j.size=0,j.type=5;else if(ee.slice(-1)=="/"){for(U=T+1;U<k.length&&n(c.FullPaths[U])!=ee;++U);for(j.C=U>=k.length?-1:U,U=T+1;U<k.length&&n(c.FullPaths[U])!=n(ee);++U);j.R=U>=k.length?-1:U,j.type=1}else n(c.FullPaths[T+1]||"")==n(ee)&&(j.R=T+1),j.type=2}}}function X(c,_){var m=_||{};if(m.fileType=="mad")return ns(c,m);switch(L(c),m.fileType){case"zip":return qi(c,m)}var v=function(le){for(var J=0,se=0,ve=0;ve<le.FileIndex.length;++ve){var be=le.FileIndex[ve];if(!!be.content){var Ue=be.content.length;Ue>0&&(Ue<4096?J+=Ue+63>>6:se+=Ue+511>>9)}}for(var ur=le.FullPaths.length+3>>2,kt=J+7>>3,Pt=J+127>>7,Lt=kt+se+ur+Pt,it=Lt+127>>7,Vn=it<=109?0:Math.ceil((it-109)/127);Lt+it+Vn+127>>7>it;)Vn=++it<=109?0:Math.ceil((it-109)/127);var Wr=[1,Vn,it,Pt,ur,se,J,0];return le.FileIndex[0].size=J<<6,Wr[7]=(le.FileIndex[0].start=Wr[0]+Wr[1]+Wr[2]+Wr[3]+Wr[4]+Wr[5])+(Wr[6]+7>>3),Wr}(c),T=b(v[7]<<9),A=0,I=0;{for(A=0;A<8;++A)T.write_shift(1,me[A]);for(A=0;A<8;++A)T.write_shift(2,0);for(T.write_shift(2,62),T.write_shift(2,3),T.write_shift(2,65534),T.write_shift(2,9),T.write_shift(2,6),A=0;A<3;++A)T.write_shift(2,0);for(T.write_shift(4,0),T.write_shift(4,v[2]),T.write_shift(4,v[0]+v[1]+v[2]+v[3]-1),T.write_shift(4,0),T.write_shift(4,1<<12),T.write_shift(4,v[3]?v[0]+v[1]+v[2]-1:te),T.write_shift(4,v[3]),T.write_shift(-4,v[1]?v[0]-1:te),T.write_shift(4,v[1]),A=0;A<109;++A)T.write_shift(-4,A<v[2]?v[1]+A:-1)}if(v[1])for(I=0;I<v[1];++I){for(;A<236+I*127;++A)T.write_shift(-4,A<v[2]?v[1]+A:-1);T.write_shift(-4,I===v[1]-1?te:I+1)}var U=function(le){for(I+=le;A<I-1;++A)T.write_shift(-4,A+1);le&&(++A,T.write_shift(-4,te))};for(I=A=0,I+=v[1];A<I;++A)T.write_shift(-4,Be.DIFSECT);for(I+=v[2];A<I;++A)T.write_shift(-4,Be.FATSECT);U(v[3]),U(v[4]);for(var R=0,k=0,P=c.FileIndex[0];R<c.FileIndex.length;++R)P=c.FileIndex[R],P.content&&(k=P.content.length,!(k<4096)&&(P.start=I,U(k+511>>9)));for(U(v[6]+7>>3);T.l&511;)T.write_shift(-4,Be.ENDOFCHAIN);for(I=A=0,R=0;R<c.FileIndex.length;++R)P=c.FileIndex[R],P.content&&(k=P.content.length,!(!k||k>=4096)&&(P.start=I,U(k+63>>6)));for(;T.l&511;)T.write_shift(-4,Be.ENDOFCHAIN);for(A=0;A<v[4]<<2;++A){var j=c.FullPaths[A];if(!j||j.length===0){for(R=0;R<17;++R)T.write_shift(4,0);for(R=0;R<3;++R)T.write_shift(4,-1);for(R=0;R<12;++R)T.write_shift(4,0);continue}P=c.FileIndex[A],A===0&&(P.start=P.size?P.start-1:te);var ee=A===0&&m.root||P.name;if(k=2*(ee.length+1),T.write_shift(64,ee,"utf16le"),T.write_shift(2,k),T.write_shift(1,P.type),T.write_shift(1,P.color),T.write_shift(-4,P.L),T.write_shift(-4,P.R),T.write_shift(-4,P.C),P.clsid)T.write_shift(16,P.clsid,"hex");else for(R=0;R<4;++R)T.write_shift(4,0);T.write_shift(4,P.state||0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,0),T.write_shift(4,P.start),T.write_shift(4,P.size),T.write_shift(4,0)}for(A=1;A<c.FileIndex.length;++A)if(P=c.FileIndex[A],P.size>=4096)if(T.l=P.start+1<<9,ye&&Buffer.isBuffer(P.content))P.content.copy(T,T.l,0,P.size),T.l+=P.size+511&-512;else{for(R=0;R<P.size;++R)T.write_shift(1,P.content[R]);for(;R&511;++R)T.write_shift(1,0)}for(A=1;A<c.FileIndex.length;++A)if(P=c.FileIndex[A],P.size>0&&P.size<4096)if(ye&&Buffer.isBuffer(P.content))P.content.copy(T,T.l,0,P.size),T.l+=P.size+63&-64;else{for(R=0;R<P.size;++R)T.write_shift(1,P.content[R]);for(;R&63;++R)T.write_shift(1,0)}if(ye)T.l=T.length;else for(;T.l<T.length;)T.write_shift(1,0);return T}function K(c,_){var m=c.FullPaths.map(function(R){return R.toUpperCase()}),v=m.map(function(R){var k=R.split("/");return k[k.length-(R.slice(-1)=="/"?2:1)]}),T=!1;_.charCodeAt(0)===47?(T=!0,_=m[0].slice(0,-1)+_):T=_.indexOf("/")!==-1;var A=_.toUpperCase(),I=T===!0?m.indexOf(A):v.indexOf(A);if(I!==-1)return c.FileIndex[I];var U=!A.match(un);for(A=A.replace(bt,""),U&&(A=A.replace(un,"!")),I=0;I<m.length;++I)if((U?m[I].replace(un,"!"):m[I]).replace(bt,"")==A||(U?v[I].replace(un,"!"):v[I]).replace(bt,"")==A)return c.FileIndex[I];return null}var Y=64,te=-2,we="d0cf11e0a1b11ae1",me=[208,207,17,224,161,177,26,225],He="00000000000000000000000000000000",Be={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:te,FREESECT:-1,HEADER_SIGNATURE:we,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:He,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function gr(c,_,m){l();var v=X(c,m);o.writeFileSync(_,v)}function Ge(c){for(var _=new Array(c.length),m=0;m<c.length;++m)_[m]=String.fromCharCode(c[m]);return _.join("")}function rr(c,_){var m=X(c,_);switch(_&&_.type||"buffer"){case"file":return l(),o.writeFileSync(_.filename,m),m;case"binary":return typeof m=="string"?m:Ge(m);case"base64":return zt(typeof m=="string"?m:Ge(m));case"buffer":if(ye)return Buffer.isBuffer(m)?m:Kr(m);case"array":return typeof m=="string"?Pr(m):m}return m}var lr;function S(c){try{var _=c.InflateRaw,m=new _;if(m._processChunk(new Uint8Array([3,0]),m._finishFlushFlag),m.bytesRead)lr=c;else throw new Error("zlib does not expose bytesRead")}catch(v){console.error("cannot use native zlib: "+(v.message||v))}}function B(c,_){if(!lr)return nn(c,_);var m=lr.InflateRaw,v=new m,T=v._processChunk(c.slice(c.l),v._finishFlushFlag);return c.l+=v.bytesRead,T}function O(c){return lr?lr.deflateRawSync(c):dr(c)}var C=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],H=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],ce=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function he(c){var _=(c<<1|c<<11)&139536|(c<<5|c<<15)&558144;return(_>>16|_>>8|_)&255}for(var oe=typeof Uint8Array!="undefined",re=oe?new Uint8Array(1<<8):[],Oe=0;Oe<1<<8;++Oe)re[Oe]=he(Oe);function Te(c,_){var m=re[c&255];return _<=8?m>>>8-_:(m=m<<8|re[c>>8&255],_<=16?m>>>16-_:(m=m<<8|re[c>>16&255],m>>>24-_))}function je(c,_){var m=_&7,v=_>>>3;return(c[v]|(m<=6?0:c[v+1]<<8))>>>m&3}function Ae(c,_){var m=_&7,v=_>>>3;return(c[v]|(m<=5?0:c[v+1]<<8))>>>m&7}function Rr(c,_){var m=_&7,v=_>>>3;return(c[v]|(m<=4?0:c[v+1]<<8))>>>m&15}function z(c,_){var m=_&7,v=_>>>3;return(c[v]|(m<=3?0:c[v+1]<<8))>>>m&31}function w(c,_){var m=_&7,v=_>>>3;return(c[v]|(m<=1?0:c[v+1]<<8))>>>m&127}function W(c,_,m){var v=_&7,T=_>>>3,A=(1<<m)-1,I=c[T]>>>v;return m<8-v||(I|=c[T+1]<<8-v,m<16-v)||(I|=c[T+2]<<16-v,m<24-v)||(I|=c[T+3]<<24-v),I&A}function fe(c,_,m){var v=_&7,T=_>>>3;return v<=5?c[T]|=(m&7)<<v:(c[T]|=m<<v&255,c[T+1]=(m&7)>>8-v),_+3}function de(c,_,m){var v=_&7,T=_>>>3;return m=(m&1)<<v,c[T]|=m,_+1}function ge(c,_,m){var v=_&7,T=_>>>3;return m<<=v,c[T]|=m&255,m>>>=8,c[T+1]=m,_+8}function ne(c,_,m){var v=_&7,T=_>>>3;return m<<=v,c[T]|=m&255,m>>>=8,c[T+1]=m&255,c[T+2]=m>>>8,_+16}function pe(c,_){var m=c.length,v=2*m>_?2*m:_+5,T=0;if(m>=_)return c;if(ye){var A=N0(v);if(c.copy)c.copy(A);else for(;T<c.length;++T)A[T]=c[T];return A}else if(oe){var I=new Uint8Array(v);if(I.set)I.set(c);else for(;T<m;++T)I[T]=c[T];return I}return c.length=v,c}function Ce(c){for(var _=new Array(c),m=0;m<c;++m)_[m]=0;return _}function ue(c,_,m){var v=1,T=0,A=0,I=0,U=0,R=c.length,k=oe?new Uint16Array(32):Ce(32);for(A=0;A<32;++A)k[A]=0;for(A=R;A<m;++A)c[A]=0;R=c.length;var P=oe?new Uint16Array(R):Ce(R);for(A=0;A<R;++A)k[T=c[A]]++,v<T&&(v=T),P[A]=0;for(k[0]=0,A=1;A<=v;++A)k[A+16]=U=U+k[A-1]<<1;for(A=0;A<R;++A)U=c[A],U!=0&&(P[A]=k[U+16]++);var j=0;for(A=0;A<R;++A)if(j=c[A],j!=0)for(U=Te(P[A],v)>>v-j,I=(1<<v+4-j)-1;I>=0;--I)_[U|I<<j]=j&15|A<<4;return v}var Ee=oe?new Uint16Array(512):Ce(512),xe=oe?new Uint16Array(32):Ce(32);if(!oe){for(var Pe=0;Pe<512;++Pe)Ee[Pe]=0;for(Pe=0;Pe<32;++Pe)xe[Pe]=0}(function(){for(var c=[],_=0;_<32;_++)c.push(5);ue(c,xe,32);var m=[];for(_=0;_<=143;_++)m.push(8);for(;_<=255;_++)m.push(9);for(;_<=279;_++)m.push(7);for(;_<=287;_++)m.push(8);ue(m,Ee,288)})();var yr=function(){for(var _=oe?new Uint8Array(32768):[],m=0,v=0;m<ce.length-1;++m)for(;v<ce[m+1];++v)_[v]=m;for(;v<32768;++v)_[v]=29;var T=oe?new Uint8Array(259):[];for(m=0,v=0;m<H.length-1;++m)for(;v<H[m+1];++v)T[v]=m;function A(U,R){for(var k=0;k<U.length;){var P=Math.min(65535,U.length-k),j=k+P==U.length;for(R.write_shift(1,+j),R.write_shift(2,P),R.write_shift(2,~P&65535);P-- >0;)R[R.l++]=U[k++]}return R.l}function I(U,R){for(var k=0,P=0,j=oe?new Uint16Array(32768):[];P<U.length;){var ee=Math.min(65535,U.length-P);if(ee<10){for(k=fe(R,k,+(P+ee==U.length)),k&7&&(k+=8-(k&7)),R.l=k/8|0,R.write_shift(2,ee),R.write_shift(2,~ee&65535);ee-- >0;)R[R.l++]=U[P++];k=R.l*8;continue}k=fe(R,k,+(P+ee==U.length)+2);for(var le=0;ee-- >0;){var J=U[P];le=(le<<5^J)&32767;var se=-1,ve=0;if((se=j[le])&&(se|=P&-32768,se>P&&(se-=32768),se<P))for(;U[se+ve]==U[P+ve]&&ve<250;)++ve;if(ve>2){J=T[ve],J<=22?k=ge(R,k,re[J+1]>>1)-1:(ge(R,k,3),k+=5,ge(R,k,re[J-23]>>5),k+=3);var be=J<8?0:J-4>>2;be>0&&(ne(R,k,ve-H[J]),k+=be),J=_[P-se],k=ge(R,k,re[J]>>3),k-=3;var Ue=J<4?0:J-2>>1;Ue>0&&(ne(R,k,P-se-ce[J]),k+=Ue);for(var ur=0;ur<ve;++ur)j[le]=P&32767,le=(le<<5^U[P])&32767,++P;ee-=ve-1}else J<=143?J=J+48:k=de(R,k,1),k=ge(R,k,re[J]),j[le]=P&32767,++P}k=ge(R,k,0)-1}return R.l=(k+7)/8|0,R.l}return function(R,k){return R.length<8?A(R,k):I(R,k)}}();function dr(c){var _=b(50+Math.floor(c.length*1.1)),m=yr(c,_);return _.slice(0,m)}var Xe=oe?new Uint16Array(32768):Ce(32768),or=oe?new Uint16Array(32768):Ce(32768),ze=oe?new Uint16Array(128):Ce(128),Yr=1,tn=1;function at(c,_){var m=z(c,_)+257;_+=5;var v=z(c,_)+1;_+=5;var T=Rr(c,_)+4;_+=4;for(var A=0,I=oe?new Uint8Array(19):Ce(19),U=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],R=1,k=oe?new Uint8Array(8):Ce(8),P=oe?new Uint8Array(8):Ce(8),j=I.length,ee=0;ee<T;++ee)I[C[ee]]=A=Ae(c,_),R<A&&(R=A),k[A]++,_+=3;var le=0;for(k[0]=0,ee=1;ee<=R;++ee)P[ee]=le=le+k[ee-1]<<1;for(ee=0;ee<j;++ee)(le=I[ee])!=0&&(U[ee]=P[le]++);var J=0;for(ee=0;ee<j;++ee)if(J=I[ee],J!=0){le=re[U[ee]]>>8-J;for(var se=(1<<7-J)-1;se>=0;--se)ze[le|se<<J]=J&7|ee<<3}var ve=[];for(R=1;ve.length<m+v;)switch(le=ze[w(c,_)],_+=le&7,le>>>=3){case 16:for(A=3+je(c,_),_+=2,le=ve[ve.length-1];A-- >0;)ve.push(le);break;case 17:for(A=3+Ae(c,_),_+=3;A-- >0;)ve.push(0);break;case 18:for(A=11+w(c,_),_+=7;A-- >0;)ve.push(0);break;default:ve.push(le),R<le&&(R=le);break}var be=ve.slice(0,m),Ue=ve.slice(m);for(ee=m;ee<286;++ee)be[ee]=0;for(ee=v;ee<30;++ee)Ue[ee]=0;return Yr=ue(be,Xe,286),tn=ue(Ue,or,30),_}function Un(c,_){if(c[0]==3&&!(c[1]&3))return[ct(_),2];for(var m=0,v=0,T=N0(_||1<<18),A=0,I=T.length>>>0,U=0,R=0;(v&1)==0;){if(v=Ae(c,m),m+=3,v>>>1==0){m&7&&(m+=8-(m&7));var k=c[m>>>3]|c[(m>>>3)+1]<<8;if(m+=32,k>0)for(!_&&I<A+k&&(T=pe(T,A+k),I=T.length);k-- >0;)T[A++]=c[m>>>3],m+=8;continue}else v>>1==1?(U=9,R=5):(m=at(c,m),U=Yr,R=tn);for(;;){!_&&I<A+32767&&(T=pe(T,A+32767),I=T.length);var P=W(c,m,U),j=v>>>1==1?Ee[P]:Xe[P];if(m+=j&15,j>>>=4,(j>>>8&255)===0)T[A++]=j;else{if(j==256)break;j-=257;var ee=j<8?0:j-4>>2;ee>5&&(ee=0);var le=A+H[j];ee>0&&(le+=W(c,m,ee),m+=ee),P=W(c,m,R),j=v>>>1==1?xe[P]:or[P],m+=j&15,j>>>=4;var J=j<4?0:j-2>>1,se=ce[j];for(J>0&&(se+=W(c,m,J),m+=J),!_&&I<le&&(T=pe(T,le+100),I=T.length);A<le;)T[A]=T[A-se],++A}}}return _?[T,m+7>>>3]:[T.slice(0,A),m+7>>>3]}function nn(c,_){var m=c.slice(c.l||0),v=Un(m,_);return c.l+=v[1],v[0]}function an(c,_){if(c)typeof console!="undefined"&&console.error(_);else throw new Error(_)}function C0(c,_){var m=c;Ar(m,0);var v=[],T=[],A={FileIndex:v,FullPaths:T};N(A,{root:_.root});for(var I=m.length-4;(m[I]!=80||m[I+1]!=75||m[I+2]!=5||m[I+3]!=6)&&I>=0;)--I;m.l=I+4,m.l+=4;var U=m.read_shift(2);m.l+=6;var R=m.read_shift(4);for(m.l=R,I=0;I<U;++I){m.l+=20;var k=m.read_shift(4),P=m.read_shift(4),j=m.read_shift(2),ee=m.read_shift(2),le=m.read_shift(2);m.l+=8;var J=m.read_shift(4),se=f(m.slice(m.l+j,m.l+j+ee));m.l+=j+ee+le;var ve=m.l;m.l=J+4,ji(m,k,P,A,se),m.l=ve}return A}function ji(c,_,m,v,T){c.l+=2;var A=c.read_shift(2),I=c.read_shift(2),U=s(c);if(A&8257)throw new Error("Unsupported ZIP encryption");for(var R=c.read_shift(4),k=c.read_shift(4),P=c.read_shift(4),j=c.read_shift(2),ee=c.read_shift(2),le="",J=0;J<j;++J)le+=String.fromCharCode(c[c.l++]);if(ee){var se=f(c.slice(c.l,c.l+ee));(se[21589]||{}).mt&&(U=se[21589].mt),((T||{})[21589]||{}).mt&&(U=T[21589].mt)}c.l+=ee;var ve=c.slice(c.l,c.l+k);switch(I){case 8:ve=B(c,P);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+I)}var be=!1;A&8&&(R=c.read_shift(4),R==134695760&&(R=c.read_shift(4),be=!0),k=c.read_shift(4),P=c.read_shift(4)),k!=_&&an(be,"Bad compressed size: "+_+" != "+k),P!=m&&an(be,"Bad uncompressed size: "+m+" != "+P),Wn(v,le,ve,{unsafe:!0,mt:U})}function qi(c,_){var m=_||{},v=[],T=[],A=b(1),I=m.compression?8:0,U=0,R=0,k=0,P=0,j=0,ee=c.FullPaths[0],le=ee,J=c.FileIndex[0],se=[],ve=0;for(R=1;R<c.FullPaths.length;++R)if(le=c.FullPaths[R].slice(ee.length),J=c.FileIndex[R],!(!J.size||!J.content||le=="Sh33tJ5")){var be=P,Ue=b(le.length);for(k=0;k<le.length;++k)Ue.write_shift(1,le.charCodeAt(k)&127);Ue=Ue.slice(0,Ue.l),se[j]=js.buf(J.content,0);var ur=J.content;I==8&&(ur=O(ur)),A=b(30),A.write_shift(4,67324752),A.write_shift(2,20),A.write_shift(2,U),A.write_shift(2,I),J.mt?i(A,J.mt):A.write_shift(4,0),A.write_shift(-4,se[j]),A.write_shift(4,ur.length),A.write_shift(4,J.content.length),A.write_shift(2,Ue.length),A.write_shift(2,0),P+=A.length,v.push(A),P+=Ue.length,v.push(Ue),P+=ur.length,v.push(ur),A=b(46),A.write_shift(4,33639248),A.write_shift(2,0),A.write_shift(2,20),A.write_shift(2,U),A.write_shift(2,I),A.write_shift(4,0),A.write_shift(-4,se[j]),A.write_shift(4,ur.length),A.write_shift(4,J.content.length),A.write_shift(2,Ue.length),A.write_shift(2,0),A.write_shift(2,0),A.write_shift(2,0),A.write_shift(2,0),A.write_shift(4,0),A.write_shift(4,be),ve+=A.l,T.push(A),ve+=Ue.length,T.push(Ue),++j}return A=b(22),A.write_shift(4,101010256),A.write_shift(2,0),A.write_shift(2,0),A.write_shift(2,j),A.write_shift(2,j),A.write_shift(4,ve),A.write_shift(4,P),A.write_shift(2,0),ar([ar(v),ar(T),A])}var sn={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Ji(c,_){if(c.ctype)return c.ctype;var m=c.name||"",v=m.match(/\.([^\.]+)$/);return v&&sn[v[1]]||_&&(v=(m=_).match(/[\.\\]([^\.\\])+$/),v&&sn[v[1]])?sn[v[1]]:"application/octet-stream"}function Zi(c){for(var _=zt(c),m=[],v=0;v<_.length;v+=76)m.push(_.slice(v,v+76));return m.join(`\r
`)+`\r
`}function Qi(c){var _=c.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(k){var P=k.charCodeAt(0).toString(16).toUpperCase();return"="+(P.length==1?"0"+P:P)});_=_.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),_.charAt(0)==`
`&&(_="=0D"+_.slice(1)),_=_.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var m=[],v=_.split(`\r
`),T=0;T<v.length;++T){var A=v[T];if(A.length==0){m.push("");continue}for(var I=0;I<A.length;){var U=76,R=A.slice(I,I+U);R.charAt(U-1)=="="?U--:R.charAt(U-2)=="="?U-=2:R.charAt(U-3)=="="&&(U-=3),R=A.slice(I,I+U),I+=U,I<A.length&&(R+="="),m.push(R)}}return m.join(`\r
`)}function es(c){for(var _=[],m=0;m<c.length;++m){for(var v=c[m];m<=c.length&&v.charAt(v.length-1)=="=";)v=v.slice(0,v.length-1)+c[++m];_.push(v)}for(var T=0;T<_.length;++T)_[T]=_[T].replace(/[=][0-9A-Fa-f]{2}/g,function(A){return String.fromCharCode(parseInt(A.slice(1),16))});return Pr(_.join(`\r
`))}function rs(c,_,m){for(var v="",T="",A="",I,U=0;U<10;++U){var R=_[U];if(!R||R.match(/^\s*$/))break;var k=R.match(/^(.*?):\s*([^\s].*)$/);if(k)switch(k[1].toLowerCase()){case"content-location":v=k[2].trim();break;case"content-type":A=k[2].trim();break;case"content-transfer-encoding":T=k[2].trim();break}}switch(++U,T.toLowerCase()){case"base64":I=Pr(zr(_.slice(U).join("")));break;case"quoted-printable":I=es(_.slice(U));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+T)}var P=Wn(c,v.slice(m.length),I,{unsafe:!0});A&&(P.ctype=A)}function ts(c,_){if(Ge(c.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var m=_&&_.root||"",v=(ye&&Buffer.isBuffer(c)?c.toString("binary"):Ge(c)).split(`\r
`),T=0,A="";for(T=0;T<v.length;++T)if(A=v[T],!!/^Content-Location:/i.test(A)&&(A=A.slice(A.indexOf("file")),m||(m=A.slice(0,A.lastIndexOf("/")+1)),A.slice(0,m.length)!=m))for(;m.length>0&&(m=m.slice(0,m.length-1),m=m.slice(0,m.lastIndexOf("/")+1),A.slice(0,m.length)!=m););var I=(v[1]||"").match(/boundary="(.*?)"/);if(!I)throw new Error("MAD cannot find boundary");var U="--"+(I[1]||""),R=[],k=[],P={FileIndex:R,FullPaths:k};N(P);var j,ee=0;for(T=0;T<v.length;++T){var le=v[T];le!==U&&le!==U+"--"||(ee++&&rs(P,v.slice(j,T),m),j=T)}return P}function ns(c,_){var m=_||{},v=m.boundary||"SheetJS";v="------="+v;for(var T=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+v.slice(2)+'"',"","",""],A=c.FullPaths[0],I=A,U=c.FileIndex[0],R=1;R<c.FullPaths.length;++R)if(I=c.FullPaths[R].slice(A.length),U=c.FileIndex[R],!(!U.size||!U.content||I=="Sh33tJ5")){I=I.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(ve){return"_x"+ve.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(ve){return"_u"+ve.charCodeAt(0).toString(16)+"_"});for(var k=U.content,P=ye&&Buffer.isBuffer(k)?k.toString("binary"):Ge(k),j=0,ee=Math.min(1024,P.length),le=0,J=0;J<=ee;++J)(le=P.charCodeAt(J))>=32&&le<128&&++j;var se=j>=ee*4/5;T.push(v),T.push("Content-Location: "+(m.root||"file:///C:/SheetJS/")+I),T.push("Content-Transfer-Encoding: "+(se?"quoted-printable":"base64")),T.push("Content-Type: "+Ji(U,I)),T.push(""),T.push(se?Qi(P):Zi(P))}return T.push(v+`--\r
`),T.join(`\r
`)}function as(c){var _={};return N(_,c),_}function Wn(c,_,m,v){var T=v&&v.unsafe;T||N(c);var A=!T&&ke.find(c,_);if(!A){var I=c.FullPaths[0];_.slice(0,I.length)==I?I=_:(I.slice(-1)!="/"&&(I+="/"),I=(I+_).replace("//","/")),A={name:a(_),type:2},c.FileIndex.push(A),c.FullPaths.push(I),T||ke.utils.cfb_gc(c)}return A.content=m,A.size=m?m.length:0,v&&(v.CLSID&&(A.clsid=v.CLSID),v.mt&&(A.mt=v.mt),v.ct&&(A.ct=v.ct)),A}function is(c,_){N(c);var m=ke.find(c,_);if(m){for(var v=0;v<c.FileIndex.length;++v)if(c.FileIndex[v]==m)return c.FileIndex.splice(v,1),c.FullPaths.splice(v,1),!0}return!1}function ss(c,_,m){N(c);var v=ke.find(c,_);if(v){for(var T=0;T<c.FileIndex.length;++T)if(c.FileIndex[T]==v)return c.FileIndex[T].name=a(m),c.FullPaths[T]=m,!0}return!1}function fs(c){L(c,!0)}return t.find=K,t.read=ie,t.parse=u,t.write=rr,t.writeFile=gr,t.utils={cfb_new:as,cfb_add:Wn,cfb_del:is,cfb_mov:ss,cfb_gc:fs,ReadShift:Wt,CheckField:Ka,prep_blob:Ar,bconcat:ar,use_zlib:S,_deflateRaw:dr,_inflateRaw:nn,consts:Be},t}();function qs(e){return typeof e=="string"?Rn(e):Array.isArray(e)?ws(e):e}function Jt(e,t,r){if(typeof Deno!="undefined"){if(r&&typeof t=="string")switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=Rn(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n=r=="utf8"?Vr(t):t;if(typeof IE_SaveFile!="undefined")return IE_SaveFile(n,e);if(typeof Blob!="undefined"){var a=new Blob([qs(n)],{type:"application/octet-stream"});if(typeof navigator!="undefined"&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if(typeof saveAs!="undefined")return saveAs(a,e);if(typeof URL!="undefined"&&typeof document!="undefined"&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if(typeof chrome=="object"&&typeof(chrome.downloads||{}).download=="function")return URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var s=document.createElement("a");if(s.download!=null)return s.download=e,s.href=i,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL&&typeof setTimeout!="undefined"&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if(typeof $!="undefined"&&typeof File!="undefined"&&typeof Folder!="undefined")try{var f=File(e);return f.open("w"),f.encoding="binary",Array.isArray(t)&&(t=qt(t)),f.write(t),f.close(),t}catch(o){if(!o.message||!o.message.match(/onstruct/))throw o}throw new Error("cannot save file "+e)}function fr(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function b0(e,t){for(var r=[],n=fr(e),a=0;a!==n.length;++a)r[e[n[a]][t]]==null&&(r[e[n[a]][t]]=n[a]);return r}function f0(e){for(var t=[],r=fr(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function Pn(e){for(var t=[],r=fr(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}function Js(e){for(var t=[],r=fr(e),n=0;n!==r.length;++n)t[e[r[n]]]==null&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}var Tn=new Date(1899,11,30,0,0,0);function Er(e,t){var r=e.getTime();t&&(r-=1462*24*60*60*1e3);var n=Tn.getTime()+(e.getTimezoneOffset()-Tn.getTimezoneOffset())*6e4;return(r-n)/(24*60*60*1e3)}var Na=new Date,Zs=Tn.getTime()+(Na.getTimezoneOffset()-Tn.getTimezoneOffset())*6e4,U0=Na.getTimezoneOffset();function Ra(e){var t=new Date;return t.setTime(e*24*60*60*1e3+Zs),t.getTimezoneOffset()!==U0&&t.setTime(t.getTime()+(t.getTimezoneOffset()-U0)*6e4),t}var W0=new Date("2017-02-19T19:06:09.000Z"),Ia=isNaN(W0.getFullYear())?new Date("2/19/17"):W0,Qs=Ia.getFullYear()==2017;function vr(e,t){var r=new Date(e);if(Qs)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Ia.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-i.getTimezoneOffset()*60*1e3)),i}function Ln(e,t){if(ye&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return Vr(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return Vr(Ts(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder!="undefined")try{if(t){if(e[0]==255&&e[1]==254)return Vr(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return Vr(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"\u20AC":"\x80","\u201A":"\x82",\u0192:"\x83","\u201E":"\x84","\u2026":"\x85","\u2020":"\x86","\u2021":"\x87","\u02C6":"\x88","\u2030":"\x89",\u0160:"\x8A","\u2039":"\x8B",\u0152:"\x8C",\u017D:"\x8E","\u2018":"\x91","\u2019":"\x92","\u201C":"\x93","\u201D":"\x94","\u2022":"\x95","\u2013":"\x96","\u2014":"\x97","\u02DC":"\x98","\u2122":"\x99",\u0161:"\x9A","\u203A":"\x9B",\u0153:"\x9C",\u017E:"\x9E",\u0178:"\x9F"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(i){return r[i]||i})}catch{}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function Tr(e){if(typeof JSON!="undefined"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Tr(e[r]));return t}function We(e,t){for(var r="";r.length<t;)r+=e;return r}function Gr(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,i){return r=-r,i}),!isNaN(t=Number(n)))?t/r:t}var ef=["january","february","march","april","may","june","july","august","september","october","november","december"];function $t(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var s=e.toLowerCase();if(s.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(s=s.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),s.length>3&&ef.indexOf(s)==-1)return r}else if(s.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}function _e(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return ye?n=Kr(r):n=As(r),ke.utils.cfb_add(e,t,n)}ke.utils.cfb_add(e,t,r)}else e.file(t,r)}function l0(){return ke.utils.cfb_new()}var Ye=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,rf={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},o0=f0(rf),u0=/[&<>'"]/g,tf=/[\u0000-\u0008\u000b-\u001f]/g;function Re(e){var t=e+"";return t.replace(u0,function(r){return o0[r]}).replace(tf,function(r){return"_x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+"_"})}function V0(e){return Re(e).replace(/ /g,"_x0020_")}var ka=/[\u0000-\u001f]/g;function nf(e){var t=e+"";return t.replace(u0,function(r){return o0[r]}).replace(/\n/g,"<br/>").replace(ka,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}function af(e){var t=e+"";return t.replace(u0,function(r){return o0[r]}).replace(ka,function(r){return"&#x"+r.charCodeAt(0).toString(16).toUpperCase()+";"})}function sf(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function ff(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Yn(e){for(var t="",r=0,n=0,a=0,i=0,s=0,f=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){s=(n&31)<<6,s|=a&63,t+=String.fromCharCode(s);continue}if(i=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|i&63);continue}s=e.charCodeAt(r++),f=((n&7)<<18|(a&63)<<12|(i&63)<<6|s&63)-65536,t+=String.fromCharCode(55296+(f>>>10&1023)),t+=String.fromCharCode(56320+(f&1023))}return t}function H0(e){var t=ct(2*e.length),r,n,a=1,i=0,s=0,f;for(n=0;n<e.length;n+=a)a=1,(f=e.charCodeAt(n))<128?r=f:f<224?(r=(f&31)*64+(e.charCodeAt(n+1)&63),a=2):f<240?(r=(f&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(f&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,s=55296+(r>>>10&1023),r=56320+(r&1023)),s!==0&&(t[i++]=s&255,t[i++]=s>>>8,s=0),t[i++]=r%256,t[i++]=r>>>8;return t.slice(0,i).toString("ucs2")}function G0(e){return Kr(e,"binary").toString("utf8")}var hn="foo bar baz\xE2\x98\x83\xF0\x9F\x8D\xA3",Ut=ye&&(G0(hn)==Yn(hn)&&G0||H0(hn)==Yn(hn)&&H0)||Yn,Vr=ye?function(e){return Kr(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},lf=function(){var e=[["nbsp"," "],["middot","\xB7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),Pa=/(^\s|\s$|\n)/;function ir(e,t){return"<"+e+(t.match(Pa)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Kt(e){return fr(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function Z(e,t,r){return"<"+e+(r!=null?Kt(r):"")+(t!=null?(t.match(Pa)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function t0(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}function of(e,t){switch(typeof e){case"string":var r=Z("vt:lpwstr",Re(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return Z((e|0)==e?"vt:i4":"vt:r8",Re(String(e)));case"boolean":return Z("vt:bool",e?"true":"false")}if(e instanceof Date)return Z("vt:filetime",t0(e));throw new Error("Unable to serialize "+e)}var Ze={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},Dt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Sr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function uf(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,i=5;i>=0;--i)a=a*256+e[t+i];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function cf(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,s=n?-t:t;isFinite(s)?s==0?a=i=0:(a=Math.floor(Math.log(s)/Math.LN2),i=s*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var f=0;f<=5;++f,i/=256)e[r+f]=i&255;e[r+6]=(a&15)<<4|i&15,e[r+7]=a>>4|n}var X0=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,i=e[0][n].length;a<i;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},z0=ye?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Kr(t)})):X0(e)}:X0,$0=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(Mt(e,a)));return n.join("").replace(bt,"")},c0=ye?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(bt,""):$0(e,t,r)}:$0,K0=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},La=ye?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):K0(e,t,r)}:K0,Y0=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(wt(e,a)));return n.join("")},Zt=ye?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):Y0(t,r,n)}:Y0,Ba=function(e,t){var r=Fr(e,t);return r>0?Zt(e,t+4,t+4+r-1):""},Ma=Ba,ba=function(e,t){var r=Fr(e,t);return r>0?Zt(e,t+4,t+4+r-1):""},Ua=ba,Wa=function(e,t){var r=2*Fr(e,t);return r>0?Zt(e,t+4,t+4+r-1):""},Va=Wa,Ha=function(t,r){var n=Fr(t,r);return n>0?c0(t,r+4,r+4+n):""},Ga=Ha,Xa=function(e,t){var r=Fr(e,t);return r>0?Zt(e,t+4,t+4+r):""},za=Xa,$a=function(e,t){return uf(e,t)},wn=$a,h0=function(t){return Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array};ye&&(Ma=function(t,r){if(!Buffer.isBuffer(t))return Ba(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Ua=function(t,r){if(!Buffer.isBuffer(t))return ba(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Va=function(t,r){if(!Buffer.isBuffer(t))return Wa(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},Ga=function(t,r){if(!Buffer.isBuffer(t))return Ha(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},za=function(t,r){if(!Buffer.isBuffer(t))return Xa(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},wn=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):$a(t,r)},h0=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array!="undefined"&&t instanceof Uint8Array});var wt=function(e,t){return e[t]},Mt=function(e,t){return e[t+1]*(1<<8)+e[t]},hf=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Fr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},ot=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},xf=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Wt(e,t){var r="",n,a,i=[],s,f,o,l;switch(t){case"dbcs":if(l=this.l,ye&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(Mt(this,l)),l+=2;e*=2;break;case"utf8":r=Zt(this,this.l,this.l+e);break;case"utf16le":e*=2,r=c0(this,this.l,this.l+e);break;case"wstr":return Wt.call(this,e,"dbcs");case"lpstr-ansi":r=Ma(this,this.l),e=4+Fr(this,this.l);break;case"lpstr-cp":r=Ua(this,this.l),e=4+Fr(this,this.l);break;case"lpwstr":r=Va(this,this.l),e=4+2*Fr(this,this.l);break;case"lpp4":e=4+Fr(this,this.l),r=Ga(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Fr(this,this.l),r=za(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(s=wt(this,this.l+e++))!==0;)i.push(on(s));r=i.join("");break;case"_wstr":for(e=0,r="";(s=Mt(this,this.l+e))!==0;)i.push(on(s)),e+=2;e+=2,r=i.join("");break;case"dbcs-cont":for(r="",l=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=wt(this,l),this.l=l+1,f=Wt.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(on(Mt(this,l))),l+=2}r=i.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",l=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(l)!==-1)return s=wt(this,l),this.l=l+1,f=Wt.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),i.join("")+f;i.push(on(wt(this,l))),l+=1}r=i.join("");break;default:switch(e){case 1:return n=wt(this,this.l),this.l++,n;case 2:return n=(t==="i"?hf:Mt)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?ot:xf)(this,this.l),this.l+=4,n):(a=Fr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=wn(this,this.l):a=wn([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=La(this,this.l,e);break}}return this.l+=e,r}var df=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},pf=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},mf=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function vf(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)mf(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var i=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var s=t.charCodeAt(a);this[this.l++]=s&255,this[this.l++]=s>>8}for(;this.l<i;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,df(this,t,this.l);break;case 8:if(n=8,r==="f"){cf(this,t,this.l);break}case 16:break;case-4:n=4,pf(this,t,this.l);break}return this.l+=n,this}function Ka(e,t){var r=La(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Ar(e,t){e.l=t,e.read_shift=Wt,e.chk=Ka,e.write_shift=vf}function Ur(e,t){e.l+=t}function b(e){var t=ct(e);return Ar(t,0),t}function _r(){var e=[],t=ye?256:2048,r=function(l){var u=b(l);return Ar(u,0),u},n=r(t),a=function(){!n||(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},i=function(l){return n&&l<n.length-n.l?n:(a(),n=r(Math.max(l+1,t)))},s=function(){return a(),ar(e)},f=function(l){a(),n=l,n.l==null&&(n.l=n.length),i(t)};return{next:i,push:f,end:s,_bufs:e}}function G(e,t,r,n){var a=+t,i;if(!isNaN(a)){n||(n=ch[a].p||(r||[]).length||0),i=1+(a>=128?1:0)+1,n>=128&&++i,n>=16384&&++i,n>=2097152&&++i;var s=e.next(i);a<=127?s.write_shift(1,a):(s.write_shift(1,(a&127)+128),s.write_shift(1,a>>7));for(var f=0;f!=4;++f)if(n>=128)s.write_shift(1,(n&127)+128),n>>=7;else{s.write_shift(1,n);break}n>0&&h0(r)&&e.push(r)}}function Vt(e,t,r){var n=Tr(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function j0(e,t,r){var n=Tr(e);return n.s=Vt(n.s,t.s,r),n.e=Vt(n.e,t.s,r),n}function Ht(e,t){if(e.cRel&&e.c<0)for(e=Tr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Tr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Ie(e);return!e.cRel&&e.cRel!=null&&(r=Ef(r)),!e.rRel&&e.rRel!=null&&(r=gf(r)),r}function jn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+hr(e.s.c)+":"+(e.e.cRel?"":"$")+hr(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+sr(e.s.r)+":"+(e.e.rRel?"":"$")+sr(e.e.r):Ht(e.s,t.biff)+":"+Ht(e.e,t.biff)}function x0(e){return parseInt(_f(e),10)-1}function sr(e){return""+(e+1)}function gf(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function _f(e){return e.replace(/\$(\d+)$/,"$1")}function d0(e){for(var t=Tf(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function hr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Ef(e){return e.replace(/^([A-Z])/,"$$$1")}function Tf(e){return e.replace(/^\$([A-Z])/,"$1")}function wf(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Qe(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Ie(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function Cr(e){var t=e.indexOf(":");return t==-1?{s:Qe(e),e:Qe(e)}:{s:Qe(e.slice(0,t)),e:Qe(e.slice(t+1))}}function Ke(e,t){return typeof t=="undefined"||typeof t=="number"?Ke(e.s,e.e):(typeof e!="string"&&(e=Ie(e)),typeof t!="string"&&(t=Ie(t)),e==t?e:e+":"+t)}function Le(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function q0(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=et(e.z,r?Er(t):t)}catch{}try{return e.w=et((e.XF||{}).numFmtId||(r?14:0),r?Er(t):t)}catch{return""+t}}function $r(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Qt[e.v]||e.v:t==null?q0(e,e.v):q0(e,t))}function dt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function Ya(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,i=e||(a?[]:{}),s=0,f=0;if(i&&n.origin!=null){if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Qe(n.origin):n.origin;s=o.r,f=o.c}i["!ref"]||(i["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var u=Le(i["!ref"]);l.s.c=u.s.c,l.s.r=u.s.r,l.e.c=Math.max(l.e.c,u.e.c),l.e.r=Math.max(l.e.r,u.e.r),s==-1&&(l.e.r=s=u.e.r+1)}for(var x=0;x!=t.length;++x)if(!!t[x]){if(!Array.isArray(t[x]))throw new Error("aoa_to_sheet expects an array of arrays");for(var d=0;d!=t[x].length;++d)if(typeof t[x][d]!="undefined"){var p={v:t[x][d]},E=s+x,h=f+d;if(l.s.r>E&&(l.s.r=E),l.s.c>h&&(l.s.c=h),l.e.r<E&&(l.e.r=E),l.e.c<h&&(l.e.c=h),t[x][d]&&typeof t[x][d]=="object"&&!Array.isArray(t[x][d])&&!(t[x][d]instanceof Date))p=t[x][d];else if(Array.isArray(p.v)&&(p.f=t[x][d][1],p.v=p.v[0]),p.v===null)if(p.f)p.t="n";else if(n.nullError)p.t="e",p.v=0;else if(n.sheetStubs)p.t="z";else continue;else typeof p.v=="number"?p.t="n":typeof p.v=="boolean"?p.t="b":p.v instanceof Date?(p.z=n.dateNF||Ve[14],n.cellDates?(p.t="d",p.w=et(p.z,Er(p.v))):(p.t="n",p.v=Er(p.v),p.w=et(p.z,p.v))):p.t="s";if(a)i[E]||(i[E]=[]),i[E][h]&&i[E][h].z&&(p.z=i[E][h].z),i[E][h]=p;else{var g=Ie({c:h,r:E});i[g]&&i[g].z&&(p.z=i[g].z),i[g]=p}}}return l.s.c<1e7&&(i["!ref"]=Ke(l)),i}function Nt(e,t){return Ya(null,e,t)}function Af(e){return e.read_shift(4,"i")}function Br(e,t){return t||(t=b(4)),t.write_shift(4,e),t}function xr(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function er(e,t){var r=!1;return t==null&&(r=!0,t=b(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Sf(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Ff(e,t){return t||(t=b(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0),t}function p0(e,t){var r=e.l,n=e.read_shift(1),a=xr(e),i=[],s={t:a,h:a};if((n&1)!==0){for(var f=e.read_shift(4),o=0;o!=f;++o)i.push(Sf(e));s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=r+t,s}function Cf(e,t){var r=!1;return t==null&&(r=!0,t=b(15+4*e.t.length)),t.write_shift(1,0),er(e.t,t),r?t.slice(0,t.l):t}var yf=p0;function Of(e,t){var r=!1;return t==null&&(r=!0,t=b(23+4*e.t.length)),t.write_shift(1,1),er(e.t,t),t.write_shift(4,1),Ff({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function Nr(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function pt(e,t){return t==null&&(t=b(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function mt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function vt(e,t){return t==null&&(t=b(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Df=xr,ja=er;function m0(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}function An(e,t){var r=!1;return t==null&&(r=!0,t=b(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Nf=xr,n0=m0,v0=An;function qa(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?wn([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):ot(t,0)>>2;return r?a/100:a}function Ja(e,t){t==null&&(t=b(4));var r=0,n=0,a=e*100;if(e==(e|0)&&e>=-(1<<29)&&e<1<<29?n=1:a==(a|0)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw new Error("unsupported RkNumber "+e)}function Za(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}function Rf(e,t){return t||(t=b(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t}var gt=Za,Rt=Rf;function It(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function ht(e,t){return(t||b(8)).write_shift(8,e,"f")}function If(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),i=e.read_shift(2,"i"),s=e.read_shift(1),f=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var l=Vf[a];l&&(t.rgb=fa(l));break;case 2:t.rgb=fa([s,f,o]);break;case 3:t.theme=a;break}return i!=0&&(t.tint=i>0?i/32767:i/32768),t}function Sn(e,t){if(t||(t=b(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index!=null?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme!=null?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),!e.rgb||e.theme!=null)t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);else{var n=e.rgb||"FFFFFF";typeof n=="number"&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}return t}function kf(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function Pf(e,t){t||(t=b(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return t.write_shift(1,r),t.write_shift(1,0),t}var Qa=2,wr=3,xn=11,Fn=19,dn=64,Lf=65,Bf=71,Mf=4108,bf=4126,nr=80,J0={1:{n:"CodePage",t:Qa},2:{n:"Category",t:nr},3:{n:"PresentationFormat",t:nr},4:{n:"ByteCount",t:wr},5:{n:"LineCount",t:wr},6:{n:"ParagraphCount",t:wr},7:{n:"SlideCount",t:wr},8:{n:"NoteCount",t:wr},9:{n:"HiddenCount",t:wr},10:{n:"MultimediaClipCount",t:wr},11:{n:"ScaleCrop",t:xn},12:{n:"HeadingPairs",t:Mf},13:{n:"TitlesOfParts",t:bf},14:{n:"Manager",t:nr},15:{n:"Company",t:nr},16:{n:"LinksUpToDate",t:xn},17:{n:"CharacterCount",t:wr},19:{n:"SharedDoc",t:xn},22:{n:"HyperlinksChanged",t:xn},23:{n:"AppVersion",t:wr,p:"version"},24:{n:"DigSig",t:Lf},26:{n:"ContentType",t:nr},27:{n:"ContentStatus",t:nr},28:{n:"Language",t:nr},29:{n:"Version",t:nr},255:{},2147483648:{n:"Locale",t:Fn},2147483651:{n:"Behavior",t:Fn},1919054434:{}},Z0={1:{n:"CodePage",t:Qa},2:{n:"Title",t:nr},3:{n:"Subject",t:nr},4:{n:"Author",t:nr},5:{n:"Keywords",t:nr},6:{n:"Comments",t:nr},7:{n:"Template",t:nr},8:{n:"LastAuthor",t:nr},9:{n:"RevNumber",t:nr},10:{n:"EditTime",t:dn},11:{n:"LastPrinted",t:dn},12:{n:"CreatedDate",t:dn},13:{n:"ModifiedDate",t:dn},14:{n:"PageCount",t:wr},15:{n:"WordCount",t:wr},16:{n:"CharCount",t:wr},17:{n:"Thumbnail",t:Bf},18:{n:"Application",t:nr},19:{n:"DocSecurity",t:wr},255:{},2147483648:{n:"Locale",t:Fn},2147483651:{n:"Behavior",t:Fn},1919054434:{}};function Uf(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var Wf=Uf([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Vf=Tr(Wf),Qt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Hf={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},pn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function ei(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function ri(e,t){var r=Js(Hf),n=[],a;n[n.length]=Ye,n[n.length]=Z("Types",null,{xmlns:Ze.CT,"xmlns:xsd":Ze.xsd,"xmlns:xsi":Ze.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(o){return Z("Default",null,{Extension:o[0],ContentType:o[1]})}));var i=function(o){e[o]&&e[o].length>0&&(a=e[o][0],n[n.length]=Z("Override",null,{PartName:(a[0]=="/"?"":"/")+a,ContentType:pn[o][t.bookType]||pn[o].xlsx}))},s=function(o){(e[o]||[]).forEach(function(l){n[n.length]=Z("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:pn[o][t.bookType]||pn[o].xlsx})})},f=function(o){(e[o]||[]).forEach(function(l){n[n.length]=Z("Override",null,{PartName:(l[0]=="/"?"":"/")+l,ContentType:r[o][0]})})};return i("workbooks"),s("sheets"),s("charts"),f("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(f),f("vba"),f("comments"),f("threadedcomments"),f("drawings"),s("metadata"),f("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var Fe={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ti(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Ft(e){var t=[Ye,Z("Relationships",null,{xmlns:Ze.RELS})];return fr(e["!id"]).forEach(function(r){t[t.length]=Z("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function Ne(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[Fe.HLINK,Fe.XPATH,Fe.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function Gf(e){var t=[Ye];t.push(`<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">
`),t.push(`  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>
`);for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+`"/>
`);return t.push("</manifest:manifest>"),t.join("")}function Q0(e,t,r){return['  <rdf:Description rdf:about="'+e+`">
`,'    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+`"/>
`,`  </rdf:Description>
`].join("")}function Xf(e,t){return['  <rdf:Description rdf:about="'+e+`">
`,'    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+t+`"/>
`,`  </rdf:Description>
`].join("")}function zf(e){var t=[Ye];t.push(`<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
`);for(var r=0;r!=e.length;++r)t.push(Q0(e[r][0],e[r][1])),t.push(Xf("",e[r][0]));return t.push(Q0("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}function ni(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+vn.version+"</meta:generator></office:meta></office:document-meta>"}var ut=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function qn(e,t,r,n,a){a[e]!=null||t==null||t===""||(a[e]=t,t=Re(t),n[n.length]=r?Z(e,t,r):ir(e,t))}function ai(e,t){var r=t||{},n=[Ye,Z("cp:coreProperties",null,{"xmlns:cp":Ze.CORE_PROPS,"xmlns:dc":Ze.dc,"xmlns:dcterms":Ze.dcterms,"xmlns:dcmitype":Ze.dcmitype,"xmlns:xsi":Ze.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(e.CreatedDate!=null&&qn("dcterms:created",typeof e.CreatedDate=="string"?e.CreatedDate:t0(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),e.ModifiedDate!=null&&qn("dcterms:modified",typeof e.ModifiedDate=="string"?e.ModifiedDate:t0(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=ut.length;++i){var s=ut[i],f=r.Props&&r.Props[s[1]]!=null?r.Props[s[1]]:e?e[s[1]]:null;f===!0?f="1":f===!1?f="0":typeof f=="number"&&(f=String(f)),f!=null&&qn(s[0],f,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var Ct=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ii=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function si(e){var t=[],r=Z;return e||(e={}),e.Application="SheetJS",t[t.length]=Ye,t[t.length]=Z("Properties",null,{xmlns:Ze.EXT_PROPS,"xmlns:vt":Ze.vt}),Ct.forEach(function(n){if(e[n[1]]!==void 0){var a;switch(n[2]){case"string":a=Re(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false";break}a!==void 0&&(t[t.length]=r(n[0],a))}}),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map(function(n){return"<vt:lpstr>"+Re(n)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function fi(e){var t=[Ye,Z("Properties",null,{xmlns:Ze.CUST_PROPS,"xmlns:vt":Ze.vt})];if(!e)return t.join("");var r=1;return fr(e).forEach(function(a){++r,t[t.length]=Z("property",of(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Re(a)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var ea={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function $f(e,t){var r=[];return fr(ea).map(function(n){for(var a=0;a<ut.length;++a)if(ut[a][1]==n)return ut[a];for(a=0;a<Ct.length;++a)if(Ct[a][1]==n)return Ct[a];throw n}).forEach(function(n){if(e[n[1]]!=null){var a=t&&t.Props&&t.Props[n[1]]!=null?t.Props[n[1]]:e[n[1]];switch(n[2]){case"date":a=new Date(a).toISOString().replace(/\.\d*Z/,"Z");break}typeof a=="number"?a=String(a):a===!0||a===!1?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(ir(ea[n[1]]||n[1],a))}}),Z("DocumentProperties",r.join(""),{xmlns:Sr.o})}function Kf(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&fr(e).forEach(function(i){if(!!Object.prototype.hasOwnProperty.call(e,i)){for(var s=0;s<ut.length;++s)if(i==ut[s][1])return;for(s=0;s<Ct.length;++s)if(i==Ct[s][1])return;for(s=0;s<r.length;++s)if(i==r[s])return;var f=e[i],o="string";typeof f=="number"?(o="float",f=String(f)):f===!0||f===!1?(o="boolean",f=f?"1":"0"):f=String(f),a.push(Z(V0(i),f,{"dt:dt":o}))}}),t&&fr(t).forEach(function(i){if(!!Object.prototype.hasOwnProperty.call(t,i)&&!(e&&Object.prototype.hasOwnProperty.call(e,i))){var s=t[i],f="string";typeof s=="number"?(f="float",s=String(s)):s===!0||s===!1?(f="boolean",s=s?"1":"0"):s instanceof Date?(f="dateTime.tz",s=s.toISOString()):s=String(s),a.push(Z(V0(i),s,{"dt:dt":f}))}}),"<"+n+' xmlns="'+Sr.o+'">'+a.join("")+"</"+n+">"}function Yf(e){var t=typeof e=="string"?new Date(Date.parse(e)):e,r=t.getTime()/1e3+11644473600,n=r%Math.pow(2,32),a=(r-n)/Math.pow(2,32);n*=1e7,a*=1e7;var i=n/Math.pow(2,32)|0;i>0&&(n=n%Math.pow(2,32),a+=i);var s=b(8);return s.write_shift(4,n),s.write_shift(4,a),s}function ra(e,t){var r=b(4),n=b(4);switch(r.write_shift(4,e==80?31:e),e){case 3:n.write_shift(-4,t);break;case 5:n=b(8),n.write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=Yf(t);break;case 31:case 80:for(n=b(4+2*(t.length+1)+(t.length%2?0:2)),n.write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return ar([r,n])}var li=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function jf(e){switch(typeof e){case"boolean":return 11;case"number":return(e|0)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function ta(e,t,r){var n=b(8),a=[],i=[],s=8,f=0,o=b(8),l=b(8);if(o.write_shift(4,2),o.write_shift(4,1200),l.write_shift(4,1),i.push(o),a.push(l),s+=8+o.length,!t){l=b(8),l.write_shift(4,0),a.unshift(l);var u=[b(4)];for(u[0].write_shift(4,e.length),f=0;f<e.length;++f){var x=e[f][0];for(o=b(4+4+2*(x.length+1)+(x.length%2?0:2)),o.write_shift(4,f+2),o.write_shift(4,x.length+1),o.write_shift(0,x,"dbcs");o.l!=o.length;)o.write_shift(1,0);u.push(o)}o=ar(u),i.unshift(o),s+=8+o.length}for(f=0;f<e.length;++f)if(!(t&&!t[e[f][0]])&&!(li.indexOf(e[f][0])>-1||ii.indexOf(e[f][0])>-1)&&e[f][1]!=null){var d=e[f][1],p=0;if(t){p=+t[e[f][0]];var E=r[p];if(E.p=="version"&&typeof d=="string"){var h=d.split(".");d=(+h[0]<<16)+(+h[1]||0)}o=ra(E.t,d)}else{var g=jf(d);g==-1&&(g=31,d=String(d)),o=ra(g,d)}i.push(o),l=b(8),l.write_shift(4,t?p:2+f),a.push(l),s+=8+o.length}var y=8*(i.length+1);for(f=0;f<i.length;++f)a[f].write_shift(4,y),y+=i[f].length;return n.write_shift(4,s),n.write_shift(4,i.length),ar([n].concat(a).concat(i))}function na(e,t,r,n,a,i){var s=b(a?68:48),f=[s];s.write_shift(2,65534),s.write_shift(2,0),s.write_shift(4,842412599),s.write_shift(16,ke.utils.consts.HEADER_CLSID,"hex"),s.write_shift(4,a?2:1),s.write_shift(16,t,"hex"),s.write_shift(4,a?68:48);var o=ta(e,r,n);if(f.push(o),a){var l=ta(a,null,null);s.write_shift(16,i,"hex"),s.write_shift(4,68+o.length),f.push(l)}return ar(f)}function qf(e,t){t||(t=b(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}function Jf(e,t){return e.read_shift(t)===1}function mr(e,t){return t||(t=b(2)),t.write_shift(2,+!!e),t}function oi(e){return e.read_shift(2,"u")}function Dr(e,t){return t||(t=b(2)),t.write_shift(2,e),t}function ui(e,t,r){return r||(r=b(2)),r.write_shift(1,t=="e"?+e:+!!e),r.write_shift(1,t=="e"?1:0),r}function ci(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var i=e.read_shift(1);i&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var s=n?e.read_shift(n,a):"";return s}function Zf(e){var t=e.t||"",r=b(3+0);r.write_shift(2,t.length),r.write_shift(1,1);var n=b(2*t.length);n.write_shift(2*t.length,t,"utf16le");var a=[r,n];return ar(a)}function Qf(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function el(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):Qf(e,n,r)}function rl(e,t,r){if(r.biff>5)return el(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function hi(e,t,r){return r||(r=b(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function aa(e,t){t||(t=b(6+e.length*2)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function tl(e){var t=b(512),r=0,n=e.Target;n.slice(0,7)=="file://"&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3;break}t.write_shift(4,2),t.write_shift(4,i);var s=[8,6815827,6619237,4849780,83];for(r=0;r<s.length;++r)t.write_shift(4,s[r]);if(i==28)n=n.slice(1),aa(n,t);else if(i&2){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));var f=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(f.length+1)),r=0;r<f.length;++r)t.write_shift(2,f.charCodeAt(r));t.write_shift(2,0),i&8&&aa(a>-1?n.slice(a+1):"",t)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var o=0;n.slice(o*3,o*3+3)=="../"||n.slice(o*3,o*3+3)=="..\\";)++o;for(t.write_shift(2,o),t.write_shift(4,n.length-3*o+1),r=0;r<n.length-3*o;++r)t.write_shift(1,n.charCodeAt(r+3*o)&255);for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function xt(e,t,r,n){return n||(n=b(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function nl(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),i=e.read_shift(n,"i"),s=e.read_shift(n,"i");return[a,i,s]}function al(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function xi(e,t){return t||(t=b(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function g0(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var i=b(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function il(e,t){var r=!t||t.biff==8,n=b(r?112:54);for(n.write_shift(t.biff==8?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}function sl(e,t){var r=!t||t.biff>=8?2:1,n=b(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function fl(e,t){var r=b(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=Zf(e[a]);var i=ar([r].concat(n));return i.parts=[r.length].concat(n.map(function(s){return s.length})),i}function ll(){var e=b(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function ol(e){var t=b(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}function ul(e,t){var r=e.name||"Arial",n=t&&t.biff==5,a=n?15+r.length:16+2*r.length,i=b(a);return i.write_shift(2,(e.sz||12)*20),i.write_shift(4,0),i.write_shift(2,400),i.write_shift(4,0),i.write_shift(2,0),i.write_shift(1,r.length),n||i.write_shift(1,1),i.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),i}function cl(e,t,r,n){var a=b(10);return xt(e,t,n,a),a.write_shift(4,r),a}function hl(e,t,r,n,a){var i=!a||a.biff==8,s=b(6+2+ +i+(1+i)*r.length);return xt(e,t,n,s),s.write_shift(2,r.length),i&&s.write_shift(1,1),s.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),s}function xl(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return i.l==null&&(i.l=i.length),i}function dl(e,t){var r=t.biff==8||!t.biff?4:2,n=b(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}function ia(e,t,r,n){var a=r&&r.biff==5;n||(n=b(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function pl(e){var t=b(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}function ml(e,t,r,n,a,i){var s=b(8);return xt(e,t,n,s),ui(r,i,s),s}function vl(e,t,r,n){var a=b(14);return xt(e,t,n,a),ht(r,a),a}function gl(e,t,r){if(r.biff<8)return _l(e,t,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);i--!==0;)n.push(nl(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function _l(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=ci(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function El(e){var t=b(2+e.length*8);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)xi(e[r],t);return t}function Tl(e){var t=b(24),r=Qe(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return ar([t,tl(e[1])])}function wl(e){var t=e[1].Tooltip,r=b(10+2*(t.length+1));r.write_shift(2,2048);var n=Qe(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function Al(e){return e||(e=b(4)),e.write_shift(2,1),e.write_shift(2,1),e}function Sl(e,t,r){if(!r.cellStyles)return Ur(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),s=e.read_shift(n),f=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var l={s:a,e:i,w:s,ixfe:f,flags:o};return(r.biff>=5||!r.biff)&&(l.level=o>>8&7),l}function Fl(e,t){var r=b(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,e.width*256),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}function Cl(e){for(var t=b(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}function yl(e,t,r){var n=b(15);return rn(n,e,t),n.write_shift(8,r,"f"),n}function Ol(e,t,r){var n=b(9);return rn(n,e,t),n.write_shift(2,r),n}var Dl=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=f0({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(f,o){var l=[],u=ct(1);switch(o.type){case"base64":u=Pr(zr(f));break;case"binary":u=Pr(f);break;case"buffer":case"array":u=f;break}Ar(u,0);var x=u.read_shift(1),d=!!(x&136),p=!1,E=!1;switch(x){case 2:break;case 3:break;case 48:p=!0,d=!0;break;case 49:p=!0,d=!0;break;case 131:break;case 139:break;case 140:E=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+x.toString(16))}var h=0,g=521;x==2&&(h=u.read_shift(2)),u.l+=3,x!=2&&(h=u.read_shift(4)),h>1048576&&(h=1e6),x!=2&&(g=u.read_shift(2));var y=u.read_shift(2),F=o.codepage||1252;x!=2&&(u.l+=16,u.read_shift(1),u[u.l]!==0&&(F=e[u[u.l]]),u.l+=1,u.l+=2),E&&(u.l+=36);for(var D=[],M={},q=Math.min(u.length,x==2?521:g-10-(p?264:0)),ie=E?32:11;u.l<q&&u[u.l]!=13;)switch(M={},M.name=lt.utils.decode(F,u.slice(u.l,u.l+ie)).replace(/[\u0000\r\n].*$/g,""),u.l+=ie,M.type=String.fromCharCode(u.read_shift(1)),x!=2&&!E&&(M.offset=u.read_shift(4)),M.len=u.read_shift(1),x==2&&(M.offset=u.read_shift(2)),M.dec=u.read_shift(1),M.name.length&&D.push(M),x!=2&&(u.l+=E?13:14),M.type){case"B":(!p||M.len!=8)&&o.WTF&&console.log("Skipping "+M.name+":"+M.type);break;case"G":case"P":o.WTF&&console.log("Skipping "+M.name+":"+M.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+M.type)}if(u[u.l]!==13&&(u.l=g-1),u.read_shift(1)!==13)throw new Error("DBF Terminator not found "+u.l+" "+u[u.l]);u.l=g;var N=0,V=0;for(l[0]=[],V=0;V!=D.length;++V)l[0][V]=D[V].name;for(;h-- >0;){if(u[u.l]===42){u.l+=y;continue}for(++u.l,l[++N]=[],V=0,V=0;V!=D.length;++V){var L=u.slice(u.l,u.l+D[V].len);u.l+=D[V].len,Ar(L,0);var X=lt.utils.decode(F,L);switch(D[V].type){case"C":X.trim().length&&(l[N][V]=X.replace(/\s+$/,""));break;case"D":X.length===8?l[N][V]=new Date(+X.slice(0,4),+X.slice(4,6)-1,+X.slice(6,8)):l[N][V]=X;break;case"F":l[N][V]=parseFloat(X.trim());break;case"+":case"I":l[N][V]=E?L.read_shift(-4,"i")^2147483648:L.read_shift(4,"i");break;case"L":switch(X.trim().toUpperCase()){case"Y":case"T":l[N][V]=!0;break;case"N":case"F":l[N][V]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+X+"|")}break;case"M":if(!d)throw new Error("DBF Unexpected MEMO for type "+x.toString(16));l[N][V]="##MEMO##"+(E?parseInt(X.trim(),10):L.read_shift(4));break;case"N":X=X.replace(/\u0000/g,"").trim(),X&&X!="."&&(l[N][V]=+X||0);break;case"@":l[N][V]=new Date(L.read_shift(-8,"f")-621356832e5);break;case"T":l[N][V]=new Date((L.read_shift(4)-2440588)*864e5+L.read_shift(4));break;case"Y":l[N][V]=L.read_shift(4,"i")/1e4+L.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":l[N][V]=-L.read_shift(-8,"f");break;case"B":if(p&&D[V].len==8){l[N][V]=L.read_shift(8,"f");break}case"G":case"P":L.l+=D[V].len;break;case"0":if(D[V].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+D[V].type)}}}if(x!=2&&u.l<u.length&&u[u.l++]!=26)throw new Error("DBF EOF Marker missing "+(u.l-1)+" of "+u.length+" "+u[u.l-1].toString(16));return o&&o.sheetRows&&(l=l.slice(0,o.sheetRows)),o.DBF=D,l}function n(f,o){var l=o||{};l.dateNF||(l.dateNF="yyyymmdd");var u=Nt(r(f,l),l);return u["!cols"]=l.DBF.map(function(x){return{wch:x.len,DBF:x}}),delete l.DBF,u}function a(f,o){try{return dt(n(f,o),o)}catch(l){if(o&&o.WTF)throw l}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function s(f,o){var l=o||{};if(+l.codepage>=0&&Xt(+l.codepage),l.type=="string")throw new Error("Cannot write DBF to JS string");var u=_r(),x=Nn(f,{header:1,raw:!0,cellDates:!0}),d=x[0],p=x.slice(1),E=f["!cols"]||[],h=0,g=0,y=0,F=1;for(h=0;h<d.length;++h){if(((E[h]||{}).DBF||{}).name){d[h]=E[h].DBF.name,++y;continue}if(d[h]!=null){if(++y,typeof d[h]=="number"&&(d[h]=d[h].toString(10)),typeof d[h]!="string")throw new Error("DBF Invalid column name "+d[h]+" |"+typeof d[h]+"|");if(d.indexOf(d[h])!==h){for(g=0;g<1024;++g)if(d.indexOf(d[h]+"_"+g)==-1){d[h]+="_"+g;break}}}}var D=Le(f["!ref"]),M=[],q=[],ie=[];for(h=0;h<=D.e.c-D.s.c;++h){var N="",V="",L=0,X=[];for(g=0;g<p.length;++g)p[g][h]!=null&&X.push(p[g][h]);if(X.length==0||d[h]==null){M[h]="?";continue}for(g=0;g<X.length;++g){switch(typeof X[g]){case"number":V="B";break;case"string":V="C";break;case"boolean":V="L";break;case"object":V=X[g]instanceof Date?"D":"C";break;default:V="C"}L=Math.max(L,String(X[g]).length),N=N&&N!=V?"C":V}L>250&&(L=250),V=((E[h]||{}).DBF||{}).type,V=="C"&&E[h].DBF.len>L&&(L=E[h].DBF.len),N=="B"&&V=="N"&&(N="N",ie[h]=E[h].DBF.dec,L=E[h].DBF.len),q[h]=N=="C"||V=="N"?L:i[N]||0,F+=q[h],M[h]=N}var K=u.next(32);for(K.write_shift(4,318902576),K.write_shift(4,p.length),K.write_shift(2,296+32*y),K.write_shift(2,F),h=0;h<4;++h)K.write_shift(4,0);for(K.write_shift(4,0|(+t[pa]||3)<<8),h=0,g=0;h<d.length;++h)if(d[h]!=null){var Y=u.next(32),te=(d[h].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);Y.write_shift(1,te,"sbcs"),Y.write_shift(1,M[h]=="?"?"C":M[h],"sbcs"),Y.write_shift(4,g),Y.write_shift(1,q[h]||i[M[h]]||0),Y.write_shift(1,ie[h]||0),Y.write_shift(1,2),Y.write_shift(4,0),Y.write_shift(1,0),Y.write_shift(4,0),Y.write_shift(4,0),g+=q[h]||i[M[h]]||0}var we=u.next(264);for(we.write_shift(4,13),h=0;h<65;++h)we.write_shift(4,0);for(h=0;h<p.length;++h){var me=u.next(F);for(me.write_shift(1,0),g=0;g<d.length;++g)if(d[g]!=null)switch(M[g]){case"L":me.write_shift(1,p[h][g]==null?63:p[h][g]?84:70);break;case"B":me.write_shift(8,p[h][g]||0,"f");break;case"N":var He="0";for(typeof p[h][g]=="number"&&(He=p[h][g].toFixed(ie[g]||0)),y=0;y<q[g]-He.length;++y)me.write_shift(1,32);me.write_shift(1,He,"sbcs");break;case"D":p[h][g]?(me.write_shift(4,("0000"+p[h][g].getFullYear()).slice(-4),"sbcs"),me.write_shift(2,("00"+(p[h][g].getMonth()+1)).slice(-2),"sbcs"),me.write_shift(2,("00"+p[h][g].getDate()).slice(-2),"sbcs")):me.write_shift(8,"00000000","sbcs");break;case"C":var Be=String(p[h][g]!=null?p[h][g]:"").slice(0,q[g]);for(me.write_shift(1,Be,"sbcs"),y=0;y<q[g]-Be.length;++y)me.write_shift(1,32);break}}return u.next(1).write_shift(1,26),u.end()}return{to_workbook:a,to_sheet:n,from_sheet:s}}(),Nl=function(){var e={AA:"\xC0",BA:"\xC1",CA:"\xC2",DA:195,HA:"\xC4",JA:197,AE:"\xC8",BE:"\xC9",CE:"\xCA",HE:"\xCB",AI:"\xCC",BI:"\xCD",CI:"\xCE",HI:"\xCF",AO:"\xD2",BO:"\xD3",CO:"\xD4",DO:213,HO:"\xD6",AU:"\xD9",BU:"\xDA",CU:"\xDB",HU:"\xDC",Aa:"\xE0",Ba:"\xE1",Ca:"\xE2",Da:227,Ha:"\xE4",Ja:229,Ae:"\xE8",Be:"\xE9",Ce:"\xEA",He:"\xEB",Ai:"\xEC",Bi:"\xED",Ci:"\xEE",Hi:"\xEF",Ao:"\xF2",Bo:"\xF3",Co:"\xF4",Do:245,Ho:"\xF6",Au:"\xF9",Bu:"\xFA",Cu:"\xFB",Hu:"\xFC",KC:"\xC7",Kc:"\xE7",q:"\xE6",z:"\u0153",a:"\xC6",j:"\u0152",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+fr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(d,p){var E=e[p];return typeof E=="number"?D0(E):E},n=function(d,p,E){var h=p.charCodeAt(0)-32<<4|E.charCodeAt(0)-48;return h==59?d:D0(h)};e["|"]=254;function a(d,p){switch(p.type){case"base64":return i(zr(d),p);case"binary":return i(d,p);case"buffer":return i(ye&&Buffer.isBuffer(d)?d.toString("binary"):qt(d),p);case"array":return i(Ln(d),p)}throw new Error("Unrecognized type "+p.type)}function i(d,p){var E=d.split(/[\n\r]+/),h=-1,g=-1,y=0,F=0,D=[],M=[],q=null,ie={},N=[],V=[],L=[],X=0,K;for(+p.codepage>=0&&Xt(+p.codepage);y!==E.length;++y){X=0;var Y=E[y].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),te=Y.replace(/;;/g,"\0").split(";").map(function(C){return C.replace(/\u0000/g,";")}),we=te[0],me;if(Y.length>0)switch(we){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":te[1].charAt(0)=="P"&&M.push(Y.slice(3).replace(/;;/g,";"));break;case"C":var He=!1,Be=!1,gr=!1,Ge=!1,rr=-1,lr=-1;for(F=1;F<te.length;++F)switch(te[F].charAt(0)){case"A":break;case"X":g=parseInt(te[F].slice(1))-1,Be=!0;break;case"Y":for(h=parseInt(te[F].slice(1))-1,Be||(g=0),K=D.length;K<=h;++K)D[K]=[];break;case"K":me=te[F].slice(1),me.charAt(0)==='"'?me=me.slice(1,me.length-1):me==="TRUE"?me=!0:me==="FALSE"?me=!1:isNaN(Gr(me))?isNaN($t(me).getDate())||(me=vr(me)):(me=Gr(me),q!==null&&ya(q)&&(me=Ra(me))),He=!0;break;case"E":Ge=!0;var S=Do(te[F].slice(1),{r:h,c:g});D[h][g]=[D[h][g],S];break;case"S":gr=!0,D[h][g]=[D[h][g],"S5S"];break;case"G":break;case"R":rr=parseInt(te[F].slice(1))-1;break;case"C":lr=parseInt(te[F].slice(1))-1;break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+Y)}if(He&&(D[h][g]&&D[h][g].length==2?D[h][g][0]=me:D[h][g]=me,q=null),gr){if(Ge)throw new Error("SYLK shared formula cannot have own formula");var B=rr>-1&&D[rr][lr];if(!B||!B[1])throw new Error("SYLK shared formula cannot find base");D[h][g][1]=No(B[1],{r:h-rr,c:g-lr})}break;case"F":var O=0;for(F=1;F<te.length;++F)switch(te[F].charAt(0)){case"X":g=parseInt(te[F].slice(1))-1,++O;break;case"Y":for(h=parseInt(te[F].slice(1))-1,K=D.length;K<=h;++K)D[K]=[];break;case"M":X=parseInt(te[F].slice(1))/20;break;case"F":break;case"G":break;case"P":q=M[parseInt(te[F].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(L=te[F].slice(1).split(" "),K=parseInt(L[0],10);K<=parseInt(L[1],10);++K)X=parseInt(L[2],10),V[K-1]=X===0?{hidden:!0}:{wch:X},_0(V[K-1]);break;case"C":g=parseInt(te[F].slice(1))-1,V[g]||(V[g]={});break;case"R":h=parseInt(te[F].slice(1))-1,N[h]||(N[h]={}),X>0?(N[h].hpt=X,N[h].hpx=gi(X)):X===0&&(N[h].hidden=!0);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+Y)}O<1&&(q=null);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+Y)}}return N.length>0&&(ie["!rows"]=N),V.length>0&&(ie["!cols"]=V),p&&p.sheetRows&&(D=D.slice(0,p.sheetRows)),[D,ie]}function s(d,p){var E=a(d,p),h=E[0],g=E[1],y=Nt(h,p);return fr(g).forEach(function(F){y[F]=g[F]}),y}function f(d,p){return dt(s(d,p),p)}function o(d,p,E,h){var g="C;Y"+(E+1)+";X"+(h+1)+";K";switch(d.t){case"n":g+=d.v||0,d.f&&!d.F&&(g+=";E"+T0(d.f,{r:E,c:h}));break;case"b":g+=d.v?"TRUE":"FALSE";break;case"e":g+=d.w||d.v;break;case"d":g+='"'+(d.w||d.v)+'"';break;case"s":g+='"'+d.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return g}function l(d,p){p.forEach(function(E,h){var g="F;W"+(h+1)+" "+(h+1)+" ";E.hidden?g+="0":(typeof E.width=="number"&&!E.wpx&&(E.wpx=Cn(E.width)),typeof E.wpx=="number"&&!E.wch&&(E.wch=yn(E.wpx)),typeof E.wch=="number"&&(g+=Math.round(E.wch))),g.charAt(g.length-1)!=" "&&d.push(g)})}function u(d,p){p.forEach(function(E,h){var g="F;";E.hidden?g+="M0;":E.hpt?g+="M"+20*E.hpt+";":E.hpx&&(g+="M"+20*On(E.hpx)+";"),g.length>2&&d.push(g+"R"+(h+1))})}function x(d,p){var E=["ID;PWXL;N;E"],h=[],g=Le(d["!ref"]),y,F=Array.isArray(d),D=`\r
`;E.push("P;PGeneral"),E.push("F;P0;DG0G8;M255"),d["!cols"]&&l(E,d["!cols"]),d["!rows"]&&u(E,d["!rows"]),E.push("B;Y"+(g.e.r-g.s.r+1)+";X"+(g.e.c-g.s.c+1)+";D"+[g.s.c,g.s.r,g.e.c,g.e.r].join(" "));for(var M=g.s.r;M<=g.e.r;++M)for(var q=g.s.c;q<=g.e.c;++q){var ie=Ie({r:M,c:q});y=F?(d[M]||[])[q]:d[ie],!(!y||y.v==null&&(!y.f||y.F))&&h.push(o(y,d,M,q))}return E.join(D)+D+h.join(D)+D+"E"+D}return{to_workbook:f,to_sheet:s,from_sheet:x}}(),Rl=function(){function e(i,s){switch(s.type){case"base64":return t(zr(i),s);case"binary":return t(i,s);case"buffer":return t(ye&&Buffer.isBuffer(i)?i.toString("binary"):qt(i),s);case"array":return t(Ln(i),s)}throw new Error("Unrecognized type "+s.type)}function t(i,s){for(var f=i.split(`
`),o=-1,l=-1,u=0,x=[];u!==f.length;++u){if(f[u].trim()==="BOT"){x[++o]=[],l=0;continue}if(!(o<0)){var d=f[u].trim().split(","),p=d[0],E=d[1];++u;for(var h=f[u]||"";(h.match(/["]/g)||[]).length&1&&u<f.length-1;)h+=`
`+f[++u];switch(h=h.trim(),+p){case-1:if(h==="BOT"){x[++o]=[],l=0;continue}else if(h!=="EOD")throw new Error("Unrecognized DIF special command "+h);break;case 0:h==="TRUE"?x[o][l]=!0:h==="FALSE"?x[o][l]=!1:isNaN(Gr(E))?isNaN($t(E).getDate())?x[o][l]=E:x[o][l]=vr(E):x[o][l]=Gr(E),++l;break;case 1:h=h.slice(1,h.length-1),h=h.replace(/""/g,'"'),h&&h.match(/^=".*"$/)&&(h=h.slice(2,-1)),x[o][l++]=h!==""?h:null;break}if(h==="EOD")break}}return s&&s.sheetRows&&(x=x.slice(0,s.sheetRows)),x}function r(i,s){return Nt(e(i,s),s)}function n(i,s){return dt(r(i,s),s)}var a=function(){var i=function(o,l,u,x,d){o.push(l),o.push(u+","+x),o.push('"'+d.replace(/"/g,'""')+'"')},s=function(o,l,u,x){o.push(l+","+u),o.push(l==1?'"'+x.replace(/"/g,'""')+'"':x)};return function(o){var l=[],u=Le(o["!ref"]),x,d=Array.isArray(o);i(l,"TABLE",0,1,"sheetjs"),i(l,"VECTORS",0,u.e.r-u.s.r+1,""),i(l,"TUPLES",0,u.e.c-u.s.c+1,""),i(l,"DATA",0,0,"");for(var p=u.s.r;p<=u.e.r;++p){s(l,-1,0,"BOT");for(var E=u.s.c;E<=u.e.c;++E){var h=Ie({r:p,c:E});if(x=d?(o[p]||[])[E]:o[h],!x){s(l,1,0,"");continue}switch(x.t){case"n":var g=x.w;!g&&x.v!=null&&(g=x.v),g==null?x.f&&!x.F?s(l,1,0,"="+x.f):s(l,1,0,""):s(l,0,g,"V");break;case"b":s(l,0,x.v?1:0,x.v?"TRUE":"FALSE");break;case"s":s(l,1,0,isNaN(x.v)?x.v:'="'+x.v+'"');break;case"d":x.w||(x.w=et(x.z||Ve[14],Er(vr(x.v)))),s(l,0,x.w,"V");break;default:s(l,1,0,"")}}}s(l,-1,0,"EOD");var y=`\r
`,F=l.join(y);return F}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),di=function(){function e(x){return x.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(x){return x.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(x,d){for(var p=x.split(`
`),E=-1,h=-1,g=0,y=[];g!==p.length;++g){var F=p[g].trim().split(":");if(F[0]==="cell"){var D=Qe(F[1]);if(y.length<=D.r)for(E=y.length;E<=D.r;++E)y[E]||(y[E]=[]);switch(E=D.r,h=D.c,F[2]){case"t":y[E][h]=e(F[3]);break;case"v":y[E][h]=+F[3];break;case"vtf":var M=F[F.length-1];case"vtc":switch(F[3]){case"nl":y[E][h]=!!+F[4];break;default:y[E][h]=+F[4];break}F[2]=="vtf"&&(y[E][h]=[y[E][h],M])}}}return d&&d.sheetRows&&(y=y.slice(0,d.sheetRows)),y}function n(x,d){return Nt(r(x,d),d)}function a(x,d){return dt(n(x,d),d)}var i=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),s=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,f=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function l(x){if(!x||!x["!ref"])return"";for(var d=[],p=[],E,h="",g=Cr(x["!ref"]),y=Array.isArray(x),F=g.s.r;F<=g.e.r;++F)for(var D=g.s.c;D<=g.e.c;++D)if(h=Ie({r:F,c:D}),E=y?(x[F]||[])[D]:x[h],!(!E||E.v==null||E.t==="z")){switch(p=["cell",h,"t"],E.t){case"s":case"str":p.push(t(E.v));break;case"n":E.f?(p[2]="vtf",p[3]="n",p[4]=E.v,p[5]=t(E.f)):(p[2]="v",p[3]=E.v);break;case"b":p[2]="vt"+(E.f?"f":"c"),p[3]="nl",p[4]=E.v?"1":"0",p[5]=t(E.f||(E.v?"TRUE":"FALSE"));break;case"d":var M=Er(vr(E.v));p[2]="vtc",p[3]="nd",p[4]=""+M,p[5]=E.w||et(E.z||Ve[14],M);break;case"e":continue}d.push(p.join(":"))}return d.push("sheet:c:"+(g.e.c-g.s.c+1)+":r:"+(g.e.r-g.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join(`
`)}function u(x){return[i,s,f,s,l(x),o].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:u}}(),Il=function(){function e(u,x,d,p,E){E.raw?x[d][p]=u:u===""||(u==="TRUE"?x[d][p]=!0:u==="FALSE"?x[d][p]=!1:isNaN(Gr(u))?isNaN($t(u).getDate())?x[d][p]=u:x[d][p]=vr(u):x[d][p]=Gr(u))}function t(u,x){var d=x||{},p=[];if(!u||u.length===0)return p;for(var E=u.split(/[\r\n]/),h=E.length-1;h>=0&&E[h].length===0;)--h;for(var g=10,y=0,F=0;F<=h;++F)y=E[F].indexOf(" "),y==-1?y=E[F].length:y++,g=Math.max(g,y);for(F=0;F<=h;++F){p[F]=[];var D=0;for(e(E[F].slice(0,g).trim(),p,F,D,d),D=1;D<=(E[F].length-g)/10+1;++D)e(E[F].slice(g+(D-1)*10,g+D*10).trim(),p,F,D,d)}return d.sheetRows&&(p=p.slice(0,d.sheetRows)),p}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(u){for(var x={},d=!1,p=0,E=0;p<u.length;++p)(E=u.charCodeAt(p))==34?d=!d:!d&&E in r&&(x[E]=(x[E]||0)+1);E=[];for(p in x)Object.prototype.hasOwnProperty.call(x,p)&&E.push([x[p],p]);if(!E.length){x=n;for(p in x)Object.prototype.hasOwnProperty.call(x,p)&&E.push([x[p],p])}return E.sort(function(h,g){return h[0]-g[0]||n[h[1]]-n[g[1]]}),r[E.pop()[1]]||44}function i(u,x){var d=x||{},p="",E=d.dense?[]:{},h={s:{c:0,r:0},e:{c:0,r:0}};u.slice(0,4)=="sep="?u.charCodeAt(5)==13&&u.charCodeAt(6)==10?(p=u.charAt(4),u=u.slice(7)):u.charCodeAt(5)==13||u.charCodeAt(5)==10?(p=u.charAt(4),u=u.slice(6)):p=a(u.slice(0,1024)):d&&d.FS?p=d.FS:p=a(u.slice(0,1024));var g=0,y=0,F=0,D=0,M=0,q=p.charCodeAt(0),ie=!1,N=0,V=u.charCodeAt(0);u=u.replace(/\r\n/mg,`
`);var L=d.dateNF!=null?Ks(d.dateNF):null;function X(){var K=u.slice(D,M),Y={};if(K.charAt(0)=='"'&&K.charAt(K.length-1)=='"'&&(K=K.slice(1,-1).replace(/""/g,'"')),K.length===0)Y.t="z";else if(d.raw)Y.t="s",Y.v=K;else if(K.trim().length===0)Y.t="s",Y.v=K;else if(K.charCodeAt(0)==61)K.charCodeAt(1)==34&&K.charCodeAt(K.length-1)==34?(Y.t="s",Y.v=K.slice(2,-1).replace(/""/g,'"')):Ro(K)?(Y.t="n",Y.f=K.slice(1)):(Y.t="s",Y.v=K);else if(K=="TRUE")Y.t="b",Y.v=!0;else if(K=="FALSE")Y.t="b",Y.v=!1;else if(!isNaN(F=Gr(K)))Y.t="n",d.cellText!==!1&&(Y.w=K),Y.v=F;else if(!isNaN($t(K).getDate())||L&&K.match(L)){Y.z=d.dateNF||Ve[14];var te=0;L&&K.match(L)&&(K=Ys(K,d.dateNF,K.match(L)||[]),te=1),d.cellDates?(Y.t="d",Y.v=vr(K,te)):(Y.t="n",Y.v=Er(vr(K,te))),d.cellText!==!1&&(Y.w=et(Y.z,Y.v instanceof Date?Er(Y.v):Y.v)),d.cellNF||delete Y.z}else Y.t="s",Y.v=K;if(Y.t=="z"||(d.dense?(E[g]||(E[g]=[]),E[g][y]=Y):E[Ie({c:y,r:g})]=Y),D=M+1,V=u.charCodeAt(D),h.e.c<y&&(h.e.c=y),h.e.r<g&&(h.e.r=g),N==q)++y;else if(y=0,++g,d.sheetRows&&d.sheetRows<=g)return!0}e:for(;M<u.length;++M)switch(N=u.charCodeAt(M)){case 34:V===34&&(ie=!ie);break;case q:case 10:case 13:if(!ie&&X())break e;break}return M-D>0&&X(),E["!ref"]=Ke(h),E}function s(u,x){return!(x&&x.PRN)||x.FS||u.slice(0,4)=="sep="||u.indexOf("	")>=0||u.indexOf(",")>=0||u.indexOf(";")>=0?i(u,x):Nt(t(u,x),x)}function f(u,x){var d="",p=x.type=="string"?[0,0,0,0]:zh(u,x);switch(x.type){case"base64":d=zr(u);break;case"binary":d=u;break;case"buffer":x.codepage==65001?d=u.toString("utf8"):x.codepage&&typeof lt!="undefined"?d=lt.utils.decode(x.codepage,u):d=ye&&Buffer.isBuffer(u)?u.toString("binary"):qt(u);break;case"array":d=Ln(u);break;case"string":d=u;break;default:throw new Error("Unrecognized type "+x.type)}return p[0]==239&&p[1]==187&&p[2]==191?d=Ut(d.slice(3)):x.type!="string"&&x.type!="buffer"&&x.codepage==65001?d=Ut(d):x.type=="binary"&&typeof lt!="undefined"&&x.codepage&&(d=lt.utils.decode(x.codepage,lt.utils.encode(28591,d))),d.slice(0,19)=="socialcalc:version:"?di.to_sheet(x.type=="string"?d:Ut(d),x):s(d,x)}function o(u,x){return dt(f(u,x),x)}function l(u){for(var x=[],d=Le(u["!ref"]),p,E=Array.isArray(u),h=d.s.r;h<=d.e.r;++h){for(var g=[],y=d.s.c;y<=d.e.c;++y){var F=Ie({r:h,c:y});if(p=E?(u[h]||[])[y]:u[F],!p||p.v==null){g.push("          ");continue}for(var D=(p.w||($r(p),p.w)||"").slice(0,10);D.length<10;)D+=" ";g.push(D+(y===0?" ":""))}x.push(g.join(""))}return x.join(`
`)}return{to_workbook:o,to_sheet:f,from_sheet:l}}(),sa=function(){function e(S,B,O){if(!!S){Ar(S,S.l||0);for(var C=O.Enum||rr;S.l<S.length;){var H=S.read_shift(2),ce=C[H]||C[65535],he=S.read_shift(2),oe=S.l+he,re=ce.f&&ce.f(S,he,O);if(S.l=oe,B(re,ce,H))return}}}function t(S,B){switch(B.type){case"base64":return r(Pr(zr(S)),B);case"binary":return r(Pr(S),B);case"buffer":case"array":return r(S,B)}throw"Unsupported type "+B.type}function r(S,B){if(!S)return S;var O=B||{},C=O.dense?[]:{},H="Sheet1",ce="",he=0,oe={},re=[],Oe=[],Te={s:{r:0,c:0},e:{r:0,c:0}},je=O.sheetRows||0;if(S[2]==0&&(S[3]==8||S[3]==9)&&S.length>=16&&S[14]==5&&S[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(S[2]==2)O.Enum=rr,e(S,function(w,W,fe){switch(fe){case 0:O.vers=w,w>=4096&&(O.qpro=!0);break;case 6:Te=w;break;case 204:w&&(ce=w);break;case 222:ce=w;break;case 15:case 51:O.qpro||(w[1].v=w[1].v.slice(1));case 13:case 14:case 16:fe==14&&(w[2]&112)==112&&(w[2]&15)>1&&(w[2]&15)<15&&(w[1].z=O.dateNF||Ve[14],O.cellDates&&(w[1].t="d",w[1].v=Ra(w[1].v))),O.qpro&&w[3]>he&&(C["!ref"]=Ke(Te),oe[H]=C,re.push(H),C=O.dense?[]:{},Te={s:{r:0,c:0},e:{r:0,c:0}},he=w[3],H=ce||"Sheet"+(he+1),ce="");var de=O.dense?(C[w[0].r]||[])[w[0].c]:C[Ie(w[0])];if(de){de.t=w[1].t,de.v=w[1].v,w[1].z!=null&&(de.z=w[1].z),w[1].f!=null&&(de.f=w[1].f);break}O.dense?(C[w[0].r]||(C[w[0].r]=[]),C[w[0].r][w[0].c]=w[1]):C[Ie(w[0])]=w[1];break}},O);else if(S[2]==26||S[2]==14)O.Enum=lr,S[2]==14&&(O.qpro=!0,S.l=0),e(S,function(w,W,fe){switch(fe){case 204:H=w;break;case 22:w[1].v=w[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(w[3]>he&&(C["!ref"]=Ke(Te),oe[H]=C,re.push(H),C=O.dense?[]:{},Te={s:{r:0,c:0},e:{r:0,c:0}},he=w[3],H="Sheet"+(he+1)),je>0&&w[0].r>=je)break;O.dense?(C[w[0].r]||(C[w[0].r]=[]),C[w[0].r][w[0].c]=w[1]):C[Ie(w[0])]=w[1],Te.e.c<w[0].c&&(Te.e.c=w[0].c),Te.e.r<w[0].r&&(Te.e.r=w[0].r);break;case 27:w[14e3]&&(Oe[w[14e3][0]]=w[14e3][1]);break;case 1537:Oe[w[0]]=w[1],w[0]==he&&(H=w[1]);break}},O);else throw new Error("Unrecognized LOTUS BOF "+S[2]);if(C["!ref"]=Ke(Te),oe[ce||H]=C,re.push(ce||H),!Oe.length)return{SheetNames:re,Sheets:oe};for(var Ae={},Rr=[],z=0;z<Oe.length;++z)oe[re[z]]?(Rr.push(Oe[z]||re[z]),Ae[Oe[z]]=oe[Oe[z]]||oe[re[z]]):(Rr.push(Oe[z]),Ae[Oe[z]]={"!ref":"A1"});return{SheetNames:Rr,Sheets:Ae}}function n(S,B){var O=B||{};if(+O.codepage>=0&&Xt(+O.codepage),O.type=="string")throw new Error("Cannot write WK1 to JS string");var C=_r(),H=Le(S["!ref"]),ce=Array.isArray(S),he=[];Q(C,0,i(1030)),Q(C,6,o(H));for(var oe=Math.min(H.e.r,8191),re=H.s.r;re<=oe;++re)for(var Oe=sr(re),Te=H.s.c;Te<=H.e.c;++Te){re===H.s.r&&(he[Te]=hr(Te));var je=he[Te]+Oe,Ae=ce?(S[re]||[])[Te]:S[je];if(!(!Ae||Ae.t=="z"))if(Ae.t=="n")(Ae.v|0)==Ae.v&&Ae.v>=-32768&&Ae.v<=32767?Q(C,13,p(re,Te,Ae.v)):Q(C,14,h(re,Te,Ae.v));else{var Rr=$r(Ae);Q(C,15,x(re,Te,Rr.slice(0,239)))}}return Q(C,1),C.end()}function a(S,B){var O=B||{};if(+O.codepage>=0&&Xt(+O.codepage),O.type=="string")throw new Error("Cannot write WK3 to JS string");var C=_r();Q(C,0,s(S));for(var H=0,ce=0;H<S.SheetNames.length;++H)(S.Sheets[S.SheetNames[H]]||{})["!ref"]&&Q(C,27,Ge(S.SheetNames[H],ce++));var he=0;for(H=0;H<S.SheetNames.length;++H){var oe=S.Sheets[S.SheetNames[H]];if(!(!oe||!oe["!ref"])){for(var re=Le(oe["!ref"]),Oe=Array.isArray(oe),Te=[],je=Math.min(re.e.r,8191),Ae=re.s.r;Ae<=je;++Ae)for(var Rr=sr(Ae),z=re.s.c;z<=re.e.c;++z){Ae===re.s.r&&(Te[z]=hr(z));var w=Te[z]+Rr,W=Oe?(oe[Ae]||[])[z]:oe[w];if(!(!W||W.t=="z"))if(W.t=="n")Q(C,23,X(Ae,z,he,W.v));else{var fe=$r(W);Q(C,22,N(Ae,z,he,fe.slice(0,239)))}}++he}}return Q(C,1),C.end()}function i(S){var B=b(2);return B.write_shift(2,S),B}function s(S){var B=b(26);B.write_shift(2,4096),B.write_shift(2,4),B.write_shift(4,0);for(var O=0,C=0,H=0,ce=0;ce<S.SheetNames.length;++ce){var he=S.SheetNames[ce],oe=S.Sheets[he];if(!(!oe||!oe["!ref"])){++H;var re=Cr(oe["!ref"]);O<re.e.r&&(O=re.e.r),C<re.e.c&&(C=re.e.c)}}return O>8191&&(O=8191),B.write_shift(2,O),B.write_shift(1,H),B.write_shift(1,C),B.write_shift(2,0),B.write_shift(2,0),B.write_shift(1,1),B.write_shift(1,2),B.write_shift(4,0),B.write_shift(4,0),B}function f(S,B,O){var C={s:{c:0,r:0},e:{c:0,r:0}};return B==8&&O.qpro?(C.s.c=S.read_shift(1),S.l++,C.s.r=S.read_shift(2),C.e.c=S.read_shift(1),S.l++,C.e.r=S.read_shift(2),C):(C.s.c=S.read_shift(2),C.s.r=S.read_shift(2),B==12&&O.qpro&&(S.l+=2),C.e.c=S.read_shift(2),C.e.r=S.read_shift(2),B==12&&O.qpro&&(S.l+=2),C.s.c==65535&&(C.s.c=C.e.c=C.s.r=C.e.r=0),C)}function o(S){var B=b(8);return B.write_shift(2,S.s.c),B.write_shift(2,S.s.r),B.write_shift(2,S.e.c),B.write_shift(2,S.e.r),B}function l(S,B,O){var C=[{c:0,r:0},{t:"n",v:0},0,0];return O.qpro&&O.vers!=20768?(C[0].c=S.read_shift(1),C[3]=S.read_shift(1),C[0].r=S.read_shift(2),S.l+=2):(C[2]=S.read_shift(1),C[0].c=S.read_shift(2),C[0].r=S.read_shift(2)),C}function u(S,B,O){var C=S.l+B,H=l(S,B,O);if(H[1].t="s",O.vers==20768){S.l++;var ce=S.read_shift(1);return H[1].v=S.read_shift(ce,"utf8"),H}return O.qpro&&S.l++,H[1].v=S.read_shift(C-S.l,"cstr"),H}function x(S,B,O){var C=b(7+O.length);C.write_shift(1,255),C.write_shift(2,B),C.write_shift(2,S),C.write_shift(1,39);for(var H=0;H<C.length;++H){var ce=O.charCodeAt(H);C.write_shift(1,ce>=128?95:ce)}return C.write_shift(1,0),C}function d(S,B,O){var C=l(S,B,O);return C[1].v=S.read_shift(2,"i"),C}function p(S,B,O){var C=b(7);return C.write_shift(1,255),C.write_shift(2,B),C.write_shift(2,S),C.write_shift(2,O,"i"),C}function E(S,B,O){var C=l(S,B,O);return C[1].v=S.read_shift(8,"f"),C}function h(S,B,O){var C=b(13);return C.write_shift(1,255),C.write_shift(2,B),C.write_shift(2,S),C.write_shift(8,O,"f"),C}function g(S,B,O){var C=S.l+B,H=l(S,B,O);if(H[1].v=S.read_shift(8,"f"),O.qpro)S.l=C;else{var ce=S.read_shift(2);M(S.slice(S.l,S.l+ce),H),S.l+=ce}return H}function y(S,B,O){var C=B&32768;return B&=-32769,B=(C?S:0)+(B>=8192?B-16384:B),(C?"":"$")+(O?hr(B):sr(B))}var F={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},D=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function M(S,B){Ar(S,0);for(var O=[],C=0,H="",ce="",he="",oe="";S.l<S.length;){var re=S[S.l++];switch(re){case 0:O.push(S.read_shift(8,"f"));break;case 1:ce=y(B[0].c,S.read_shift(2),!0),H=y(B[0].r,S.read_shift(2),!1),O.push(ce+H);break;case 2:{var Oe=y(B[0].c,S.read_shift(2),!0),Te=y(B[0].r,S.read_shift(2),!1);ce=y(B[0].c,S.read_shift(2),!0),H=y(B[0].r,S.read_shift(2),!1),O.push(Oe+Te+":"+ce+H)}break;case 3:if(S.l<S.length){console.error("WK1 premature formula end");return}break;case 4:O.push("("+O.pop()+")");break;case 5:O.push(S.read_shift(2));break;case 6:{for(var je="";re=S[S.l++];)je+=String.fromCharCode(re);O.push('"'+je.replace(/"/g,'""')+'"')}break;case 8:O.push("-"+O.pop());break;case 23:O.push("+"+O.pop());break;case 22:O.push("NOT("+O.pop()+")");break;case 20:case 21:oe=O.pop(),he=O.pop(),O.push(["AND","OR"][re-20]+"("+he+","+oe+")");break;default:if(re<32&&D[re])oe=O.pop(),he=O.pop(),O.push(he+D[re]+oe);else if(F[re]){if(C=F[re][1],C==69&&(C=S[S.l++]),C>O.length){console.error("WK1 bad formula parse 0x"+re.toString(16)+":|"+O.join("|")+"|");return}var Ae=O.slice(-C);O.length-=C,O.push(F[re][0]+"("+Ae.join(",")+")")}else return re<=7?console.error("WK1 invalid opcode "+re.toString(16)):re<=24?console.error("WK1 unsupported op "+re.toString(16)):re<=30?console.error("WK1 invalid opcode "+re.toString(16)):re<=115?console.error("WK1 unsupported function opcode "+re.toString(16)):console.error("WK1 unrecognized opcode "+re.toString(16))}}O.length==1?B[1].f=""+O[0]:console.error("WK1 bad formula parse |"+O.join("|")+"|")}function q(S){var B=[{c:0,r:0},{t:"n",v:0},0];return B[0].r=S.read_shift(2),B[3]=S[S.l++],B[0].c=S[S.l++],B}function ie(S,B){var O=q(S);return O[1].t="s",O[1].v=S.read_shift(B-4,"cstr"),O}function N(S,B,O,C){var H=b(6+C.length);H.write_shift(2,S),H.write_shift(1,O),H.write_shift(1,B),H.write_shift(1,39);for(var ce=0;ce<C.length;++ce){var he=C.charCodeAt(ce);H.write_shift(1,he>=128?95:he)}return H.write_shift(1,0),H}function V(S,B){var O=q(S);O[1].v=S.read_shift(2);var C=O[1].v>>1;if(O[1].v&1)switch(C&7){case 0:C=(C>>3)*5e3;break;case 1:C=(C>>3)*500;break;case 2:C=(C>>3)/20;break;case 3:C=(C>>3)/200;break;case 4:C=(C>>3)/2e3;break;case 5:C=(C>>3)/2e4;break;case 6:C=(C>>3)/16;break;case 7:C=(C>>3)/64;break}return O[1].v=C,O}function L(S,B){var O=q(S),C=S.read_shift(4),H=S.read_shift(4),ce=S.read_shift(2);if(ce==65535)return C===0&&H===3221225472?(O[1].t="e",O[1].v=15):C===0&&H===3489660928?(O[1].t="e",O[1].v=42):O[1].v=0,O;var he=ce&32768;return ce=(ce&32767)-16446,O[1].v=(1-he*2)*(H*Math.pow(2,ce+32)+C*Math.pow(2,ce)),O}function X(S,B,O,C){var H=b(14);if(H.write_shift(2,S),H.write_shift(1,O),H.write_shift(1,B),C==0)return H.write_shift(4,0),H.write_shift(4,0),H.write_shift(2,65535),H;var ce=0,he=0,oe=0,re=0;return C<0&&(ce=1,C=-C),he=Math.log2(C)|0,C/=Math.pow(2,he-31),re=C>>>0,(re&2147483648)==0&&(C/=2,++he,re=C>>>0),C-=re,re|=2147483648,re>>>=0,C*=Math.pow(2,32),oe=C>>>0,H.write_shift(4,oe),H.write_shift(4,re),he+=16383+(ce?32768:0),H.write_shift(2,he),H}function K(S,B){var O=L(S);return S.l+=B-14,O}function Y(S,B){var O=q(S),C=S.read_shift(4);return O[1].v=C>>6,O}function te(S,B){var O=q(S),C=S.read_shift(8,"f");return O[1].v=C,O}function we(S,B){var O=te(S);return S.l+=B-10,O}function me(S,B){return S[S.l+B-1]==0?S.read_shift(B,"cstr"):""}function He(S,B){var O=S[S.l++];O>B-1&&(O=B-1);for(var C="";C.length<O;)C+=String.fromCharCode(S[S.l++]);return C}function Be(S,B,O){if(!(!O.qpro||B<21)){var C=S.read_shift(1);S.l+=17,S.l+=1,S.l+=2;var H=S.read_shift(B-21,"cstr");return[C,H]}}function gr(S,B){for(var O={},C=S.l+B;S.l<C;){var H=S.read_shift(2);if(H==14e3){for(O[H]=[0,""],O[H][0]=S.read_shift(2);S[S.l];)O[H][1]+=String.fromCharCode(S[S.l]),S.l++;S.l++}}return O}function Ge(S,B){var O=b(5+S.length);O.write_shift(2,14e3),O.write_shift(2,B);for(var C=0;C<S.length;++C){var H=S.charCodeAt(C);O[O.l++]=H>127?95:H}return O[O.l++]=0,O}var rr={0:{n:"BOF",f:oi},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:E},15:{n:"LABEL",f:u},16:{n:"FORMULA",f:g},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:u},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:me},222:{n:"SHEETNAMELP",f:He},65535:{n:""}},lr={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:ie},23:{n:"NUMBER17",f:L},24:{n:"NUMBER18",f:V},25:{n:"FORMULA19",f:K},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:gr},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:Y},38:{n:"??"},39:{n:"NUMBER27",f:te},40:{n:"FORMULA28",f:we},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:me},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:Be},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}(),kl=/^\s|\s$|[\t\n\r]/;function pi(e,t){if(!t.bookSST)return"";var r=[Ye];r[r.length]=Z("sst",null,{xmlns:Dt[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(e[n]!=null){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(kl)&&(i+=' xml:space="preserve"'),i+=">"+Re(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}function Pl(e){return[e.read_shift(4),e.read_shift(4)]}function Ll(e,t){return t||(t=b(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}var Bl=Cf;function Ml(e){var t=_r();G(t,159,Ll(e));for(var r=0;r<e.length;++r)G(t,19,Bl(e[r]));return G(t,160),t.end()}function bl(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function mi(e){var t=0,r,n=bl(e),a=n.length+1,i,s,f,o,l;for(r=ct(a),r[0]=n.length,i=1;i!=a;++i)r[i]=n[i-1];for(i=a-1;i>=0;--i)s=r[i],f=(t&16384)===0?0:1,o=t<<1&32767,l=f|o,t=l^s;return t^52811}var Ul=function(){function e(a,i){switch(i.type){case"base64":return t(zr(a),i);case"binary":return t(a,i);case"buffer":return t(ye&&Buffer.isBuffer(a)?a.toString("binary"):qt(a),i);case"array":return t(Ln(a),i)}throw new Error("Unrecognized type "+i.type)}function t(a,i){var s=i||{},f=s.dense?[]:{},o=a.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var l={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(u,x){Array.isArray(f)&&(f[x]=[]);for(var d=/\\\w+\b/g,p=0,E,h=-1;E=d.exec(u);){switch(E[0]){case"\\cell":var g=u.slice(p,d.lastIndex-E[0].length);if(g[0]==" "&&(g=g.slice(1)),++h,g.length){var y={v:g,t:"s"};Array.isArray(f)?f[x][h]=y:f[Ie({r:x,c:h})]=y}break}p=d.lastIndex}h>l.e.c&&(l.e.c=h)}),f["!ref"]=Ke(l),f}function r(a,i){return dt(e(a,i),i)}function n(a){for(var i=["{\\rtf1\\ansi"],s=Le(a["!ref"]),f,o=Array.isArray(a),l=s.s.r;l<=s.e.r;++l){i.push("\\trowd\\trautofit1");for(var u=s.s.c;u<=s.e.c;++u)i.push("\\cellx"+(u+1));for(i.push("\\pard\\intbl"),u=s.s.c;u<=s.e.c;++u){var x=Ie({r:l,c:u});f=o?(a[l]||[])[u]:a[x],!(!f||f.v==null&&(!f.f||f.F))&&(i.push(" "+(f.w||($r(f),f.w))),i.push("\\cell"))}i.push("\\pard\\intbl\\row")}return i.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function fa(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var Wl=6,Xr=Wl;function Cn(e){return Math.floor((e+Math.round(128/Xr)/256)*Xr)}function yn(e){return Math.floor((e-5)/Xr*100+.5)/100}function a0(e){return Math.round((e*Xr+5)/Xr*256)/256}function _0(e){e.width?(e.wpx=Cn(e.width),e.wch=yn(e.wpx),e.MDW=Xr):e.wpx?(e.wch=yn(e.wpx),e.width=a0(e.wch),e.MDW=Xr):typeof e.wch=="number"&&(e.width=a0(e.wch),e.wpx=Cn(e.width),e.MDW=Xr),e.customWidth&&delete e.customWidth}var Vl=96,vi=Vl;function On(e){return e*96/vi}function gi(e){return e*vi/96}function Hl(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)e[n]!=null&&(t[t.length]=Z("numFmt",null,{numFmtId:n,formatCode:Re(e[n])}))}),t.length===1?"":(t[t.length]="</numFmts>",t[0]=Z("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function Gl(e){var t=[];return t[t.length]=Z("cellXfs",null),e.forEach(function(r){t[t.length]=Z("xf",null,r)}),t[t.length]="</cellXfs>",t.length===2?"":(t[0]=Z("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}function _i(e,t){var r=[Ye,Z("styleSheet",null,{xmlns:Dt[0],"xmlns:vt":Ze.vt})],n;return e.SSF&&(n=Hl(e.SSF))!=null&&(r[r.length]=n),r[r.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',r[r.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',r[r.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',r[r.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(n=Gl(t.cellXfs))&&(r[r.length]=n),r[r.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',r[r.length]='<dxfs count="0"/>',r[r.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',r.length>2&&(r[r.length]="</styleSheet>",r[1]=r[1].replace("/>",">")),r.join("")}function Xl(e,t){var r=e.read_shift(2),n=xr(e);return[r,n]}function zl(e,t,r){r||(r=b(6+4*t.length)),r.write_shift(2,e),er(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return r.l==null&&(r.l=r.length),n}function $l(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=kf(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var i=e.read_shift(2);switch(i===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var s=e.read_shift(1);s!=0&&(n.underline=s);var f=e.read_shift(1);f>0&&(n.family=f);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=If(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=xr(e),n}function Kl(e,t){t||(t=b(25+4*32)),t.write_shift(2,e.sz*20),Pf(e,t),t.write_shift(2,e.bold?700:400);var r=0;e.vertAlign=="superscript"?r=1:e.vertAlign=="subscript"&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Sn(e.color,t);var n=0;return e.scheme=="major"&&(n=1),e.scheme=="minor"&&(n=2),t.write_shift(1,n),er(e.name,t),t.length>t.l?t.slice(0,t.l):t}var Yl=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Jn,jl=Ur;function la(e,t){t||(t=b(4*3+8*7+16*1)),Jn||(Jn=f0(Yl));var r=Jn[e.patternType];r==null&&(r=40),t.write_shift(4,r);var n=0;if(r!=40)for(Sn({auto:1},t),Sn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function ql(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}function Ei(e,t,r){r||(r=b(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);var n=0;return r.write_shift(1,n),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Bt(e,t){return t||(t=b(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var Jl=Ur;function Zl(e,t){return t||(t=b(51)),t.write_shift(1,0),Bt(null,t),Bt(null,t),Bt(null,t),Bt(null,t),Bt(null,t),t.length>t.l?t.slice(0,t.l):t}function Ql(e,t){return t||(t=b(12+4*10)),t.write_shift(4,e.xfId),t.write_shift(2,1),t.write_shift(1,+e.builtinId),t.write_shift(1,0),An(e.name||"",t),t.length>t.l?t.slice(0,t.l):t}function eo(e,t,r){var n=b(2052);return n.write_shift(4,e),An(t,n),An(r,n),n.length>n.l?n.slice(0,n.l):n}function ro(e,t){if(!!t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&++r}),r!=0&&(G(e,615,Br(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&G(e,44,zl(a,t[a]))}),G(e,616))}}function to(e){var t=1;G(e,611,Br(t)),G(e,43,Kl({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),G(e,612)}function no(e){var t=2;G(e,603,Br(t)),G(e,45,la({patternType:"none"})),G(e,45,la({patternType:"gray125"})),G(e,604)}function ao(e){var t=1;G(e,613,Br(t)),G(e,46,Zl()),G(e,614)}function io(e){var t=1;G(e,626,Br(t)),G(e,47,Ei({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),G(e,627)}function so(e,t){G(e,617,Br(t.length)),t.forEach(function(r){G(e,47,Ei(r,0))}),G(e,618)}function fo(e){var t=1;G(e,619,Br(t)),G(e,48,Ql({xfId:0,builtinId:0,name:"Normal"})),G(e,620)}function lo(e){var t=0;G(e,505,Br(t)),G(e,506)}function oo(e){var t=0;G(e,508,eo(t,"TableStyleMedium9","PivotStyleMedium4")),G(e,509)}function uo(e,t){var r=_r();return G(r,278),ro(r,e.SSF),to(r),no(r),ao(r),io(r),so(r,t.cellXfs),fo(r),lo(r),oo(r),G(r,279),r.end()}function Ti(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&typeof e.raw=="string")return e.raw;var r=[Ye];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uFF2D\uFF33 \uFF30\u30B4\u30B7\u30C3\u30AF"/>',r[r.length]='<a:font script="Hang" typeface="\uB9D1\uC740 \uACE0\uB515"/>',r[r.length]='<a:font script="Hans" typeface="\u5B8B\u4F53"/>',r[r.length]='<a:font script="Hant" typeface="\u65B0\u7D30\u660E\u9AD4"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function co(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:xr(e)}}function ho(e){var t=b(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),er(e.name,t),t.slice(0,t.l)}function xo(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function po(e){var t=b(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}function mo(e,t){var r=b(8+2*t.length);return r.write_shift(4,e),er(t,r),r.slice(0,r.l)}function vo(e){return e.l+=4,e.read_shift(4)!=0}function go(e,t){var r=b(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}function _o(){var e=_r();return G(e,332),G(e,334,Br(1)),G(e,335,ho({name:"XLDAPR",version:12e4,flags:3496657072})),G(e,336),G(e,339,mo(1,"XLDAPR")),G(e,52),G(e,35,Br(514)),G(e,4096,Br(0)),G(e,4097,Dr(1)),G(e,36),G(e,53),G(e,340),G(e,337,go(1,!0)),G(e,51,po([[1,0]])),G(e,338),G(e,333),e.end()}function wi(){var e=[Ye];return e.push(`<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">
  <metadataTypes count="1">
    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>
  </metadataTypes>
  <futureMetadata name="XLDAPR" count="1">
    <bk>
      <extLst>
        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">
          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>
        </ext>
      </extLst>
    </bk>
  </futureMetadata>
  <cellMetadata count="1">
    <bk>
      <rc t="1" v="0"/>
    </bk>
  </cellMetadata>
</metadata>`),e.join("")}function Eo(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Ie(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}var At=1024;function Ai(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Z("xml",null,{"xmlns:v":Sr.v,"xmlns:o":Sr.o,"xmlns:x":Sr.x,"xmlns:mv":Sr.mv}).replace(/\/>/,">"),Z("o:shapelayout",Z("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Z("v:shapetype",[Z("v:stroke",null,{joinstyle:"miter"}),Z("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];At<e*1e3;)At+=1e3;return t.forEach(function(i){var s=Qe(i[0]),f={color2:"#BEFF82",type:"gradient"};f.type=="gradient"&&(f.angle="-180");var o=f.type=="gradient"?Z("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,l=Z("v:fill",o,f),u={on:"t",obscured:"t"};++At,a=a.concat(["<v:shape"+Kt({id:"_x0000_s"+At,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(i[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",l,Z("v:shadow",null,u),Z("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",ir("x:Anchor",[s.c+1,0,s.r+1,0,s.c+3,20,s.r+5,20].join(",")),ir("x:AutoFill","False"),ir("x:Row",String(s.r)),ir("x:Column",String(s.c)),i[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function Si(e){var t=[Ye,Z("comments",null,{xmlns:Dt[0]})],r=[];return t.push("<authors>"),e.forEach(function(n){n[1].forEach(function(a){var i=Re(a.a);r.indexOf(i)==-1&&(r.push(i),t.push("<author>"+i+"</author>")),a.T&&a.ID&&r.indexOf("tc="+a.ID)==-1&&(r.push("tc="+a.ID),t.push("<author>tc="+a.ID+"</author>"))})}),r.length==0&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(n){var a=0,i=[];if(n[1][0]&&n[1][0].T&&n[1][0].ID?a=r.indexOf("tc="+n[1][0].ID):n[1].forEach(function(o){o.a&&(a=r.indexOf(Re(o.a))),i.push(o.t||"")}),t.push('<comment ref="'+n[0]+'" authorId="'+a+'"><text>'),i.length<=1)t.push(ir("t",Re(i[0]||"")));else{for(var s=`Comment:
    `+i[0]+`
`,f=1;f<i.length;++f)s+=`Reply:
    `+i[f]+`
`;t.push(ir("t",Re(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function To(e,t,r){var n=[Ye,Z("ThreadedComments",null,{xmlns:Ze.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(a){var i="";(a[1]||[]).forEach(function(s,f){if(!s.T){delete s.ID;return}s.a&&t.indexOf(s.a)==-1&&t.push(s.a);var o={ref:a[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};f==0?i=o.id:o.parentId=i,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(Z("threadedComment",ir("text",s.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}function wo(e){var t=[Ye,Z("personList",null,{xmlns:Ze.TCMNT,"xmlns:x":Dt[0]}).replace(/[\/]>/,">")];return e.forEach(function(r,n){t.push(Z("person",null,{displayName:r,id:"{54EE7950-7262-4200-6969-"+("000000000000"+n).slice(-12)+"}",userId:r,providerId:"None"}))}),t.push("</personList>"),t.join("")}function Ao(e){var t={};t.iauthor=e.read_shift(4);var r=gt(e);return t.rfx=r.s,t.ref=Ie(r.s),e.l+=16,t}function So(e,t){return t==null&&(t=b(36)),t.write_shift(4,e[1].iauthor),Rt(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}var Fo=xr;function Co(e){return er(e.slice(0,54))}function yo(e){var t=_r(),r=[];return G(t,628),G(t,630),e.forEach(function(n){n[1].forEach(function(a){r.indexOf(a.a)>-1||(r.push(a.a.slice(0,54)),G(t,632,Co(a.a)))})}),G(t,631),G(t,633),e.forEach(function(n){n[1].forEach(function(a){a.iauthor=r.indexOf(a.a);var i={s:Qe(n[0]),e:Qe(n[0])};G(t,635,So([i,a])),a.t&&a.t.length>0&&G(t,637,Of(a)),G(t,636),delete a.iauthor})}),G(t,634),G(t,629),t.end()}function Oo(e,t){t.FullPaths.forEach(function(r,n){if(n!=0){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");a.slice(-1)!=="/"&&ke.utils.cfb_add(e,a,t.FileIndex[n].content)}})}var Fi=["xlsb","xlsm","xlam","biff8","xla"],Do=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,i,s){var f=!1,o=!1;i.length==0?o=!0:i.charAt(0)=="["&&(o=!0,i=i.slice(1,-1)),s.length==0?f=!0:s.charAt(0)=="["&&(f=!0,s=s.slice(1,-1));var l=i.length>0?parseInt(i,10)|0:0,u=s.length>0?parseInt(s,10)|0:0;return f?u+=t.c:--u,o?l+=t.r:--l,a+(f?"":"$")+hr(u)+(o?"":"$")+sr(l)}return function(a,i){return t=i,a.replace(e,r)}}(),E0=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,T0=function(){return function(t,r){return t.replace(E0,function(n,a,i,s,f,o){var l=d0(s)-(i?0:r.c),u=x0(o)-(f?0:r.r),x=u==0?"":f?u+1:"["+u+"]",d=l==0?"":i?l+1:"["+l+"]";return a+"R"+x+"C"+d})}}();function No(e,t){return e.replace(E0,function(r,n,a,i,s,f){return n+(a=="$"?a+i:hr(d0(i)+t.c))+(s=="$"?s+f:sr(x0(f)+t.r))})}function Ro(e){return e.length!=1}function $e(e){e.l+=1}function rt(e,t){var r=e.read_shift(t==1?1:2);return[r&16383,r>>14&1,r>>15&1]}function Ci(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return yi(e);r.biff==12&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),s=rt(e,2),f=rt(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:i,c:f[0],cRel:f[1],rRel:f[2]}}}function yi(e){var t=rt(e,2),r=rt(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Io(e,t,r){if(r.biff<8)return yi(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),i=rt(e,2),s=rt(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:s[0],cRel:s[1],rRel:s[2]}}}function Oi(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return ko(e);var n=e.read_shift(r&&r.biff==12?4:2),a=rt(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function ko(e){var t=rt(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function Po(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Lo(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Bo(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),s=(i&16384)>>14,f=(i&32768)>>15;if(i&=16383,f==1)for(;a>524287;)a-=1048576;if(s==1)for(;i>8191;)i=i-16384;return{r:a,c:i,cRel:s,rRel:f}}function Bo(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function Mo(e,t,r){var n=(e[e.l++]&96)>>5,a=Ci(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function bo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}var s=Ci(e,i,r);return[n,a,s]}function Uo(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Wo(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12;break}return e.l+=i,[n,a]}function Vo(e,t,r){var n=(e[e.l++]&96)>>5,a=Io(e,t-1,r);return[n,a]}function Ho(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function oa(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Go(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function Xo(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function zo(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function $o(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function Ko(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function Di(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function Yo(e){return e.read_shift(2),Di(e)}function jo(e){return e.read_shift(2),Di(e)}function qo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Oi(e,0,r);return[n,a]}function Jo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Lo(e,0,r);return[n,a]}function Zo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var i=Oi(e,0,r);return[n,a,i]}function Qo(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[Qu[a],Ii[a],n]}function eu(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:ru(e);return[a,(i[0]===0?Ii:Zu)[i[1]]]}function ru(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function tu(e,t,r){e.l+=r&&r.biff==2?3:4}function nu(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function au(e){return e.l++,Qt[e.read_shift(1)]}function iu(e){return e.l++,e.read_shift(2)}function su(e){return e.l++,e.read_shift(1)!==0}function fu(e){return e.l++,It(e)}function lu(e,t,r){return e.l++,ci(e,t-1,r)}function ou(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=Jf(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Qt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=It(e);break;case 2:r[1]=rl(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function uu(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],i=0;i!=n;++i)a.push((r.biff==12?gt:al)(e));return a}function cu(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var i=0,s=[];i!=n&&(s[i]=[]);++i)for(var f=0;f!=a;++f)s[i][f]=ou(e,r.biff);return s}function hu(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,i]}function xu(e,t,r){if(r.biff==5)return du(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),i=e.read_shift(4);return[n,a,i]}function du(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function pu(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function mu(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function vu(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function gu(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6;break}return e.l+=i,[n,a]}var _u=Ur,Eu=Ur,Tu=Ur;function en(e,t,r){return e.l+=2,[Po(e)]}function w0(e){return e.l+=6,[]}var wu=en,Au=w0,Su=w0,Fu=en;function Ni(e){return e.l+=2,[oi(e),e.read_shift(2)&1]}var Cu=en,yu=Ni,Ou=w0,Du=en,Nu=en,Ru=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Iu(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2),s=Ru[r>>2&31];return{ixti:t,coltype:r&3,rt:s,idx:n,c:a,C:i}}function ku(e){return e.l+=2,[e.read_shift(4)]}function Pu(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Lu(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Bu(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function Mu(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function bu(e){return e.l+=4,[0,0]}var ua={1:{n:"PtgExp",f:nu},2:{n:"PtgTbl",f:Tu},3:{n:"PtgAdd",f:$e},4:{n:"PtgSub",f:$e},5:{n:"PtgMul",f:$e},6:{n:"PtgDiv",f:$e},7:{n:"PtgPower",f:$e},8:{n:"PtgConcat",f:$e},9:{n:"PtgLt",f:$e},10:{n:"PtgLe",f:$e},11:{n:"PtgEq",f:$e},12:{n:"PtgGe",f:$e},13:{n:"PtgGt",f:$e},14:{n:"PtgNe",f:$e},15:{n:"PtgIsect",f:$e},16:{n:"PtgUnion",f:$e},17:{n:"PtgRange",f:$e},18:{n:"PtgUplus",f:$e},19:{n:"PtgUminus",f:$e},20:{n:"PtgPercent",f:$e},21:{n:"PtgParen",f:$e},22:{n:"PtgMissArg",f:$e},23:{n:"PtgStr",f:lu},26:{n:"PtgSheet",f:Pu},27:{n:"PtgEndSheet",f:Lu},28:{n:"PtgErr",f:au},29:{n:"PtgBool",f:su},30:{n:"PtgInt",f:iu},31:{n:"PtgNum",f:fu},32:{n:"PtgArray",f:Ho},33:{n:"PtgFunc",f:Qo},34:{n:"PtgFuncVar",f:eu},35:{n:"PtgName",f:hu},36:{n:"PtgRef",f:qo},37:{n:"PtgArea",f:Mo},38:{n:"PtgMemArea",f:pu},39:{n:"PtgMemErr",f:_u},40:{n:"PtgMemNoMem",f:Eu},41:{n:"PtgMemFunc",f:mu},42:{n:"PtgRefErr",f:vu},43:{n:"PtgAreaErr",f:Uo},44:{n:"PtgRefN",f:Jo},45:{n:"PtgAreaN",f:Vo},46:{n:"PtgMemAreaN",f:Bu},47:{n:"PtgMemNoMemN",f:Mu},57:{n:"PtgNameX",f:xu},58:{n:"PtgRef3d",f:Zo},59:{n:"PtgArea3d",f:bo},60:{n:"PtgRefErr3d",f:gu},61:{n:"PtgAreaErr3d",f:Wo},255:{}},Uu={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Wu={1:{n:"PtgElfLel",f:Ni},2:{n:"PtgElfRw",f:Du},3:{n:"PtgElfCol",f:wu},6:{n:"PtgElfRwV",f:Nu},7:{n:"PtgElfColV",f:Fu},10:{n:"PtgElfRadical",f:Cu},11:{n:"PtgElfRadicalS",f:Ou},13:{n:"PtgElfColS",f:Au},15:{n:"PtgElfColSV",f:Su},16:{n:"PtgElfRadicalLel",f:yu},25:{n:"PtgList",f:Iu},29:{n:"PtgSxName",f:ku},255:{}},Vu={0:{n:"PtgAttrNoop",f:bu},1:{n:"PtgAttrSemi",f:Ko},2:{n:"PtgAttrIf",f:zo},4:{n:"PtgAttrChoose",f:Go},8:{n:"PtgAttrGoto",f:Xo},16:{n:"PtgAttrSum",f:tu},32:{n:"PtgAttrBaxcel",f:oa},33:{n:"PtgAttrBaxcel",f:oa},64:{n:"PtgAttrSpace",f:Yo},65:{n:"PtgAttrSpaceSemi",f:jo},128:{n:"PtgAttrIfError",f:$o},255:{}};function Hu(e,t,r,n){if(n.biff<8)return Ur(e,t);for(var a=e.l+t,i=[],s=0;s!==r.length;++s)switch(r[s][0]){case"PtgArray":r[s][1]=cu(e,0,n),i.push(r[s][1]);break;case"PtgMemArea":r[s][2]=uu(e,r[s][1],n),i.push(r[s][2]);break;case"PtgExp":n&&n.biff==12&&(r[s][1][1]=e.read_shift(4),i.push(r[s][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[s][0]}return t=a-e.l,t!==0&&i.push(Ur(e,t)),i}function Gu(e,t,r){for(var n=e.l+t,a,i,s=[];n!=e.l;)t=n-e.l,i=e[e.l],a=ua[i]||ua[Uu[i]],(i===24||i===25)&&(a=(i===24?Wu:Vu)[e[e.l+1]]),!a||!a.f?Ur(e,t):s.push([a.n,a.f(e,t,r)]);return s}function Xu(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var s=n[i];if(s)switch(s[0]){case 2:a.push('"'+s[1].replace(/"/g,'""')+'"');break;default:a.push(s[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var zu={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function $u(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Ri(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(i){return i.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function ca(e,t,r){var n=Ri(e,t,r);return n=="#REF"?n:$u(n,r)}function Ot(e,t,r,n,a){var i=a&&a.biff||8,s={s:{c:0,r:0},e:{c:0,r:0}},f=[],o,l,u,x=0,d=0,p,E="";if(!e[0]||!e[0][0])return"";for(var h=-1,g="",y=0,F=e[0].length;y<F;++y){var D=e[0][y];switch(D[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=f.pop(),l=f.pop(),h>=0){switch(e[0][h][1][0]){case 0:g=We(" ",e[0][h][1][1]);break;case 1:g=We("\r",e[0][h][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][h][1][0])}l=l+g,h=-1}f.push(l+zu[D[0]]+o);break;case"PtgIsect":o=f.pop(),l=f.pop(),f.push(l+" "+o);break;case"PtgUnion":o=f.pop(),l=f.pop(),f.push(l+","+o);break;case"PtgRange":o=f.pop(),l=f.pop(),f.push(l+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":u=Vt(D[1][1],s,a),f.push(Ht(u,i));break;case"PtgRefN":u=r?Vt(D[1][1],r,a):D[1][1],f.push(Ht(u,i));break;case"PtgRef3d":x=D[1][1],u=Vt(D[1][2],s,a),E=ca(n,x,a),f.push(E+"!"+Ht(u,i));break;case"PtgFunc":case"PtgFuncVar":var M=D[1][0],q=D[1][1];M||(M=0),M&=127;var ie=M==0?[]:f.slice(-M);f.length-=M,q==="User"&&(q=ie.shift()),f.push(q+"("+ie.join(",")+")");break;case"PtgBool":f.push(D[1]?"TRUE":"FALSE");break;case"PtgInt":f.push(D[1]);break;case"PtgNum":f.push(String(D[1]));break;case"PtgStr":f.push('"'+D[1].replace(/"/g,'""')+'"');break;case"PtgErr":f.push(D[1]);break;case"PtgAreaN":p=j0(D[1][1],r?{s:r}:s,a),f.push(jn(p,a));break;case"PtgArea":p=j0(D[1][1],s,a),f.push(jn(p,a));break;case"PtgArea3d":x=D[1][1],p=D[1][2],E=ca(n,x,a),f.push(E+"!"+jn(p,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=D[1][2];var N=(n.names||[])[d-1]||(n[0]||[])[d],V=N?N.Name:"SH33TJSNAME"+String(d);V&&V.slice(0,6)=="_xlfn."&&!a.xlfn&&(V=V.slice(6)),f.push(V);break;case"PtgNameX":var L=D[1][1];d=D[1][2];var X;if(a.biff<=5)L<0&&(L=-L),n[L]&&(X=n[L][d]);else{var K="";if(((n[L]||[])[0]||[])[0]==14849||(((n[L]||[])[0]||[])[0]==1025?n[L][d]&&n[L][d].itab>0&&(K=n.SheetNames[n[L][d].itab-1]+"!"):K=n.SheetNames[d-1]+"!"),n[L]&&n[L][d])K+=n[L][d].Name;else if(n[0]&&n[0][d])K+=n[0][d].Name;else{var Y=(Ri(n,L,a)||"").split(";;");Y[d-1]?K=Y[d-1]:K+="SH33TJSERRX"}f.push(K);break}X||(X={Name:"SH33TJSERRY"}),f.push(X.Name);break;case"PtgParen":var te="(",we=")";if(h>=0){switch(g="",e[0][h][1][0]){case 2:te=We(" ",e[0][h][1][1])+te;break;case 3:te=We("\r",e[0][h][1][1])+te;break;case 4:we=We(" ",e[0][h][1][1])+we;break;case 5:we=We("\r",e[0][h][1][1])+we;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][h][1][0])}h=-1}f.push(te+f.pop()+we);break;case"PtgRefErr":f.push("#REF!");break;case"PtgRefErr3d":f.push("#REF!");break;case"PtgExp":u={c:D[1][1],r:D[1][0]};var me={c:r.c,r:r.r};if(n.sharedf[Ie(u)]){var He=n.sharedf[Ie(u)];f.push(Ot(He,s,me,n,a))}else{var Be=!1;for(o=0;o!=n.arrayf.length;++o)if(l=n.arrayf[o],!(u.c<l[0].s.c||u.c>l[0].e.c)&&!(u.r<l[0].s.r||u.r>l[0].e.r)){f.push(Ot(l[1],s,me,n,a)),Be=!0;break}Be||f.push(D[1])}break;case"PtgArray":f.push("{"+Xu(D[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":h=y;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":f.push("");break;case"PtgAreaErr":f.push("#REF!");break;case"PtgAreaErr3d":f.push("#REF!");break;case"PtgList":f.push("Table"+D[1].idx+"[#"+D[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(D));default:throw new Error("Unrecognized Formula Token: "+String(D))}var gr=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&h>=0&&gr.indexOf(e[0][y][0])==-1){D=e[0][h];var Ge=!0;switch(D[1][0]){case 4:Ge=!1;case 0:g=We(" ",D[1][1]);break;case 5:Ge=!1;case 1:g=We("\r",D[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+D[1][0])}f.push((Ge?g:"")+f.pop()+(Ge?"":g)),h=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function Ku(e){if(e==null){var t=b(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}else if(typeof e=="number")return ht(e);return ht(0)}function Yu(e,t,r,n,a){var i=xt(t,r,a),s=Ku(e.v),f=b(6),o=33;f.write_shift(2,o),f.write_shift(4,0);for(var l=b(e.bf.length),u=0;u<e.bf.length;++u)l[u]=e.bf[u];var x=ar([i,s,f,l]);return x}function Bn(e,t,r){var n=e.read_shift(4),a=Gu(e,n,r),i=e.read_shift(4),s=i>0?Hu(e,i,a,r):null;return[a,s]}var ju=Bn,Mn=Bn,qu=Bn,Ju=Bn,Zu={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Ii={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Qu={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function ec(e){var t="of:="+e.replace(E0,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return t.replace(/;/g,"|").replace(/,/g,";")}function rc(e){return e.replace(/\./,"!")}var Gt=typeof Map!="undefined";function A0(e,t,r){var n=0,a=e.length;if(r){if(Gt?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var i=Gt?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t},e.Count++,e.Unique++,r&&(Gt?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function bn(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(Xr=t.MDW),t.width!=null?r.customWidth=1:t.wpx!=null?n=yn(t.wpx):t.wch!=null&&(n=t.wch),n>-1?(r.width=a0(n),r.customWidth=1):t.width!=null&&(r.width=t.width),t.hidden&&(r.hidden=!0),t.level!=null&&(r.outlineLevel=r.level=t.level),r}function ki(e,t){if(!!e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function nt(e,t,r){var n=r.revssf[t.z!=null?t.z:"General"],a=60,i=e.length;if(n==null&&r.ssf){for(;a<392;++a)if(r.ssf[a]==null){Oa(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function tc(e,t,r){if(e&&e["!ref"]){var n=Le(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}function nc(e){if(e.length===0)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+Ke(e[r])+'"/>';return t+"</mergeCells>"}function ac(e,t,r,n,a){var i=!1,s={},f=null;if(n.bookType!=="xlsx"&&t.vbaraw){var o=t.SheetNames[r];try{t.Workbook&&(o=t.Workbook.Sheets[r].CodeName||o)}catch{}i=!0,s.codeName=Vr(Re(o))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),f=(f||"")+Z("outlinePr",null,l)}!i&&!f||(a[a.length]=Z("sheetPr",f,s))}var ic=["objects","scenarios","selectLockedCells","selectUnlockedCells"],sc=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function fc(e){var t={sheet:1};return ic.forEach(function(r){e[r]!=null&&e[r]&&(t[r]="1")}),sc.forEach(function(r){e[r]!=null&&!e[r]&&(t[r]="0")}),e.password&&(t.password=mi(e.password).toString(16).toUpperCase()),Z("sheetProtection",null,t)}function lc(e){return ki(e),Z("pageMargins",null,e)}function oc(e,t){for(var r=["<cols>"],n,a=0;a!=t.length;++a)!(n=t[a])||(r[r.length]=Z("col",null,bn(a,n)));return r[r.length]="</cols>",r.join("")}function uc(e,t,r,n){var a=typeof e.ref=="string"?e.ref:Ke(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,s=Cr(a);s.s.r==s.e.r&&(s.e.r=Cr(t["!ref"]).e.r,a=Ke(s));for(var f=0;f<i.length;++f){var o=i[f];if(o.Name=="_xlnm._FilterDatabase"&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return f==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Z("autoFilter",null,{ref:a})}function cc(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Z("sheetViews",Z("sheetView",null,a),{})}function hc(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),e.v===void 0&&typeof e.f!="string"||e.t==="z"&&!e.f)return"";var a="",i=e.t,s=e.v;if(e.t!=="z")switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Qt[e.v];break;case"d":n&&n.cellDates?a=vr(e.v,-1).toISOString():(e=Tr(e),e.t="n",a=""+(e.v=Er(vr(e.v)))),typeof e.z=="undefined"&&(e.z=Ve[14]);break;default:a=e.v;break}var f=ir("v",Re(a)),o={r:t},l=nt(n.cellXfs,e,n);switch(l!==0&&(o.s=l),e.t){case"n":break;case"d":o.t="d";break;case"b":o.t="b";break;case"e":o.t="e";break;case"z":break;default:if(e.v==null){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){f=ir("v",""+A0(n.Strings,e.v,n.revStrings)),o.t="s";break}o.t="str";break}if(e.t!=i&&(e.t=i,e.v=s),typeof e.f=="string"&&e.f){var u=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;f=Z("f",Re(e.f),u)+(e.v!=null?f:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(o.cm=1),Z("c",f,o)}function xc(e,t,r,n){var a=[],i=[],s=Le(e["!ref"]),f="",o,l="",u=[],x=0,d=0,p=e["!rows"],E=Array.isArray(e),h={r:l},g,y=-1;for(d=s.s.c;d<=s.e.c;++d)u[d]=hr(d);for(x=s.s.r;x<=s.e.r;++x){for(i=[],l=sr(x),d=s.s.c;d<=s.e.c;++d){o=u[d]+l;var F=E?(e[x]||[])[d]:e[o];F!==void 0&&(f=hc(F,o,e,t))!=null&&i.push(f)}(i.length>0||p&&p[x])&&(h={r:l},p&&p[x]&&(g=p[x],g.hidden&&(h.hidden=1),y=-1,g.hpx?y=On(g.hpx):g.hpt&&(y=g.hpt),y>-1&&(h.ht=y,h.customHeight=1),g.level&&(h.outlineLevel=g.level)),a[a.length]=Z("row",i.join(""),h))}if(p)for(;x<p.length;++x)p&&p[x]&&(h={r:x+1},g=p[x],g.hidden&&(h.hidden=1),y=-1,g.hpx?y=On(g.hpx):g.hpt&&(y=g.hpt),y>-1&&(h.ht=y,h.customHeight=1),g.level&&(h.outlineLevel=g.level),a[a.length]=Z("row","",h));return a.join("")}function Pi(e,t,r,n){var a=[Ye,Z("worksheet",null,{xmlns:Dt[0],"xmlns:r":Ze.r})],i=r.SheetNames[e],s=0,f="",o=r.Sheets[i];o==null&&(o={});var l=o["!ref"]||"A1",u=Le(l);if(u.e.c>16383||u.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");u.e.c=Math.min(u.e.c,16383),u.e.r=Math.min(u.e.c,1048575),l=Ke(u)}n||(n={}),o["!comments"]=[];var x=[];ac(o,r,e,t,a),a[a.length]=Z("dimension",null,{ref:l}),a[a.length]=cc(o,t,e,r),t.sheetFormat&&(a[a.length]=Z("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),o["!cols"]!=null&&o["!cols"].length>0&&(a[a.length]=oc(o,o["!cols"])),a[s=a.length]="<sheetData/>",o["!links"]=[],o["!ref"]!=null&&(f=xc(o,t),f.length>0&&(a[a.length]=f)),a.length>s+1&&(a[a.length]="</sheetData>",a[s]=a[s].replace("/>",">")),o["!protect"]&&(a[a.length]=fc(o["!protect"])),o["!autofilter"]!=null&&(a[a.length]=uc(o["!autofilter"],o,r,e)),o["!merges"]!=null&&o["!merges"].length>0&&(a[a.length]=nc(o["!merges"]));var d=-1,p,E=-1;return o["!links"].length>0&&(a[a.length]="<hyperlinks>",o["!links"].forEach(function(h){!h[1].Target||(p={ref:h[0]},h[1].Target.charAt(0)!="#"&&(E=Ne(n,-1,Re(h[1].Target).replace(/#.*$/,""),Fe.HLINK),p["r:id"]="rId"+E),(d=h[1].Target.indexOf("#"))>-1&&(p.location=Re(h[1].Target.slice(d+1))),h[1].Tooltip&&(p.tooltip=Re(h[1].Tooltip)),a[a.length]=Z("hyperlink",null,p))}),a[a.length]="</hyperlinks>"),delete o["!links"],o["!margins"]!=null&&(a[a.length]=lc(o["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&(a[a.length]=ir("ignoredErrors",Z("ignoredError",null,{numberStoredAsText:1,sqref:l}))),x.length>0&&(E=Ne(n,-1,"../drawings/drawing"+(e+1)+".xml",Fe.DRAW),a[a.length]=Z("drawing",null,{"r:id":"rId"+E}),o["!drawing"]=x),o["!comments"].length>0&&(E=Ne(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Fe.VML),a[a.length]=Z("legacyDrawing",null,{"r:id":"rId"+E}),o["!legacy"]=E),a.length>1&&(a[a.length]="</worksheet>",a[1]=a[1].replace("/>",">")),a.join("")}function dc(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,i&7&&(r.level=i&7),i&16&&(r.hidden=!0),i&32&&(r.hpt=a/20),r}function pc(e,t,r){var n=b(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=On(a.hpx)*20:a.hpt&&(i=a.hpt*20),n.write_shift(2,i),n.write_shift(1,0);var s=0;a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var f=0,o=n.l;n.l+=4;for(var l={r:e,c:0},u=0;u<16;++u)if(!(t.s.c>u+1<<10||t.e.c<u<<10)){for(var x=-1,d=-1,p=u<<10;p<u+1<<10;++p){l.c=p;var E=Array.isArray(r)?(r[l.r]||[])[l.c]:r[Ie(l)];E&&(x<0&&(x=p),d=p)}x<0||(++f,n.write_shift(4,x),n.write_shift(4,d))}var h=n.l;return n.l=o,n.write_shift(4,f),n.l=h,n.length>n.l?n.slice(0,n.l):n}function mc(e,t,r,n){var a=pc(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&G(e,0,a)}var vc=gt,gc=Rt;function _c(){}function Ec(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Df(e),r}function Tc(e,t,r){r==null&&(r=b(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Sn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),ja(e,r),r.slice(0,r.l)}function wc(e){var t=Nr(e);return[t]}function Ac(e,t,r){return r==null&&(r=b(8)),pt(t,r)}function Sc(e){var t=mt(e);return[t]}function Fc(e,t,r){return r==null&&(r=b(4)),vt(t,r)}function Cc(e){var t=Nr(e),r=e.read_shift(1);return[t,r,"b"]}function yc(e,t,r){return r==null&&(r=b(9)),pt(t,r),r.write_shift(1,e.v?1:0),r}function Oc(e){var t=mt(e),r=e.read_shift(1);return[t,r,"b"]}function Dc(e,t,r){return r==null&&(r=b(5)),vt(t,r),r.write_shift(1,e.v?1:0),r}function Nc(e){var t=Nr(e),r=e.read_shift(1);return[t,r,"e"]}function Rc(e,t,r){return r==null&&(r=b(9)),pt(t,r),r.write_shift(1,e.v),r}function Ic(e){var t=mt(e),r=e.read_shift(1);return[t,r,"e"]}function kc(e,t,r){return r==null&&(r=b(8)),vt(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function Pc(e){var t=Nr(e),r=e.read_shift(4);return[t,r,"s"]}function Lc(e,t,r){return r==null&&(r=b(12)),pt(t,r),r.write_shift(4,t.v),r}function Bc(e){var t=mt(e),r=e.read_shift(4);return[t,r,"s"]}function Mc(e,t,r){return r==null&&(r=b(8)),vt(t,r),r.write_shift(4,t.v),r}function bc(e){var t=Nr(e),r=It(e);return[t,r,"n"]}function Uc(e,t,r){return r==null&&(r=b(16)),pt(t,r),ht(e.v,r),r}function Wc(e){var t=mt(e),r=It(e);return[t,r,"n"]}function Vc(e,t,r){return r==null&&(r=b(12)),vt(t,r),ht(e.v,r),r}function Hc(e){var t=Nr(e),r=qa(e);return[t,r,"n"]}function Gc(e,t,r){return r==null&&(r=b(12)),pt(t,r),Ja(e.v,r),r}function Xc(e){var t=mt(e),r=qa(e);return[t,r,"n"]}function zc(e,t,r){return r==null&&(r=b(8)),vt(t,r),Ja(e.v,r),r}function $c(e){var t=Nr(e),r=p0(e);return[t,r,"is"]}function Kc(e){var t=Nr(e),r=xr(e);return[t,r,"str"]}function Yc(e,t,r){return r==null&&(r=b(12+4*e.v.length)),pt(t,r),er(e.v,r),r.length>r.l?r.slice(0,r.l):r}function jc(e){var t=mt(e),r=xr(e);return[t,r,"str"]}function qc(e,t,r){return r==null&&(r=b(8+4*e.v.length)),vt(t,r),er(e.v,r),r.length>r.l?r.slice(0,r.l):r}function Jc(e,t,r){var n=e.l+t,a=Nr(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"b"];if(r.cellFormula){e.l+=2;var f=Mn(e,n-e.l,r);s[3]=Ot(f,null,a,r.supbooks,r)}else e.l=n;return s}function Zc(e,t,r){var n=e.l+t,a=Nr(e);a.r=r["!row"];var i=e.read_shift(1),s=[a,i,"e"];if(r.cellFormula){e.l+=2;var f=Mn(e,n-e.l,r);s[3]=Ot(f,null,a,r.supbooks,r)}else e.l=n;return s}function Qc(e,t,r){var n=e.l+t,a=Nr(e);a.r=r["!row"];var i=It(e),s=[a,i,"n"];if(r.cellFormula){e.l+=2;var f=Mn(e,n-e.l,r);s[3]=Ot(f,null,a,r.supbooks,r)}else e.l=n;return s}function e1(e,t,r){var n=e.l+t,a=Nr(e);a.r=r["!row"];var i=xr(e),s=[a,i,"str"];if(r.cellFormula){e.l+=2;var f=Mn(e,n-e.l,r);s[3]=Ot(f,null,a,r.supbooks,r)}else e.l=n;return s}var r1=gt,t1=Rt;function n1(e,t){return t==null&&(t=b(4)),t.write_shift(4,e),t}function a1(e,t){var r=e.l+t,n=gt(e),a=m0(e),i=xr(e),s=xr(e),f=xr(e);e.l=r;var o={rfx:n,relId:a,loc:i,display:f};return s&&(o.Tooltip=s),o}function i1(e,t){var r=b(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Rt({s:Qe(e[0]),e:Qe(e[0])},r),v0("rId"+t,r);var n=e[1].Target.indexOf("#"),a=n==-1?"":e[1].Target.slice(n+1);return er(a||"",r),er(e[1].Tooltip||"",r),er("",r),r.slice(0,r.l)}function s1(){}function f1(e,t,r){var n=e.l+t,a=Za(e),i=e.read_shift(1),s=[a];if(s[2]=i,r.cellFormula){var f=ju(e,n-e.l,r);s[1]=f}else e.l=n;return s}function l1(e,t,r){var n=e.l+t,a=gt(e),i=[a];if(r.cellFormula){var s=Ju(e,n-e.l,r);i[1]=s,e.l=n}else e.l=n;return i}function o1(e,t,r){r==null&&(r=b(18));var n=bn(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,(n.width||10)*256),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),typeof n.width=="number"&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}var Li=["left","right","top","bottom","header","footer"];function u1(e){var t={};return Li.forEach(function(r){t[r]=It(e)}),t}function c1(e,t){return t==null&&(t=b(6*8)),ki(e),Li.forEach(function(r){ht(e[r],t)}),t}function h1(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function x1(e,t,r){r==null&&(r=b(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}function d1(e){var t=b(24);return t.write_shift(4,4),t.write_shift(4,1),Rt(e,t),t}function p1(e,t){return t==null&&(t=b(16*4+2)),t.write_shift(2,e.password?mi(e.password):0),t.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(r){r[1]?t.write_shift(4,e[r[0]]!=null&&!e[r[0]]?1:0):t.write_shift(4,e[r[0]]!=null&&e[r[0]]?0:1)}),t}function m1(){}function v1(){}function g1(e,t,r,n,a,i,s){if(t.v===void 0)return!1;var f="";switch(t.t){case"b":f=t.v?"1":"0";break;case"d":t=Tr(t),t.z=t.z||Ve[14],t.v=Er(vr(t.v)),t.t="n";break;case"n":case"e":f=""+t.v;break;default:f=t.v;break}var o={r,c:n};switch(o.s=nt(a.cellXfs,t,a),t.l&&i["!links"].push([Ie(o),t.l]),t.c&&i["!comments"].push([Ie(o),t.c]),t.t){case"s":case"str":return a.bookSST?(f=A0(a.Strings,t.v,a.revStrings),o.t="s",o.v=f,s?G(e,18,Mc(t,o)):G(e,7,Lc(t,o))):(o.t="str",s?G(e,17,qc(t,o)):G(e,6,Yc(t,o))),!0;case"n":return t.v==(t.v|0)&&t.v>-1e3&&t.v<1e3?s?G(e,13,zc(t,o)):G(e,2,Gc(t,o)):s?G(e,16,Vc(t,o)):G(e,5,Uc(t,o)),!0;case"b":return o.t="b",s?G(e,15,Dc(t,o)):G(e,4,yc(t,o)),!0;case"e":return o.t="e",s?G(e,14,kc(t,o)):G(e,3,Rc(t,o)),!0}return s?G(e,12,Fc(t,o)):G(e,1,Ac(t,o)),!0}function _1(e,t,r,n){var a=Le(t["!ref"]||"A1"),i,s="",f=[];G(e,145);var o=Array.isArray(t),l=a.e.r;t["!rows"]&&(l=Math.max(a.e.r,t["!rows"].length-1));for(var u=a.s.r;u<=l;++u){s=sr(u),mc(e,t,a,u);var x=!1;if(u<=a.e.r)for(var d=a.s.c;d<=a.e.c;++d){u===a.s.r&&(f[d]=hr(d)),i=f[d]+s;var p=o?(t[u]||[])[d]:t[i];if(!p){x=!1;continue}x=g1(e,p,u,d,n,t,x)}}G(e,146)}function E1(e,t){!t||!t["!merges"]||(G(e,177,n1(t["!merges"].length)),t["!merges"].forEach(function(r){G(e,176,t1(r))}),G(e,178))}function T1(e,t){!t||!t["!cols"]||(G(e,390),t["!cols"].forEach(function(r,n){r&&G(e,60,o1(n,r))}),G(e,391))}function w1(e,t){!t||!t["!ref"]||(G(e,648),G(e,649,d1(Le(t["!ref"]))),G(e,650))}function A1(e,t,r){t["!links"].forEach(function(n){if(!!n[1].Target){var a=Ne(r,-1,n[1].Target.replace(/#.*$/,""),Fe.HLINK);G(e,494,i1(n,a))}}),delete t["!links"]}function S1(e,t,r,n){if(t["!comments"].length>0){var a=Ne(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",Fe.VML);G(e,551,v0("rId"+a)),t["!legacy"]=a}}function F1(e,t,r,n){if(!!t["!autofilter"]){var a=t["!autofilter"],i=typeof a.ref=="string"?a.ref:Ke(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,f=Cr(i);f.s.r==f.e.r&&(f.e.r=Cr(t["!ref"]).e.r,i=Ke(f));for(var o=0;o<s.length;++o){var l=s[o];if(l.Name=="_xlnm._FilterDatabase"&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+i;break}}o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),G(e,161,Rt(Le(i))),G(e,162)}}function C1(e,t,r){G(e,133),G(e,137,x1(t,r)),G(e,138),G(e,134)}function y1(e,t){!t["!protect"]||G(e,535,p1(t["!protect"]))}function O1(e,t,r,n){var a=_r(),i=r.SheetNames[e],s=r.Sheets[i]||{},f=i;try{r&&r.Workbook&&(f=r.Workbook.Sheets[e].CodeName||f)}catch{}var o=Le(s["!ref"]||"A1");if(o.e.c>16383||o.e.r>1048575){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");o.e.c=Math.min(o.e.c,16383),o.e.r=Math.min(o.e.c,1048575)}return s["!links"]=[],s["!comments"]=[],G(a,129),(r.vbaraw||s["!outline"])&&G(a,147,Tc(f,s["!outline"])),G(a,148,gc(o)),C1(a,s,r.Workbook),T1(a,s),_1(a,s,e,t),y1(a,s),F1(a,s,r,e),E1(a,s),A1(a,s,n),s["!margins"]&&G(a,476,c1(s["!margins"])),(!t||t.ignoreEC||t.ignoreEC==null)&&w1(a,s),S1(a,s,e,n),G(a,130),a.end()}function D1(e,t){e.l+=10;var r=xr(e);return{name:r}}var N1=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];function R1(e){return!e.Workbook||!e.Workbook.WBProps?"false":ff(e.Workbook.WBProps.date1904)?"true":"false"}var I1="][*?/\\".split("");function Bi(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return I1.forEach(function(n){if(e.indexOf(n)!=-1){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function k1(e,t,r){e.forEach(function(n,a){Bi(n);for(var i=0;i<a;++i)if(n==e[i])throw new Error("Duplicate Sheet Name: "+n);if(r){var s=t&&t[a]&&t[a].CodeName||n;if(s.charCodeAt(0)==95&&s.length>22)throw new Error("Bad Code Name: Worksheet"+s)}})}function P1(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];k1(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)tc(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}function Mi(e){var t=[Ye];t[t.length]=Z("workbook",null,{xmlns:Dt[0],"xmlns:r":Ze.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(N1.forEach(function(f){e.Workbook.WBProps[f[0]]!=null&&e.Workbook.WBProps[f[0]]!=f[1]&&(n[f[0]]=e.Workbook.WBProps[f[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Z("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&!!a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&!(!a[i]||!a[i].Hidden);++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var s={name:Re(e.SheetNames[i].slice(0,31))};if(s.sheetId=""+(i+1),s["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:s.state="hidden";break;case 2:s.state="veryHidden";break}t[t.length]=Z("sheet",null,s)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(f){var o={name:f.Name};f.Comment&&(o.comment=f.Comment),f.Sheet!=null&&(o.localSheetId=""+f.Sheet),f.Hidden&&(o.hidden="1"),f.Ref&&(t[t.length]=Z("definedName",Re(f.Ref),o))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function L1(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=n0(e),r.name=xr(e),r}function B1(e,t){return t||(t=b(127)),t.write_shift(4,e.Hidden),t.write_shift(4,e.iTabID),v0(e.strRelID,t),er(e.name.slice(0,31),t),t.length>t.l?t.slice(0,t.l):t}function M1(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?xr(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function b1(e,t){t||(t=b(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),ja(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}function U1(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Nf(e),s=qu(e,0,r),f=m0(e);e.l=n;var o={Name:i,Ptg:s};return a<268435455&&(o.Sheet=a),f&&(o.Comment=f),o}function W1(e,t){G(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n=t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,a={Hidden:n,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]};G(e,156,B1(a))}G(e,144)}function V1(e,t){t||(t=b(127));for(var r=0;r!=4;++r)t.write_shift(4,0);return er("SheetJS",t),er(vn.version,t),er(vn.version,t),er("7262",t),t.length>t.l?t.slice(0,t.l):t}function H1(e,t){t||(t=b(29)),t.write_shift(-4,0),t.write_shift(-4,460),t.write_shift(4,28800),t.write_shift(4,17600),t.write_shift(4,500),t.write_shift(4,e),t.write_shift(4,e);var r=120;return t.write_shift(1,r),t.length>t.l?t.slice(0,t.l):t}function G1(e,t){if(!(!t.Workbook||!t.Workbook.Sheets)){for(var r=t.Workbook.Sheets,n=0,a=-1,i=-1;n<r.length;++n)!r[n]||!r[n].Hidden&&a==-1?a=n:r[n].Hidden==1&&i==-1&&(i=n);i>a||(G(e,135),G(e,158,H1(a)),G(e,136))}}function X1(e,t){var r=_r();return G(r,131),G(r,128,V1()),G(r,153,b1(e.Workbook&&e.Workbook.WBProps||null)),G1(r,e),W1(r,e),G(r,132),r.end()}function z1(e,t,r){return(t.slice(-4)===".bin"?X1:Mi)(e)}function $1(e,t,r,n,a){return(t.slice(-4)===".bin"?O1:Pi)(e,r,n,a)}function K1(e,t,r){return(t.slice(-4)===".bin"?uo:_i)(e,r)}function Y1(e,t,r){return(t.slice(-4)===".bin"?Ml:pi)(e,r)}function j1(e,t,r){return(t.slice(-4)===".bin"?yo:Si)(e)}function q1(e){return(e.slice(-4)===".bin"?_o:wi)()}function J1(e,t){var r=[];return e.Props&&r.push($f(e.Props,t)),e.Custprops&&r.push(Kf(e.Props,e.Custprops)),r.join("")}function Z1(){return""}function Q1(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach(function(n,a){var i=[];i.push(Z("NumberFormat",null,{"ss:Format":Re(Ve[n.numFmtId])}));var s={"ss:ID":"s"+(21+a)};r.push(Z("Style",i.join(""),s))}),Z("Styles",r.join(""))}function bi(e){return Z("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+T0(e.Ref,{r:0,c:0})})}function eh(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];a.Sheet==null&&(a.Name.match(/^_xlfn\./)||r.push(bi(a)))}return Z("Names",r.join(""))}function rh(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],s=0;s<a.length;++s){var f=a[s];f.Sheet==r&&(f.Name.match(/^_xlfn\./)||i.push(bi(f)))}return i.join("")}function th(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Z("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Z("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Z("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Z("Visible",n.Workbook.Sheets[r].Hidden==1?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&!(n.Workbook.Sheets[i]&&!n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(ir("ProtectContents","True")),e["!protect"].objects&&a.push(ir("ProtectObjects","True")),e["!protect"].scenarios&&a.push(ir("ProtectScenarios","True")),e["!protect"].selectLockedCells!=null&&!e["!protect"].selectLockedCells?a.push(ir("EnableSelection","NoSelection")):e["!protect"].selectUnlockedCells!=null&&!e["!protect"].selectUnlockedCells&&a.push(ir("EnableSelection","UnlockedCells")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(s){e["!protect"][s[0]]&&a.push("<"+s[1]+"/>")})),a.length==0?"":Z("WorksheetOptions",a.join(""),{xmlns:Sr.x})}function nh(e){return e.map(function(t){var r=sf(t.t||""),n=Z("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return Z("Comment",n,{"ss:Author":t.a})}).join("")}function ah(e,t,r,n,a,i,s){if(!e||e.v==null&&e.f==null)return"";var f={};if(e.f&&(f["ss:Formula"]="="+Re(T0(e.f,s))),e.F&&e.F.slice(0,t.length)==t){var o=Qe(e.F.slice(t.length+1));f["ss:ArrayRange"]="RC:R"+(o.r==s.r?"":"["+(o.r-s.r)+"]")+"C"+(o.c==s.c?"":"["+(o.c-s.c)+"]")}if(e.l&&e.l.Target&&(f["ss:HRef"]=Re(e.l.Target),e.l.Tooltip&&(f["x:HRefScreenTip"]=Re(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],u=0;u!=l.length;++u)l[u].s.c!=s.c||l[u].s.r!=s.r||(l[u].e.c>l[u].s.c&&(f["ss:MergeAcross"]=l[u].e.c-l[u].s.c),l[u].e.r>l[u].s.r&&(f["ss:MergeDown"]=l[u].e.r-l[u].s.r));var x="",d="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":x="Number",d=String(e.v);break;case"b":x="Boolean",d=e.v?"1":"0";break;case"e":x="Error",d=Qt[e.v];break;case"d":x="DateTime",d=new Date(e.v).toISOString(),e.z==null&&(e.z=e.z||Ve[14]);break;case"s":x="String",d=af(e.v||"");break}var p=nt(n.cellXfs,e,n);f["ss:StyleID"]="s"+(21+p),f["ss:Index"]=s.c+1;var E=e.v!=null?d:"",h=e.t=="z"?"":'<Data ss:Type="'+x+'">'+E+"</Data>";return(e.c||[]).length>0&&(h+=nh(e.c)),Z("Cell",h,f)}function ih(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=gi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function sh(e,t,r,n){if(!e["!ref"])return"";var a=Le(e["!ref"]),i=e["!merges"]||[],s=0,f=[];e["!cols"]&&e["!cols"].forEach(function(g,y){_0(g);var F=!!g.width,D=bn(y,g),M={"ss:Index":y+1};F&&(M["ss:Width"]=Cn(D.width)),g.hidden&&(M["ss:Hidden"]="1"),f.push(Z("Column",null,M))});for(var o=Array.isArray(e),l=a.s.r;l<=a.e.r;++l){for(var u=[ih(l,(e["!rows"]||[])[l])],x=a.s.c;x<=a.e.c;++x){var d=!1;for(s=0;s!=i.length;++s)if(!(i[s].s.c>x)&&!(i[s].s.r>l)&&!(i[s].e.c<x)&&!(i[s].e.r<l)){(i[s].s.c!=x||i[s].s.r!=l)&&(d=!0);break}if(!d){var p={r:l,c:x},E=Ie(p),h=o?(e[l]||[])[x]:e[E];u.push(ah(h,E,e,t,r,n,p))}}u.push("</Row>"),u.length>2&&f.push(u.join(""))}return f.join("")}function fh(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],s=i?rh(i,t,e,r):"";return s.length>0&&n.push("<Names>"+s+"</Names>"),s=i?sh(i,t,e,r):"",s.length>0&&n.push("<Table>"+s+"</Table>"),n.push(th(i,t,e,r)),n.join("")}function lh(e,t){t||(t={}),e.SSF||(e.SSF=Tr(Ve)),e.SSF&&(kn(),In(e.SSF),t.revssf=Pn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],nt(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(J1(e,t)),r.push(Z1()),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Z("Worksheet",fh(n,t,e),{"ss:Name":Re(e.SheetNames[n])}));return r[2]=Q1(e,t),r[3]=eh(e),Ye+Z("Workbook",r.join(""),{xmlns:Sr.ss,"xmlns:o":Sr.o,"xmlns:x":Sr.x,"xmlns:ss":Sr.ss,"xmlns:dt":Sr.dt,"xmlns:html":Sr.html})}var Zn={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function oh(e,t){var r=[],n=[],a=[],i=0,s,f=b0(J0,"n"),o=b0(Z0,"n");if(e.Props)for(s=fr(e.Props),i=0;i<s.length;++i)(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Props[s[i]]]);if(e.Custprops)for(s=fr(e.Custprops),i=0;i<s.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},s[i])||(Object.prototype.hasOwnProperty.call(f,s[i])?r:Object.prototype.hasOwnProperty.call(o,s[i])?n:a).push([s[i],e.Custprops[s[i]]]);var l=[];for(i=0;i<a.length;++i)li.indexOf(a[i][0])>-1||ii.indexOf(a[i][0])>-1||a[i][1]!=null&&l.push(a[i]);n.length&&ke.utils.cfb_add(t,"/SummaryInformation",na(n,Zn.SI,o,Z0)),(r.length||l.length)&&ke.utils.cfb_add(t,"/DocumentSummaryInformation",na(r,Zn.DSI,f,J0,l.length?l:null,Zn.UDI))}function uh(e,t){var r=t||{},n=ke.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return ke.utils.cfb_add(n,a,Ui(e,r)),r.biff==8&&(e.Props||e.Custprops)&&oh(e,n),r.biff==8&&e.vbaraw&&Oo(n,ke.read(e.vbaraw,{type:typeof e.vbaraw=="string"?"binary":"buffer"})),n}var ch={0:{f:dc},1:{f:wc},2:{f:Hc},3:{f:Nc},4:{f:Cc},5:{f:bc},6:{f:Kc},7:{f:Pc},8:{f:e1},9:{f:Qc},10:{f:Jc},11:{f:Zc},12:{f:Sc},13:{f:Xc},14:{f:Ic},15:{f:Oc},16:{f:Wc},17:{f:jc},18:{f:Bc},19:{f:p0},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:U1},40:{},42:{},43:{f:$l},44:{f:Xl},45:{f:jl},46:{f:Jl},47:{f:ql},48:{},49:{f:Af},50:{},51:{f:xo},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Sl},62:{f:$c},63:{f:Eo},64:{f:m1},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Ur,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:h1},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Ec},148:{f:vc,p:16},151:{f:s1},152:{},153:{f:M1},154:{},155:{},156:{f:L1},157:{},158:{},159:{T:1,f:Pl},160:{T:-1},161:{T:1,f:gt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:r1},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:co},336:{T:-1},337:{f:vo,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:n0},357:{},358:{},359:{},360:{T:1},361:{},362:{f:gl},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:f1},427:{f:l1},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:u1},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:_c},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:a1},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:n0},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Fo},633:{T:1},634:{T:-1},635:{T:1,f:Ao},636:{T:-1},637:{f:yf},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:D1},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:v1},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function Q(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,a),s.write_shift(2,i),i>0&&h0(r)&&e.push(r)}}function hh(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return Q(e,t,r,a);var i=t;if(!isNaN(i)){for(var s=r.parts||[],f=0,o=0,l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;var u=e.next(4);for(u.write_shift(2,i),u.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for(u=e.next(4),u.write_shift(2,60),l=0;l+(s[f]||8224)<=8224;)l+=s[f]||8224,f++;u.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}function rn(e,t,r){return e||(e=b(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function xh(e,t,r,n){var a=b(9);return rn(a,e,t),ui(r,n||"b",a),a}function dh(e,t,r){var n=b(8+2*r.length);return rn(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}function ph(e,t,r,n){if(t.v!=null)switch(t.t){case"d":case"n":var a=t.t=="d"?Er(vr(t.v)):t.v;a==(a|0)&&a>=0&&a<65536?Q(e,2,Ol(r,n,a)):Q(e,3,yl(r,n,a));return;case"b":case"e":Q(e,5,xh(r,n,t.v,t.t));return;case"s":case"str":Q(e,4,dh(r,n,(t.v||"").slice(0,255)));return}Q(e,1,rn(null,r,n))}function mh(e,t,r,n){var a=Array.isArray(t),i=Le(t["!ref"]||"A1"),s,f="",o=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),s=Ke(i)}for(var l=i.s.r;l<=i.e.r;++l){f=sr(l);for(var u=i.s.c;u<=i.e.c;++u){l===i.s.r&&(o[u]=hr(u)),s=o[u]+f;var x=a?(t[l]||[])[u]:t[s];!x||ph(e,x,l,u)}}}function vh(e,t){for(var r=t||{},n=_r(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(a==0&&!!r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return Q(n,r.biff==4?1033:r.biff==3?521:9,g0(e,16,r)),mh(n,e.Sheets[e.SheetNames[a]],a,r),Q(n,10),n.end()}function gh(e,t,r){Q(e,49,ul({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function _h(e,t,r){!t||[[5,8],[23,26],[41,44],[50,392]].forEach(function(n){for(var a=n[0];a<=n[1];++a)t[a]!=null&&Q(e,1054,xl(a,t[a],r))})}function Eh(e,t){var r=b(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),Q(e,2151,r),r=b(39),r.write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),xi(Le(t["!ref"]||"A1"),r),r.write_shift(4,4),Q(e,2152,r)}function Th(e,t){for(var r=0;r<16;++r)Q(e,224,ia({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(n){Q(e,224,ia(n,0,t))})}function wh(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];Q(e,440,Tl(n)),n[1].Tooltip&&Q(e,2048,wl(n))}delete t["!links"]}function Ah(e,t){if(!!t){var r=0;t.forEach(function(n,a){++r<=256&&n&&Q(e,125,Fl(bn(a,n),a))})}}function Sh(e,t,r,n,a){var i=16+nt(a.cellXfs,t,a);if(t.v==null&&!t.bf){Q(e,513,xt(r,n,i));return}if(t.bf)Q(e,6,Yu(t,r,n,a,i));else switch(t.t){case"d":case"n":var s=t.t=="d"?Er(vr(t.v)):t.v;Q(e,515,vl(r,n,s,i));break;case"b":case"e":Q(e,517,ml(r,n,t.v,i,a,t.t));break;case"s":case"str":if(a.bookSST){var f=A0(a.Strings,t.v,a.revStrings);Q(e,253,cl(r,n,f,i))}else Q(e,516,hl(r,n,(t.v||"").slice(0,255),i,a));break;default:Q(e,513,xt(r,n,i))}}function Fh(e,t,r){var n=_r(),a=r.SheetNames[e],i=r.Sheets[a]||{},s=(r||{}).Workbook||{},f=(s.Sheets||[])[e]||{},o=Array.isArray(i),l=t.biff==8,u,x="",d=[],p=Le(i["!ref"]||"A1"),E=l?65536:16384;if(p.e.c>255||p.e.r>=E){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");p.e.c=Math.min(p.e.c,255),p.e.r=Math.min(p.e.c,E-1)}Q(n,2057,g0(r,16,t)),Q(n,13,Dr(1)),Q(n,12,Dr(100)),Q(n,15,mr(!0)),Q(n,17,mr(!1)),Q(n,16,ht(.001)),Q(n,95,mr(!0)),Q(n,42,mr(!1)),Q(n,43,mr(!1)),Q(n,130,Dr(1)),Q(n,128,pl([0,0])),Q(n,131,mr(!1)),Q(n,132,mr(!1)),l&&Ah(n,i["!cols"]),Q(n,512,dl(p,t)),l&&(i["!links"]=[]);for(var h=p.s.r;h<=p.e.r;++h){x=sr(h);for(var g=p.s.c;g<=p.e.c;++g){h===p.s.r&&(d[g]=hr(g)),u=d[g]+x;var y=o?(i[h]||[])[g]:i[u];!y||(Sh(n,y,h,g,t),l&&y.l&&i["!links"].push([u,y.l]))}}var F=f.CodeName||f.name||a;return l&&Q(n,574,ol((s.Views||[])[0])),l&&(i["!merges"]||[]).length&&Q(n,229,El(i["!merges"])),l&&wh(n,i),Q(n,442,hi(F)),l&&Eh(n,i),Q(n,10),n.end()}function Ch(e,t,r){var n=_r(),a=(e||{}).Workbook||{},i=a.Sheets||[],s=a.WBProps||{},f=r.biff==8,o=r.biff==5;if(Q(n,2057,g0(e,5,r)),r.bookType=="xla"&&Q(n,135),Q(n,225,f?Dr(1200):null),Q(n,193,qf(2)),o&&Q(n,191),o&&Q(n,192),Q(n,226),Q(n,92,il("SheetJS",r)),Q(n,66,Dr(f?1200:1252)),f&&Q(n,353,Dr(0)),f&&Q(n,448),Q(n,317,Cl(e.SheetNames.length)),f&&e.vbaraw&&Q(n,211),f&&e.vbaraw){var l=s.CodeName||"ThisWorkbook";Q(n,442,hi(l))}Q(n,156,Dr(17)),Q(n,25,mr(!1)),Q(n,18,mr(!1)),Q(n,19,Dr(0)),f&&Q(n,431,mr(!1)),f&&Q(n,444,Dr(0)),Q(n,61,ll()),Q(n,64,mr(!1)),Q(n,141,Dr(0)),Q(n,34,mr(R1(e)=="true")),Q(n,14,mr(!0)),f&&Q(n,439,mr(!1)),Q(n,218,Dr(0)),gh(n,e,r),_h(n,e.SSF,r),Th(n,r),f&&Q(n,352,mr(!1));var u=n.end(),x=_r();f&&Q(x,140,Al()),f&&r.Strings&&hh(x,252,fl(r.Strings)),Q(x,10);var d=x.end(),p=_r(),E=0,h=0;for(h=0;h<e.SheetNames.length;++h)E+=(f?12:11)+(f?2:1)*e.SheetNames[h].length;var g=u.length+E+d.length;for(h=0;h<e.SheetNames.length;++h){var y=i[h]||{};Q(p,133,sl({pos:g,hs:y.Hidden||0,dt:0,name:e.SheetNames[h]},r)),g+=t[h].length}var F=p.end();if(E!=F.length)throw new Error("BS8 "+E+" != "+F.length);var D=[];return u.length&&D.push(u),F.length&&D.push(F),d.length&&D.push(d),ar(D)}function yh(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=Tr(Ve)),e&&e.SSF&&(kn(),In(e.SSF),r.revssf=Pn(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,S0(r),r.cellXfs=[],nt(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=Fh(a,r,e);return n.unshift(Ch(e,n,r)),ar(n)}function Ui(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(!(!n||!n["!ref"])){var a=Cr(n["!ref"]);a.e.c>255&&typeof console!="undefined"&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}}var i=t||{};switch(i.biff||2){case 8:case 5:return yh(e,t);case 4:case 3:case 2:return vh(e,t)}throw new Error("invalid type "+i.bookType+" for BIFF")}function Oh(e,t,r,n){for(var a=e["!merges"]||[],i=[],s=t.s.c;s<=t.e.c;++s){for(var f=0,o=0,l=0;l<a.length;++l)if(!(a[l].s.r>r||a[l].s.c>s)&&!(a[l].e.r<r||a[l].e.c<s)){if(a[l].s.r<r||a[l].s.c<s){f=-1;break}f=a[l].e.r-a[l].s.r+1,o=a[l].e.c-a[l].s.c+1;break}if(!(f<0)){var u=Ie({r,c:s}),x=n.dense?(e[r]||[])[s]:e[u],d=x&&x.v!=null&&(x.h||nf(x.w||($r(x),x.w)||""))||"",p={};f>1&&(p.rowspan=f),o>1&&(p.colspan=o),n.editable?d='<span contenteditable="true">'+d+"</span>":x&&(p["data-t"]=x&&x.t||"z",x.v!=null&&(p["data-v"]=x.v),x.z!=null&&(p["data-z"]=x.z),x.l&&(x.l.Target||"#").charAt(0)!="#"&&(d='<a href="'+x.l.Target+'">'+d+"</a>")),p.id=(n.id||"sjs")+"-"+u,i.push(Z("td",d,p))}}var E="<tr>";return E+i.join("")+"</tr>"}var Dh='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Nh="</body></html>";function Rh(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Wi(e,t){var r=t||{},n=r.header!=null?r.header:Dh,a=r.footer!=null?r.footer:Nh,i=[n],s=Cr(e["!ref"]);r.dense=Array.isArray(e),i.push(Rh(e,s,r));for(var f=s.s.r;f<=s.e.r;++f)i.push(Oh(e,s,f,r));return i.push("</table>"+a),i.join("")}function Vi(e,t,r){var n=r||{},a=0,i=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var s=typeof n.origin=="string"?Qe(n.origin):n.origin;a=s.r,i=s.c}var f=t.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,f.length),l={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var u=Cr(e["!ref"]);l.s.r=Math.min(l.s.r,u.s.r),l.s.c=Math.min(l.s.c,u.s.c),l.e.r=Math.max(l.e.r,u.e.r),l.e.c=Math.max(l.e.c,u.e.c),a==-1&&(l.e.r=a=u.e.r+1)}var x=[],d=0,p=e["!rows"]||(e["!rows"]=[]),E=0,h=0,g=0,y=0,F=0,D=0;for(e["!cols"]||(e["!cols"]=[]);E<f.length&&h<o;++E){var M=f[E];if(ha(M)){if(n.display)continue;p[h]={hidden:!0}}var q=M.children;for(g=y=0;g<q.length;++g){var ie=q[g];if(!(n.display&&ha(ie))){var N=ie.hasAttribute("data-v")?ie.getAttribute("data-v"):ie.hasAttribute("v")?ie.getAttribute("v"):lf(ie.innerHTML),V=ie.getAttribute("data-z")||ie.getAttribute("z");for(d=0;d<x.length;++d){var L=x[d];L.s.c==y+i&&L.s.r<h+a&&h+a<=L.e.r&&(y=L.e.c+1-i,d=-1)}D=+ie.getAttribute("colspan")||1,((F=+ie.getAttribute("rowspan")||1)>1||D>1)&&x.push({s:{r:h+a,c:y+i},e:{r:h+a+(F||1)-1,c:y+i+(D||1)-1}});var X={t:"s",v:N},K=ie.getAttribute("data-t")||ie.getAttribute("t")||"";N!=null&&(N.length==0?X.t=K||"z":n.raw||N.trim().length==0||K=="s"||(N==="TRUE"?X={t:"b",v:!0}:N==="FALSE"?X={t:"b",v:!1}:isNaN(Gr(N))?isNaN($t(N).getDate())||(X={t:"d",v:vr(N)},n.cellDates||(X={t:"n",v:Er(X.v)}),X.z=n.dateNF||Ve[14]):X={t:"n",v:Gr(N)})),X.z===void 0&&V!=null&&(X.z=V);var Y="",te=ie.getElementsByTagName("A");if(te&&te.length)for(var we=0;we<te.length&&!(te[we].hasAttribute("href")&&(Y=te[we].getAttribute("href"),Y.charAt(0)!="#"));++we);Y&&Y.charAt(0)!="#"&&(X.l={Target:Y}),n.dense?(e[h+a]||(e[h+a]=[]),e[h+a][y+i]=X):e[Ie({c:y+i,r:h+a})]=X,l.e.c<y+i&&(l.e.c=y+i),y+=D}}++h}return x.length&&(e["!merges"]=(e["!merges"]||[]).concat(x)),l.e.r=Math.max(l.e.r,h-1+a),e["!ref"]=Ke(l),h>=o&&(e["!fullref"]=Ke((l.e.r=f.length-E+h-1+a,l))),e}function Hi(e,t){var r=t||{},n=r.dense?[]:{};return Vi(n,e,t)}function Ih(e,t){return dt(Hi(e,t),t)}function ha(e){var t="",r=kh(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function kh(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}var Ph=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Kt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Ye+t}}(),xa=function(){var e=function(i){return Re(i).replace(/  +/g,function(s){return'<text:s text:c="'+s.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t=`          <table:table-cell />
`,r=`          <table:covered-table-cell/>
`,n=function(i,s,f){var o=[];o.push('      <table:table table:name="'+Re(s.SheetNames[f])+`" table:style-name="ta1">
`);var l=0,u=0,x=Cr(i["!ref"]||"A1"),d=i["!merges"]||[],p=0,E=Array.isArray(i);if(i["!cols"])for(u=0;u<=x.e.c;++u)o.push("        <table:table-column"+(i["!cols"][u]?' table:style-name="co'+i["!cols"][u].ods+'"':"")+`></table:table-column>
`);var h="",g=i["!rows"]||[];for(l=0;l<x.s.r;++l)h=g[l]?' table:style-name="ro'+g[l].ods+'"':"",o.push("        <table:table-row"+h+`></table:table-row>
`);for(;l<=x.e.r;++l){for(h=g[l]?' table:style-name="ro'+g[l].ods+'"':"",o.push("        <table:table-row"+h+`>
`),u=0;u<x.s.c;++u)o.push(t);for(;u<=x.e.c;++u){var y=!1,F={},D="";for(p=0;p!=d.length;++p)if(!(d[p].s.c>u)&&!(d[p].s.r>l)&&!(d[p].e.c<u)&&!(d[p].e.r<l)){(d[p].s.c!=u||d[p].s.r!=l)&&(y=!0),F["table:number-columns-spanned"]=d[p].e.c-d[p].s.c+1,F["table:number-rows-spanned"]=d[p].e.r-d[p].s.r+1;break}if(y){o.push(r);continue}var M=Ie({r:l,c:u}),q=E?(i[l]||[])[u]:i[M];if(q&&q.f&&(F["table:formula"]=Re(ec(q.f)),q.F&&q.F.slice(0,M.length)==M)){var ie=Cr(q.F);F["table:number-matrix-columns-spanned"]=ie.e.c-ie.s.c+1,F["table:number-matrix-rows-spanned"]=ie.e.r-ie.s.r+1}if(!q){o.push(t);continue}switch(q.t){case"b":D=q.v?"TRUE":"FALSE",F["office:value-type"]="boolean",F["office:boolean-value"]=q.v?"true":"false";break;case"n":D=q.w||String(q.v||0),F["office:value-type"]="float",F["office:value"]=q.v||0;break;case"s":case"str":D=q.v==null?"":q.v,F["office:value-type"]="string";break;case"d":D=q.w||vr(q.v).toISOString(),F["office:value-type"]="date",F["office:date-value"]=vr(q.v).toISOString(),F["table:style-name"]="ce1";break;default:o.push(t);continue}var N=e(D);if(q.l&&q.l.Target){var V=q.l.Target;V=V.charAt(0)=="#"?"#"+rc(V.slice(1)):V,V.charAt(0)!="#"&&!V.match(/^\w+:/)&&(V="../"+V),N=Z("text:a",N,{"xlink:href":V.replace(/&/g,"&amp;")})}o.push("          "+Z("table:table-cell",Z("text:p",N,{}),F)+`
`)}o.push(`        </table:table-row>
`)}return o.push(`      </table:table>
`),o.join("")},a=function(i,s){i.push(` <office:automatic-styles>
`),i.push(`  <number:date-style style:name="N37" number:automatic-order="true">
`),i.push(`   <number:month number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:day number:style="long"/>
`),i.push(`   <number:text>/</number:text>
`),i.push(`   <number:year/>
`),i.push(`  </number:date-style>
`);var f=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(!!l&&l["!cols"]){for(var u=0;u<l["!cols"].length;++u)if(l["!cols"][u]){var x=l["!cols"][u];if(x.width==null&&x.wpx==null&&x.wch==null)continue;_0(x),x.ods=f;var d=l["!cols"][u].wpx+"px";i.push('  <style:style style:name="co'+f+`" style:family="table-column">
`),i.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+d+`"/>
`),i.push(`  </style:style>
`),++f}}});var o=0;s.SheetNames.map(function(l){return s.Sheets[l]}).forEach(function(l){if(!!l&&l["!rows"]){for(var u=0;u<l["!rows"].length;++u)if(l["!rows"][u]){l["!rows"][u].ods=o;var x=l["!rows"][u].hpx+"px";i.push('  <style:style style:name="ro'+o+`" style:family="table-row">
`),i.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+x+`"/>
`),i.push(`  </style:style>
`),++o}}}),i.push(`  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">
`),i.push(`   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>
`),i.push(`  </style:style>
`),i.push(`  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>
`),i.push(` </office:automatic-styles>
`)};return function(s,f){var o=[Ye],l=Kt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),u=Kt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});f.bookType=="fods"?(o.push("<office:document"+l+u+`>
`),o.push(ni().replace(/office:document-meta/g,"office:meta"))):o.push("<office:document-content"+l+`>
`),a(o,s),o.push(`  <office:body>
`),o.push(`    <office:spreadsheet>
`);for(var x=0;x!=s.SheetNames.length;++x)o.push(n(s.Sheets[s.SheetNames[x]],s,x));return o.push(`    </office:spreadsheet>
`),o.push(`  </office:body>
`),f.bookType=="fods"?o.push("</office:document>"):o.push("</office:document-content>"),o.join("")}}();function Gi(e,t){if(t.bookType=="fods")return xa(e,t);var r=l0(),n="",a=[],i=[];return n="mimetype",_e(r,n,"application/vnd.oasis.opendocument.spreadsheet"),n="content.xml",_e(r,n,xa(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),n="styles.xml",_e(r,n,Ph(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),n="meta.xml",_e(r,n,Ye+ni()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),n="manifest.rdf",_e(r,n,zf(i)),a.push([n,"application/rdf+xml"]),n="META-INF/manifest.xml",_e(r,n,Gf(a)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Dn(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Lh(e){return typeof TextEncoder!="undefined"?new TextEncoder().encode(e):Pr(Vr(e))}function Bh(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}function tt(e){var t=e.reduce(function(a,i){return a+i.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function Mh(e,t,r){var n=Math.floor(r==0?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(n&127)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=a&255;e[t+15]|=r>=0?0:128}function Yt(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function De(e){var t=new Uint8Array(7);t[0]=e&127;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function yt(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function qe(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=Yt(e,r),i=a&7;a=Math.floor(a/8);var s=0,f;if(a==0)break;switch(i){case 0:{for(var o=r[0];e[r[0]++]>=128;);f=e.slice(o,r[0])}break;case 5:s=4,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 1:s=8,f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 2:s=Yt(e,r),f=e.slice(r[0],r[0]+s),r[0]+=s;break;case 3:case 4:default:throw new Error("PB Type ".concat(i," for Field ").concat(a," at offset ").concat(n))}var l={data:f,type:i};t[a]==null?t[a]=[l]:t[a].push(l)}return t}function tr(e){var t=[];return e.forEach(function(r,n){r.forEach(function(a){!a.data||(t.push(De(n*8+a.type)),a.type==2&&t.push(De(a.data.length)),t.push(a.data))})}),tt(t)}function Ir(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=Yt(e,n),i=qe(e.slice(n[0],n[0]+a));n[0]+=a;var s={id:yt(i[1][0].data),messages:[]};i[2].forEach(function(f){var o=qe(f.data),l=yt(o[3][0].data);s.messages.push({meta:o,data:e.slice(n[0],n[0]+l)}),n[0]+=l}),(t=i[3])!=null&&t[0]&&(s.merge=yt(i[3][0].data)>>>0>0),r.push(s)}return r}function Et(e){var t=[];return e.forEach(function(r){var n=[];n[1]=[{data:De(r.id),type:0}],n[2]=[],r.merge!=null&&(n[3]=[{data:De(+!!r.merge),type:0}]);var a=[];r.messages.forEach(function(s){a.push(s.data),s.meta[3]=[{type:0,data:De(s.data.length)}],n[2].push({data:tr(s.meta),type:2})});var i=tr(n);t.push(De(i.length)),t.push(i),a.forEach(function(s){return t.push(s)})}),tt(t)}function bh(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Yt(t,r),a=[];r[0]<t.length;){var i=t[r[0]]&3;if(i==0){var s=t[r[0]++]>>2;if(s<60)++s;else{var f=s-59;s=t[r[0]],f>1&&(s|=t[r[0]+1]<<8),f>2&&(s|=t[r[0]+2]<<16),f>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=f}a.push(t.slice(r[0],r[0]+s)),r[0]+=s;continue}else{var o=0,l=0;if(i==1?(l=(t[r[0]]>>2&7)+4,o=(t[r[0]++]&224)<<3,o|=t[r[0]++]):(l=(t[r[0]++]>>2)+1,i==2?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[tt(a)],o==0)throw new Error("Invalid offset 0");if(o>a[0].length)throw new Error("Invalid offset beyond length");if(l>=o)for(a.push(a[0].slice(-o)),l-=o;l>=a[a.length-1].length;)a.push(a[a.length-1]),l-=a[a.length-1].length;a.push(a[0].slice(-o,-o+l))}}var u=tt(a);if(u.length!=n)throw new Error("Unexpected length: ".concat(u.length," != ").concat(n));return u}function kr(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(bh(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return tt(t)}function Tt(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=De(n),s=i.length;t.push(i),n<=60?(s++,t.push(new Uint8Array([n-1<<2]))):n<=256?(s+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(s+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(s+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(s+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),s+=n,a[0]=0,a[1]=s&255,a[2]=s>>8&255,a[3]=s>>16&255,r+=n}return tt(t)}function Qn(e,t){var r=new Uint8Array(32),n=Dn(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,Mh(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function e0(e,t){var r=new Uint8Array(32),n=Dn(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(t.indexOf(e.v)==-1)throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Jr(e){var t=qe(e);return Yt(t[1][0].data)}function Uh(e,t,r){var n,a,i,s;if(!((n=e[6])!=null&&n[0])||!((a=e[7])!=null&&a[0]))throw"Mutation only works on post-BNC storages!";var f=((s=(i=e[8])==null?void 0:i[0])==null?void 0:s.data)&&yt(e[8][0].data)>0||!1;if(f)throw"Math only works with normal offsets";for(var o=0,l=Dn(e[7][0].data),u=0,x=[],d=Dn(e[4][0].data),p=0,E=[],h=0;h<t.length;++h){if(t[h]==null){l.setUint16(h*2,65535,!0),d.setUint16(h*2,65535);continue}l.setUint16(h*2,u,!0),d.setUint16(h*2,p,!0);var g,y;switch(typeof t[h]){case"string":g=Qn({t:"s",v:t[h]},r),y=e0({t:"s",v:t[h]},r);break;case"number":g=Qn({t:"n",v:t[h]},r),y=e0({t:"n",v:t[h]},r);break;case"boolean":g=Qn({t:"b",v:t[h]},r),y=e0({t:"b",v:t[h]},r);break;default:throw new Error("Unsupported value "+t[h])}x.push(g),u+=g.length,E.push(y),p+=y.length,++o}for(e[2][0].data=De(o);h<e[7][0].data.length/2;++h)l.setUint16(h*2,65535,!0),d.setUint16(h*2,65535,!0);return e[6][0].data=tt(x),e[3][0].data=tt(E),o}function Wh(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=Cr(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(Ke(n)));var i=Nn(r,{range:n,header:1}),s=["~Sh33tJ5~"];i.forEach(function(B){return B.forEach(function(O){typeof O=="string"&&s.push(O)})});var f={},o=[],l=ke.read(t.numbers,{type:"base64"});l.FileIndex.map(function(B,O){return[B,l.FullPaths[O]]}).forEach(function(B){var O=B[0],C=B[1];if(O.type==2&&!!O.name.match(/\.iwa/)){var H=O.content,ce=kr(H),he=Ir(ce);he.forEach(function(oe){o.push(oe.id),f[oe.id]={deps:[],location:C,type:yt(oe.messages[0].meta[1][0].data)}})}}),o.sort(function(B,O){return B-O});var u=o.filter(function(B){return B>1}).map(function(B){return[B,De(B)]});l.FileIndex.map(function(B,O){return[B,l.FullPaths[O]]}).forEach(function(B){var O=B[0];if(B[1],!!O.name.match(/\.iwa/)){var C=Ir(kr(O.content));C.forEach(function(H){H.messages.forEach(function(ce){u.forEach(function(he){H.messages.some(function(oe){return yt(oe.meta[1][0].data)!=11006&&Bh(oe.data,he[1])})&&f[he[0]].deps.push(H.id)})})})}});for(var x=ke.find(l,f[1].location),d=Ir(kr(x.content)),p,E=0;E<d.length;++E){var h=d[E];h.id==1&&(p=h)}var g=Jr(qe(p.messages[0].data)[1][0].data);for(x=ke.find(l,f[g].location),d=Ir(kr(x.content)),E=0;E<d.length;++E)h=d[E],h.id==g&&(p=h);for(g=Jr(qe(p.messages[0].data)[2][0].data),x=ke.find(l,f[g].location),d=Ir(kr(x.content)),E=0;E<d.length;++E)h=d[E],h.id==g&&(p=h);for(g=Jr(qe(p.messages[0].data)[2][0].data),x=ke.find(l,f[g].location),d=Ir(kr(x.content)),E=0;E<d.length;++E)h=d[E],h.id==g&&(p=h);var y=qe(p.messages[0].data);{y[6][0].data=De(n.e.r+1),y[7][0].data=De(n.e.c+1);var F=Jr(y[46][0].data),D=ke.find(l,f[F].location),M=Ir(kr(D.content));{for(var q=0;q<M.length&&M[q].id!=F;++q);if(M[q].id!=F)throw"Bad ColumnRowUIDMapArchive";var ie=qe(M[q].messages[0].data);ie[1]=[],ie[2]=[],ie[3]=[];for(var N=0;N<=n.e.c;++N){var V=[];V[1]=V[2]=[{type:0,data:De(N+420690)}],ie[1].push({type:2,data:tr(V)}),ie[2].push({type:0,data:De(N)}),ie[3].push({type:0,data:De(N)})}ie[4]=[],ie[5]=[],ie[6]=[];for(var L=0;L<=n.e.r;++L)V=[],V[1]=V[2]=[{type:0,data:De(L+726270)}],ie[4].push({type:2,data:tr(V)}),ie[5].push({type:0,data:De(L)}),ie[6].push({type:0,data:De(L)});M[q].messages[0].data=tr(ie)}D.content=Tt(Et(M)),D.size=D.content.length,delete y[46];var X=qe(y[4][0].data);{X[7][0].data=De(n.e.r+1);var K=qe(X[1][0].data),Y=Jr(K[2][0].data);D=ke.find(l,f[Y].location),M=Ir(kr(D.content));{if(M[0].id!=Y)throw"Bad HeaderStorageBucket";var te=qe(M[0].messages[0].data);for(L=0;L<i.length;++L){var we=qe(te[2][0].data);we[1][0].data=De(L),we[4][0].data=De(i[L].length),te[2][L]={type:te[2][0].type,data:tr(we)}}M[0].messages[0].data=tr(te)}D.content=Tt(Et(M)),D.size=D.content.length;var me=Jr(X[2][0].data);D=ke.find(l,f[me].location),M=Ir(kr(D.content));{if(M[0].id!=me)throw"Bad HeaderStorageBucket";for(te=qe(M[0].messages[0].data),N=0;N<=n.e.c;++N)we=qe(te[2][0].data),we[1][0].data=De(N),we[4][0].data=De(n.e.r+1),te[2][N]={type:te[2][0].type,data:tr(we)};M[0].messages[0].data=tr(te)}D.content=Tt(Et(M)),D.size=D.content.length;var He=Jr(X[4][0].data);(function(){for(var B=ke.find(l,f[He].location),O=Ir(kr(B.content)),C,H=0;H<O.length;++H){var ce=O[H];ce.id==He&&(C=ce)}var he=qe(C.messages[0].data);{he[3]=[];var oe=[];s.forEach(function(Te,je){oe[1]=[{type:0,data:De(je)}],oe[2]=[{type:0,data:De(1)}],oe[3]=[{type:2,data:Lh(Te)}],he[3].push({type:2,data:tr(oe)})})}C.messages[0].data=tr(he);var re=Et(O),Oe=Tt(re);B.content=Oe,B.size=B.content.length})();var Be=qe(X[3][0].data);{var gr=Be[1][0];delete Be[2];var Ge=qe(gr.data);{var rr=Jr(Ge[2][0].data);(function(){for(var B=ke.find(l,f[rr].location),O=Ir(kr(B.content)),C,H=0;H<O.length;++H){var ce=O[H];ce.id==rr&&(C=ce)}var he=qe(C.messages[0].data);{delete he[6],delete Be[7];var oe=new Uint8Array(he[5][0].data);he[5]=[];for(var re=0,Oe=0;Oe<=n.e.r;++Oe){var Te=qe(oe);re+=Uh(Te,i[Oe],s),Te[1][0].data=De(Oe),he[5].push({data:tr(Te),type:2})}he[1]=[{type:0,data:De(n.e.c+1)}],he[2]=[{type:0,data:De(n.e.r+1)}],he[3]=[{type:0,data:De(re)}],he[4]=[{type:0,data:De(n.e.r+1)}]}C.messages[0].data=tr(he);var je=Et(O),Ae=Tt(je);B.content=Ae,B.size=B.content.length})()}gr.data=tr(Ge)}X[3][0].data=tr(Be)}y[4][0].data=tr(X)}p.messages[0].data=tr(y);var lr=Et(d),S=Tt(lr);return x.content=S,x.size=x.content.length,l}function Vh(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function S0(e){Vh([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Hh(e,t){return t.bookType=="ods"?Gi(e,t):t.bookType=="numbers"?Wh(e,t):t.bookType=="xlsb"?Gh(e,t):Xh(e,t)}function Gh(e,t){At=1024,e&&!e.SSF&&(e.SSF=Tr(Ve)),e&&e.SSF&&(kn(),In(e.SSF),t.revssf=Pn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Gt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r=t.bookType=="xlsb"?"bin":"xml",n=Fi.indexOf(t.bookType)>-1,a=ei();S0(t=t||{});var i=l0(),s="",f=0;if(t.cellXfs=[],nt(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",_e(i,s,ai(e.Props,t)),a.coreprops.push(s),Ne(t.rels,2,s,Fe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}for(e.Props.Worksheets=e.Props.SheetNames.length,_e(i,s,si(e.Props)),a.extprops.push(s),Ne(t.rels,3,s,Fe.EXT_PROPS),e.Custprops!==e.Props&&fr(e.Custprops||{}).length>0&&(s="docProps/custom.xml",_e(i,s,fi(e.Custprops)),a.custprops.push(s),Ne(t.rels,4,s,Fe.CUST_PROPS)),f=1;f<=e.SheetNames.length;++f){var u={"!id":{}},x=e.Sheets[e.SheetNames[f-1]],d=(x||{})["!type"]||"sheet";switch(d){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,_e(i,s,$1(f-1,s,t,e,u)),a.sheets.push(s),Ne(t.wbrels,-1,"worksheets/sheet"+f+"."+r,Fe.WS[0])}if(x){var p=x["!comments"],E=!1,h="";p&&p.length>0&&(h="xl/comments"+f+"."+r,_e(i,h,j1(p,h)),a.comments.push(h),Ne(u,-1,"../comments"+f+"."+r,Fe.CMNT),E=!0),x["!legacy"]&&E&&_e(i,"xl/drawings/vmlDrawing"+f+".vml",Ai(f,x["!comments"])),delete x["!comments"],delete x["!legacy"]}u["!id"].rId1&&_e(i,ti(s),Ft(u))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,_e(i,s,Y1(t.Strings,s,t)),a.strs.push(s),Ne(t.wbrels,-1,"sharedStrings."+r,Fe.SST)),s="xl/workbook."+r,_e(i,s,z1(e,s)),a.workbooks.push(s),Ne(t.rels,1,s,Fe.WB),s="xl/theme/theme1.xml",_e(i,s,Ti(e.Themes,t)),a.themes.push(s),Ne(t.wbrels,-1,"theme/theme1.xml",Fe.THEME),s="xl/styles."+r,_e(i,s,K1(e,s,t)),a.styles.push(s),Ne(t.wbrels,-1,"styles."+r,Fe.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",_e(i,s,e.vbaraw),a.vba.push(s),Ne(t.wbrels,-1,"vbaProject.bin",Fe.VBA)),s="xl/metadata."+r,_e(i,s,q1(s)),a.metadata.push(s),Ne(t.wbrels,-1,"metadata."+r,Fe.XLMETA),_e(i,"[Content_Types].xml",ri(a,t)),_e(i,"_rels/.rels",Ft(t.rels)),_e(i,"xl/_rels/workbook."+r+".rels",Ft(t.wbrels)),delete t.revssf,delete t.ssf,i}function Xh(e,t){At=1024,e&&!e.SSF&&(e.SSF=Tr(Ve)),e&&e.SSF&&(kn(),In(e.SSF),t.revssf=Pn(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Gt?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Fi.indexOf(t.bookType)>-1,a=ei();S0(t=t||{});var i=l0(),s="",f=0;if(t.cellXfs=[],nt(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),s="docProps/core.xml",_e(i,s,ai(e.Props,t)),a.coreprops.push(s),Ne(t.rels,2,s,Fe.CORE_PROPS),s="docProps/app.xml",!(e.Props&&e.Props.SheetNames))if(!e.Workbook||!e.Workbook.Sheets)e.Props.SheetNames=e.SheetNames;else{for(var o=[],l=0;l<e.SheetNames.length;++l)(e.Workbook.Sheets[l]||{}).Hidden!=2&&o.push(e.SheetNames[l]);e.Props.SheetNames=o}e.Props.Worksheets=e.Props.SheetNames.length,_e(i,s,si(e.Props)),a.extprops.push(s),Ne(t.rels,3,s,Fe.EXT_PROPS),e.Custprops!==e.Props&&fr(e.Custprops||{}).length>0&&(s="docProps/custom.xml",_e(i,s,fi(e.Custprops)),a.custprops.push(s),Ne(t.rels,4,s,Fe.CUST_PROPS));var u=["SheetJ5"];for(t.tcid=0,f=1;f<=e.SheetNames.length;++f){var x={"!id":{}},d=e.Sheets[e.SheetNames[f-1]],p=(d||{})["!type"]||"sheet";switch(p){case"chart":default:s="xl/worksheets/sheet"+f+"."+r,_e(i,s,Pi(f-1,t,e,x)),a.sheets.push(s),Ne(t.wbrels,-1,"worksheets/sheet"+f+"."+r,Fe.WS[0])}if(d){var E=d["!comments"],h=!1,g="";if(E&&E.length>0){var y=!1;E.forEach(function(F){F[1].forEach(function(D){D.T==!0&&(y=!0)})}),y&&(g="xl/threadedComments/threadedComment"+f+"."+r,_e(i,g,To(E,u,t)),a.threadedcomments.push(g),Ne(x,-1,"../threadedComments/threadedComment"+f+"."+r,Fe.TCMNT)),g="xl/comments"+f+"."+r,_e(i,g,Si(E)),a.comments.push(g),Ne(x,-1,"../comments"+f+"."+r,Fe.CMNT),h=!0}d["!legacy"]&&h&&_e(i,"xl/drawings/vmlDrawing"+f+".vml",Ai(f,d["!comments"])),delete d["!comments"],delete d["!legacy"]}x["!id"].rId1&&_e(i,ti(s),Ft(x))}return t.Strings!=null&&t.Strings.length>0&&(s="xl/sharedStrings."+r,_e(i,s,pi(t.Strings,t)),a.strs.push(s),Ne(t.wbrels,-1,"sharedStrings."+r,Fe.SST)),s="xl/workbook."+r,_e(i,s,Mi(e)),a.workbooks.push(s),Ne(t.rels,1,s,Fe.WB),s="xl/theme/theme1.xml",_e(i,s,Ti(e.Themes,t)),a.themes.push(s),Ne(t.wbrels,-1,"theme/theme1.xml",Fe.THEME),s="xl/styles."+r,_e(i,s,_i(e,t)),a.styles.push(s),Ne(t.wbrels,-1,"styles."+r,Fe.STY),e.vbaraw&&n&&(s="xl/vbaProject.bin",_e(i,s,e.vbaraw),a.vba.push(s),Ne(t.wbrels,-1,"vbaProject.bin",Fe.VBA)),s="xl/metadata."+r,_e(i,s,wi()),a.metadata.push(s),Ne(t.wbrels,-1,"metadata."+r,Fe.XLMETA),u.length>1&&(s="xl/persons/person.xml",_e(i,s,wo(u)),a.people.push(s),Ne(t.wbrels,-1,"persons/person.xml",Fe.PEOPLE)),_e(i,"[Content_Types].xml",ri(a,t)),_e(i,"_rels/.rels",Ft(t.rels)),_e(i,"xl/_rels/workbook."+r+".rels",Ft(t.wbrels)),delete t.revssf,delete t.ssf,i}function zh(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=zr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Xi(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Jt(t.file,ke.write(e,{type:ye?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return ke.write(e,t)}function $h(e,t){var r=Tr(t||{}),n=Hh(e,r);return Kh(n,r)}function Kh(e,t){var r={},n=ye?"nodebuffer":typeof Uint8Array!="undefined"?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?ke.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if(typeof Deno!="undefined"&&typeof a=="string"){if(t.type=="binary"||t.type=="base64")return a;a=new Uint8Array(Rn(a))}return t.password&&typeof encrypt_agile!="undefined"?Xi(encrypt_agile(a,t.password),t):t.type==="file"?Jt(t.file,a):t.type=="string"?Ut(a):a}function Yh(e,t){var r=t||{},n=uh(e,r);return Xi(n,r)}function br(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return zt(Vr(n));case"binary":return Vr(n);case"string":return e;case"file":return Jt(t.file,n,"utf8");case"buffer":return ye?Kr(n,"utf8"):typeof TextEncoder!="undefined"?new TextEncoder().encode(n):br(n,{type:"binary"}).split("").map(function(a){return a.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function jh(e,t){switch(t.type){case"base64":return zt(e);case"binary":return e;case"string":return e;case"file":return Jt(t.file,e,"binary");case"buffer":return ye?Kr(e,"binary"):e.split("").map(function(r){return r.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function mn(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return t.type=="base64"?zt(r):t.type=="string"?Ut(r):r;case"file":return Jt(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function zi(e,t){Es(),P1(e);var r=Tr(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),r.type=="array"){r.type="binary";var n=zi(e,r);return r.type="array",Rn(n)}var a=0;if(r.sheet&&(typeof r.sheet=="number"?a=r.sheet:a=e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return br(lh(e,r),r);case"slk":case"sylk":return br(Nl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return br(Wi(e.Sheets[e.SheetNames[a]],r),r);case"txt":return jh($i(e.Sheets[e.SheetNames[a]],r),r);case"csv":return br(F0(e.Sheets[e.SheetNames[a]],r),r,"\uFEFF");case"dif":return br(Rl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return mn(Dl.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return br(Il.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return br(Ul.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return br(di.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return br(Gi(e,r),r);case"wk1":return mn(sa.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return mn(sa.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),mn(Ui(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),Yh(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return $h(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function qh(e){if(!e.bookType){var t={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"},r=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();r.match(/^\.[a-z]+$/)&&(e.bookType=r.slice(1)),e.bookType=t[e.bookType]||e.bookType}}function Jh(e,t,r){var n=r||{};return n.type="file",n.file=t,qh(n),zi(e,n)}function Zh(e,t,r,n,a,i,s,f){var o=sr(r),l=f.defval,u=f.raw||!Object.prototype.hasOwnProperty.call(f,"raw"),x=!0,d=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(d,"__rowNum__",{value:r,enumerable:!1})}catch{d.__rowNum__=r}else d.__rowNum__=r;if(!s||e[r])for(var p=t.s.c;p<=t.e.c;++p){var E=s?e[r][p]:e[n[p]+o];if(E===void 0||E.t===void 0){if(l===void 0)continue;i[p]!=null&&(d[i[p]]=l);continue}var h=E.v;switch(E.t){case"z":if(h==null)break;continue;case"e":h=h==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+E.t)}if(i[p]!=null){if(h==null)if(E.t=="e"&&h===null)d[i[p]]=null;else if(l!==void 0)d[i[p]]=l;else if(u&&h===null)d[i[p]]=null;else continue;else d[i[p]]=u&&(E.t!=="n"||E.t==="n"&&f.rawNumbers!==!1)?h:$r(E,h,f);h!=null&&(x=!1)}}return{row:d,isempty:x}}function Nn(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,i=[],s=0,f="",o={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},u=l.range!=null?l.range:e["!ref"];switch(l.header===1?n=1:l.header==="A"?n=2:Array.isArray(l.header)?n=3:l.header==null&&(n=0),typeof u){case"string":o=Le(u);break;case"number":o=Le(e["!ref"]),o.s.r=u;break;default:o=u}n>0&&(a=0);var x=sr(o.s.r),d=[],p=[],E=0,h=0,g=Array.isArray(e),y=o.s.r,F=0,D={};g&&!e[y]&&(e[y]=[]);var M=l.skipHidden&&e["!cols"]||[],q=l.skipHidden&&e["!rows"]||[];for(F=o.s.c;F<=o.e.c;++F)if(!(M[F]||{}).hidden)switch(d[F]=hr(F),r=g?e[y][F]:e[d[F]+x],n){case 1:i[F]=F-o.s.c;break;case 2:i[F]=d[F];break;case 3:i[F]=l.header[F-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),f=s=$r(r,null,l),h=D[s]||0,!h)D[s]=1;else{do f=s+"_"+h++;while(D[f]);D[s]=h,D[f]=1}i[F]=f}for(y=o.s.r+a;y<=o.e.r;++y)if(!(q[y]||{}).hidden){var ie=Zh(e,o,y,d,n,i,g,l);(ie.isempty===!1||(n===1?l.blankrows!==!1:!!l.blankrows))&&(p[E++]=ie.row)}return p.length=E,p}var da=/"/g;function Qh(e,t,r,n,a,i,s,f){for(var o=!0,l=[],u="",x=sr(r),d=t.s.c;d<=t.e.c;++d)if(!!n[d]){var p=f.dense?(e[r]||[])[d]:e[n[d]+x];if(p==null)u="";else if(p.v!=null){o=!1,u=""+(f.rawNumbers&&p.t=="n"?p.v:$r(p,null,f));for(var E=0,h=0;E!==u.length;++E)if((h=u.charCodeAt(E))===a||h===i||h===34||f.forceQuotes){u='"'+u.replace(da,'""')+'"';break}u=="ID"&&(u='"ID"')}else p.f!=null&&!p.F?(o=!1,u="="+p.f,u.indexOf(",")>=0&&(u='"'+u.replace(da,'""')+'"')):u="";l.push(u)}return f.blankrows===!1&&o?null:l.join(s)}function F0(e,t){var r=[],n=t==null?{}:t;if(e==null||e["!ref"]==null)return"";var a=Le(e["!ref"]),i=n.FS!==void 0?n.FS:",",s=i.charCodeAt(0),f=n.RS!==void 0?n.RS:`
`,o=f.charCodeAt(0),l=new RegExp((i=="|"?"\\|":i)+"+$"),u="",x=[];n.dense=Array.isArray(e);for(var d=n.skipHidden&&e["!cols"]||[],p=n.skipHidden&&e["!rows"]||[],E=a.s.c;E<=a.e.c;++E)(d[E]||{}).hidden||(x[E]=hr(E));for(var h=0,g=a.s.r;g<=a.e.r;++g)(p[g]||{}).hidden||(u=Qh(e,a,g,x,s,o,i,n),u!=null&&(n.strip&&(u=u.replace(l,"")),(u||n.blankrows!==!1)&&r.push((h++?f:"")+u)));return delete n.dense,r.join("")}function $i(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=F0(e,t);return r}function ex(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=Le(e["!ref"]),i="",s=[],f,o=[],l=Array.isArray(e);for(f=a.s.c;f<=a.e.c;++f)s[f]=hr(f);for(var u=a.s.r;u<=a.e.r;++u)for(i=sr(u),f=a.s.c;f<=a.e.c;++f)if(t=s[f]+i,r=l?(e[u]||[])[f]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=t+"="+n}return o}function Ki(e,t,r){var n=r||{},a=+!n.skipHeader,i=e||{},s=0,f=0;if(i&&n.origin!=null)if(typeof n.origin=="number")s=n.origin;else{var o=typeof n.origin=="string"?Qe(n.origin):n.origin;s=o.r,f=o.c}var l,u={s:{c:0,r:0},e:{c:f,r:s+t.length-1+a}};if(i["!ref"]){var x=Le(i["!ref"]);u.e.c=Math.max(u.e.c,x.e.c),u.e.r=Math.max(u.e.r,x.e.r),s==-1&&(s=x.e.r+1,u.e.r=s+t.length-1+a)}else s==-1&&(s=0,u.e.r=t.length-1+a);var d=n.header||[],p=0;t.forEach(function(h,g){fr(h).forEach(function(y){(p=d.indexOf(y))==-1&&(d[p=d.length]=y);var F=h[y],D="z",M="",q=Ie({c:f+p,r:s+g+a});l=jt(i,q),F&&typeof F=="object"&&!(F instanceof Date)?i[q]=F:(typeof F=="number"?D="n":typeof F=="boolean"?D="b":typeof F=="string"?D="s":F instanceof Date?(D="d",n.cellDates||(D="n",F=Er(F)),M=n.dateNF||Ve[14]):F===null&&n.nullError&&(D="e",F=0),l?(l.t=D,l.v=F,delete l.w,delete l.R,M&&(l.z=M)):i[q]=l={t:D,v:F},M&&(l.z=M))})}),u.e.c=Math.max(u.e.c,f+d.length-1);var E=sr(s);if(a)for(p=0;p<d.length;++p)i[hr(p+f)+E]={t:"s",v:d[p]};return i["!ref"]=Ke(u),i}function rx(e,t){return Ki(null,e,t)}function jt(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=Qe(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?jt(e,Ie(t)):jt(e,Ie({r:t,c:r||0}))}function tx(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function nx(){return{SheetNames:[],Sheets:{}}}function ax(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var s=i&&i[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=s+a)!=-1;++a);}if(Bi(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function ix(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=tx(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function sx(e,t){return e.z=t,e}function Yi(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function fx(e,t,r){return Yi(e,"#"+t,r)}function lx(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function ox(e,t,r,n){for(var a=typeof t!="string"?t:Le(t),i=typeof t=="string"?t:Ke(t),s=a.s.r;s<=a.e.r;++s)for(var f=a.s.c;f<=a.e.c;++f){var o=jt(e,s,f);o.t="n",o.F=i,delete o.v,s==a.s.r&&f==a.s.c&&(o.f=r,n&&(o.D=!0))}return e}var cr={encode_col:hr,encode_row:sr,encode_cell:Ie,encode_range:Ke,decode_col:d0,decode_row:x0,split_cell:wf,decode_cell:Qe,decode_range:Cr,format_cell:$r,sheet_add_aoa:Ya,sheet_add_json:Ki,sheet_add_dom:Vi,aoa_to_sheet:Nt,json_to_sheet:rx,table_to_sheet:Hi,table_to_book:Ih,sheet_to_csv:F0,sheet_to_txt:$i,sheet_to_json:Nn,sheet_to_html:Wi,sheet_to_formulae:ex,sheet_to_row_object_array:Nn,sheet_get_cell:jt,book_new:nx,book_append_sheet:ax,book_set_sheet_visibility:ix,cell_set_number_format:sx,cell_set_hyperlink:Yi,cell_set_internal_link:fx,cell_add_comment:lx,sheet_set_array_formula:ox,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};const ux={class:"row q-mb-lg"},cx={class:"col-12 col-sm-6"},hx={class:"q-pa-sm"},xx={class:"row q-mb-lg"},dx={class:"col-12 col-sm-6"},px={class:"q-pa-sm"},mx={class:"col-12 col-sm-6"},vx={class:"q-pa-sm"},gx={key:0,class:"row q-mb-lg"},_x={class:"col-12"},Ex={class:"row q-mb-md"},Tx={class:"col-12 col-sm-4 q-pa-sm"},wx={class:"col-12 col-sm-4 q-pa-sm"},Ax={class:"col-12 col-sm-4 q-pa-sm"},Sx={class:"row q-mb-md"},Fx={class:"col-12 col-sm-4"},Cx={class:"q-pa-sm"},yx={class:"col-12 col-sm-4"},Ox={class:"q-pa-sm"},Dx={class:"col-12 col-sm-4"},Nx={class:"q-pa-sm"},Rx={class:"row q-col-gutter-md"},Ix={class:"col-12 col-sm-4"},kx={class:"q-pa-sm"},Px={class:"row q-mb-md"},Lx={class:"col-12 col-sm-4 q-pa-sm"},Bx={class:"col-12 col-sm-4 q-pa-sm"},Mx={class:"col-12 col-sm-4 q-pa-sm"},bx={class:"row q-mb-md"},Ux={class:"col-12 col-sm-4"},Wx={class:"q-pa-sm"},Vx={class:"col-12 col-sm-4"},Hx={class:"q-pa-sm"},Gx={class:"col-12 col-sm-4"},Xx={class:"q-pa-sm"},zx={class:"row q-mb-md"},$x={class:"col-12 col-sm-4 q-pa-sm"},Kx={class:"col-12 col-sm-4 q-pa-sm"},Yx={class:"col-12 col-sm-4 q-pa-sm"},jx={class:"row q-mb-md"},qx={class:"col-12 col-sm-4 q-pa-sm"},Jx={class:"col-12 col-sm-4 q-pa-sm"},Zx={class:"col-12 col-sm-4 q-pa-sm"},Qx={class:"row q-mb-md"},e2={class:"col-12 col-sm-4"},r2={class:"q-pa-sm"},t2={class:"col-12 col-sm-4"},n2={class:"q-pa-sm"},a2={class:"col-12 col-sm-4"},i2={class:"q-pa-sm"},s2={class:"text-center q-mb-sm"},f2={class:"row q-mb-md"},l2={class:"col-12 col-sm-6"},o2={class:"text-subtitle1"},u2={class:"text-subtitle1"},c2={class:"col-12 col-sm-6 text-right"},h2=ls({__name:"BatchAnalysisPage",setup(e){const t=ps(),r=[{label:"\u7248\u8DEF\u5206\u6790",value:"ball-follow"}],n=[{label:"\u5A01\u529B\u5F69",value:"super_lotto638"},{label:"\u5927\u6A02\u900F",value:"lotto649"},{label:"\u4ECA\u5F69539",value:"daily539"},{label:"\u516D\u5408\u5F69",value:"lotto_hk"}],a=Je("ball-follow"),i=Je("super_lotto638"),s=Je(""),f=Je("excel"),o=Je(!1),l=Je(0),u=Je(""),x=Je([]),d=Je(1),p=Je([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21\u4EE5\u4E0A",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21\u4EE5\u4E0A",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21\u4EE5\u4E0A",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21\u4EE5\u4E0A",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21\u4EE5\u4E0A",value:5}]),E=y0(()=>new Date().toISOString().split("T")[0]),h=y0(()=>a.value&&i.value&&s.value&&!o.value),g=Je({num1:1,num2:1,num3:1,periodNum:50,maxRange:20,aheadNum:1}),y=Je({num1:1,num2:1,num3:1,periodNum:50,maxRange:20,aheadNum:1}),F=Je({comb1:1,comb2:1,comb3:1,tailComb1:1,tailComb2:1,tailComb3:1,period:50,maxRange:20,ahead:1}),D=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],M=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],q=Je(Array.from({length:491},(z,w)=>({label:`${w+10}\u671F`,value:w+10}))),ie=Je(Array.from({length:21},(z,w)=>({label:`${w+10}\u671F`,value:w+10}))),N=Je(Array.from({length:15},(z,w)=>({label:`\u4E0B${w+1}\u671F`,value:w+1}))),V=Je(Array.from({length:991},(z,w)=>({label:`${w+10}\u671F`,value:w+10}))),L=z=>({"ball-follow":"\u7248\u8DEF\u5206\u6790",tail:"\u5C3E\u6578\u5206\u6790",pattern:"\u7D9C\u5408\u5206\u6790"})[z]||z,X=z=>({super_lotto638:"\u5A01\u529B\u5F69",lotto649:"\u5927\u6A02\u900F",daily539:"\u4ECA\u5F69539",lotto_hk:"\u516D\u5408\u5F69"})[z]||z,K=()=>{},Y=()=>{switch(a.value){case"ball-follow":return{comb1:g.value.num1,comb2:g.value.num2,comb3:g.value.num3,periodNum:g.value.periodNum,maxRange:g.value.maxRange,aheadNum:g.value.aheadNum};case"tail":return{tailComb1:y.value.num1,tailComb2:y.value.num2,tailComb3:y.value.num3,periodNum:y.value.periodNum,maxRange:y.value.maxRange,aheadNum:y.value.aheadNum};case"pattern":return{comb1:F.value.comb1,comb2:F.value.comb2,comb3:F.value.comb3,tailComb1:F.value.tailComb1,tailComb2:F.value.tailComb2,tailComb3:F.value.tailComb3,period:F.value.period,maxRange:F.value.maxRange,ahead:F.value.ahead};default:return{comb1:1,comb2:1,comb3:1,tailComb1:1,tailComb2:1,tailComb3:1,periodNum:50,maxRange:20,aheadNum:1}}},te=Je(!1),we=(z,w=49,W=1)=>{const fe=z.filter(ue=>ue.consecutiveHits>=W),de=new Map,ge=new Map,ne=new Map;fe.forEach(ue=>{ue.targetNumbers.forEach(Ee=>{de.set(Ee,(de.get(Ee)||0)+1);const xe=Ee%10;ge.set(xe,(ge.get(xe)||0)+1)})});const pe=[];for(let ue=1;ue<=w;ue++)if(!de.has(ue)){pe.push(ue);const Ee=z.filter(xe=>xe.targetNumbers.includes(ue)).length;ne.set(ue,Ee)}return{predictNumbers:Array.from(de.entries()).sort((ue,Ee)=>Ee[1]-ue[1]).slice(0,10).map(([ue])=>ue),targetNumAppearances:de,nonAppearedNumbers:pe,nonAppearedByFrequency:ne,tailNumAppearances:ge}},me=(z,w)=>z.filter(W=>w.includes(W)),He=z=>{switch(z){case"super_lotto638":return 38;case"lotto649":return 49;case"daily539":return 39;case"lotto_hk":return 49;default:return 49}},Be=z=>we(z).predictNumbers,gr=z=>{const w=[];for(let W=0;W<z.length;W+=10){const fe=z.slice(W,W+10);w.push(fe.map(de=>de.toString().padStart(2,"0")).join(" "))}return w},Ge=(z,w,W=!1)=>{const fe=[];for(let de=0;de<z.length;de+=10){const ne=z.slice(de,de+10).map(pe=>{const Ce=W?pe.toString():pe.toString().padStart(2,"0");return w.includes(pe)?`*${Ce}*`:Ce});fe.push(ne.join(" "))}return fe},rr=(z,w=!1)=>{const W=[];for(let fe=0;fe<z.length;fe+=10){const ge=z.slice(fe,fe+10).map(ne=>w?ne.toString():ne.toString().padStart(2,"0"));W.push(ge)}return W},lr=(z,w)=>{const W=[{wch:10},{wch:12},{wch:30},{wch:30},{wch:30},{wch:10}];z["!cols"]=W;const fe=Array(w).fill({hpt:20});z["!rows"]=fe},S=(z,w,W)=>{console.log("\u958B\u59CB\u61C9\u7528\u7D05\u8272\u5B57\u9AD4\u6A19\u8A18\uFF0C\u985E\u578B:",W),console.log("\u6279\u91CF\u7D50\u679C\u6578\u91CF:",x.value.length);for(let fe=3;fe<w.length;fe++){const de=w[fe],ge=de[0];if(!ge||ge==="")continue;const ne=x.value.find(pe=>pe.date===ge);if(!ne||!ne.actualNumbers){console.log("\u627E\u4E0D\u5230\u5C0D\u61C9\u7D50\u679C\uFF0C\u65E5\u671F:",ge);continue}console.log("\u8655\u7406\u65E5\u671F:",ge,"\u5BE6\u969B\u865F\u78BC:",ne.actualNumbers);for(let pe=1;pe<de.length;pe++){const Ce=de[pe];if(Ce&&Ce!==""){let ue=!1;if(W==="predict"){const Ee=parseInt(Ce.toString().replace(/^0+/,""),10);ue=ne.actualNumbers.includes(Ee),console.log("\u6AA2\u67E5\u9810\u6E2C\u865F\u78BC:",Ee,"\u662F\u5426\u5339\u914D:",ue)}else{const Ee=parseInt(Ce.toString(),10),xe=ne.actualNumbers.map(Pe=>Pe%10);ue=xe.includes(Ee),console.log("\u6AA2\u67E5\u5C3E\u6578:",Ee,"\u5BE6\u969B\u5C3E\u6578:",xe,"\u662F\u5426\u5339\u914D:",ue)}if(ue){const Ee=cr.encode_cell({r:fe,c:pe});console.log("\u6A19\u8A18\u547D\u4E2D\u865F\u78BC\uFF0C\u5132\u5B58\u683C:",Ee,"\u503C:",Ce);const xe=`${Ce}\u2605`;z[Ee]?z[Ee].v=xe:z[Ee]={v:xe,t:"s"},w[fe][pe]=xe}}}}console.log("\u7D05\u8272\u5B57\u9AD4\u6A19\u8A18\u61C9\u7528\u5B8C\u6210")},B=async()=>{if(!h.value){ft.create({type:"warning",message:"\u8ACB\u5B8C\u6574\u586B\u5BEB\u6240\u6709\u5FC5\u8981\u53C3\u6578"});return}try{o.value=!0,te.value=i.value==="super_lotto638",l.value=0,u.value="\u6E96\u5099\u958B\u59CB...",x.value=[];const z=Y(),w="periodNum"in z?z.periodNum:"period"in z?z.period:50,fe=(await $n.getLottoList({draw_type:i.value,date_end:s.value,limit:w})).data.reverse();u.value="\u6B63\u5728\u9032\u884C\u5206\u6790...";let de=0;for(const ge of fe){l.value=de/fe.length,u.value=`\u5206\u6790\u4E2D... ${++de}/${fe.length}`;const ne=await $n.getLottoList({draw_type:i.value,date_end:ge.draw_date,limit:w}),pe=Y(),Ce="aheadNum"in pe?pe.aheadNum:"ahead"in pe?pe.ahead:1,ue=await $n.getLottoPredict({draw_type:i.value,draw_date:ge.draw_date,ahead_count:Ce});let Ee=[];ue.data&&(Ee=[...ue.data.draw_number_size||[]],!te.value&&ue.data.special_number&&Ee.push(ue.data.special_number));let xe={date:ge.draw_date,period:ge.period,analysisType:a.value,actualNumbers:Ee,predictResponse:ue.data};switch(a.value){case"ball-follow":const Pe=await C(ne.data);xe.ballFollowResults=Pe.data,xe.ballFollowOccurrences=Pe.occurrences;const yr=He(i.value),dr=we(Pe.data,yr,d.value);xe.predictNumbers=dr.predictNumbers,xe.targetNumAppearances=dr.targetNumAppearances,xe.nonAppearedNumbers=dr.nonAppearedNumbers,xe.nonAppearedByFrequency=dr.nonAppearedByFrequency,xe.tailNumAppearances=dr.tailNumAppearances,xe.matches=me(xe.predictNumbers||[],xe.actualNumbers||[]);break;case"tail":const Xe=await H(ne.data);xe.tailResults=Xe.data,xe.tailOccurrences=Xe.occurrences;const or=we(Xe.data,10,d.value);xe.predictNumbers=or.predictNumbers,xe.targetNumAppearances=or.targetNumAppearances,xe.nonAppearedNumbers=or.nonAppearedNumbers,xe.nonAppearedByFrequency=or.nonAppearedByFrequency,xe.tailNumAppearances=or.tailNumAppearances,xe.matches=me(xe.predictNumbers||[],xe.actualNumbers||[]);break;case"pattern":const ze=await C(ne.data),Yr=await H(ne.data);xe.ballFollowResults=ze.data,xe.ballFollowOccurrences=ze.occurrences,xe.tailResults=Yr.data,xe.tailOccurrences=Yr.occurrences;const tn=He(i.value),at=we(ze.data,tn,d.value),Un=we(Yr.data,10,d.value),nn=at.predictNumbers,an=Un.predictNumbers;xe.predictNumbers=[...new Set([...nn,...an])],xe.targetNumAppearances=at.targetNumAppearances,xe.nonAppearedNumbers=at.nonAppearedNumbers,xe.nonAppearedByFrequency=at.nonAppearedByFrequency,xe.tailNumAppearances=at.tailNumAppearances,xe.matches=me(xe.predictNumbers||[],xe.actualNumbers||[]);break}x.value.push(xe)}u.value="\u5206\u6790\u5B8C\u6210\uFF01",l.value=1,ft.create({type:"positive",position:"top",message:"\u5206\u6790\u5B8C\u6210\uFF01"})}catch(z){ms(z)}finally{o.value=!1}},O=()=>{o.value=!1,u.value="\u5206\u6790\u5DF2\u4E2D\u65B7",ft.create({type:"warning",message:"\u5206\u6790\u5DF2\u4E2D\u65B7"})},C=async z=>{const w=Y(),W=z.map(fe=>{const de=[...fe.draw_number_size];return!te.value&&fe.special_number&&de.push(fe.special_number),{numbers:[...de],period:String(fe.period)}});return t.init({firstGroupSize:w.comb1||1,secondGroupSize:w.comb2||1,targetGroupSize:w.comb3||1,maxRange:w.maxRange||20,lookAheadCount:w.aheadNum||1},W),await t.analyzeWithProgress()},H=async z=>{const w=Y(),W=z.map(fe=>{const de=new Set;for(let ne of fe.draw_number_size)de.add(ne%10);!te.value&&fe.special_number&&de.add(fe.special_number%10);const ge=Array.from(de).sort((ne,pe)=>ne===0?1:pe===0?-1:ne-pe);return{period:String(fe.period),numbers:[...ge]}});return t.init({firstGroupSize:w.tailComb1||1,secondGroupSize:w.tailComb2||1,targetGroupSize:w.tailComb3||1,maxRange:w.maxRange||20,lookAheadCount:w.aheadNum||1},W),await t.analyzeWithProgress()},ce=()=>{try{he()}catch(z){console.error("\u4E0B\u8F09\u5931\u6557:",z),ft.create({type:"negative",message:"\u6A94\u6848\u4E0B\u8F09\u5931\u6557"})}},he=()=>{if(x.value.length===0){ft.create({type:"warning",message:"\u6C92\u6709\u53EF\u4E0B\u8F09\u7684\u7D50\u679C"});return}try{const z=cr.book_new();switch(a.value){case"ball-follow":oe(z),re(z),Oe(z),Te(z),je(z);break;case"tail":Ae(z),je(z);break;case"pattern":Rr(z);break}const w=L(a.value),W=X(i.value),fe=new Date().toISOString().split("T")[0].replace(/-/g,""),de=`${w}_${W}_${fe}.xlsx`;Jh(z,de),ft.create({type:"positive",message:"\u5831\u8868\u4E0B\u8F09\u6210\u529F\uFF01"})}catch(z){console.error("Excel\u751F\u6210\u5931\u6557:",z),ft.create({type:"negative",message:"Excel\u6A94\u6848\u751F\u6210\u5931\u6557"})}},oe=z=>{const w=[];w.push(["\u9810\u6E2C\u865F\u78BC\u7D71\u8A08"]),w.push([]),w.push(["\u65E5\u671F"]),x.value.forEach(fe=>{if(fe.targetNumAppearances){const de=Array.from(fe.targetNumAppearances.entries()).sort((ne,pe)=>pe[1]-ne[1]).map(([ne])=>ne);rr(de).forEach((ne,pe)=>{pe===0?w.push([fe.date,...ne]):w.push(["",...ne])})}});const W=cr.aoa_to_sheet(w);S(W,w,"predict"),cr.book_append_sheet(z,W,"\u9810\u6E2C\u865F\u78BC")},re=z=>{const w=[];w.push(["\u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u9810\u6E2C\u6B21\u6578\u6392\u5E8F"]),w.push([]),w.push(["\u65E5\u671F"]),x.value.forEach(fe=>{if(fe.nonAppearedByFrequency){const de=Array.from(fe.nonAppearedByFrequency.entries()).sort((ne,pe)=>pe[1]-ne[1]).map(([ne])=>ne);rr(de).forEach((ne,pe)=>{pe===0?w.push([fe.date,...ne]):w.push(["",...ne])})}});const W=cr.aoa_to_sheet(w);S(W,w,"predict"),cr.book_append_sheet(z,W,"\u672A\u51FA\u73FE\u865F\u78BC-\u9810\u6E2C\u6B21\u6578")},Oe=z=>{const w=[];w.push(["\u672A\u51FA\u73FE\u865F\u78BC - \u4F9D\u5927\u5C0F\u6392\u5E8F"]),w.push([]),w.push(["\u65E5\u671F"]),x.value.forEach(fe=>{if(fe.nonAppearedNumbers){const de=[...fe.nonAppearedNumbers].sort((ne,pe)=>ne-pe);rr(de).forEach((ne,pe)=>{pe===0?w.push([fe.date,...ne]):w.push(["",...ne])})}});const W=cr.aoa_to_sheet(w);S(W,w,"predict"),cr.book_append_sheet(z,W,"\u672A\u51FA\u73FE\u865F\u78BC-\u5927\u5C0F\u6392\u5E8F")},Te=z=>{const w=[];w.push(["\u9810\u6E2C\u5C3E\u6578\u7D71\u8A08"]),w.push([]),w.push(["\u65E5\u671F"]),x.value.forEach(fe=>{if(fe.tailNumAppearances){const de=Array.from(fe.tailNumAppearances.entries()).sort((ne,pe)=>pe[1]-ne[1]).map(([ne])=>ne);rr(de,!0).forEach((ne,pe)=>{pe===0?w.push([fe.date,...ne]):w.push(["",...ne])})}});const W=cr.aoa_to_sheet(w);S(W,w,"tail"),cr.book_append_sheet(z,W,"\u5C3E\u6578\u7D71\u8A08")},je=z=>{const w=[],W=Y(),fe="aheadNum"in W?W.aheadNum:"ahead"in W?W.ahead:1;w.push([`\u5BE6\u969B\u958B\u734E\u865F\u78BC (\u9810\u6E2C${fe}\u671F\u5F8C)`]),w.push([]);const de=x.value.some(ue=>!ue.actualNumbers||ue.actualNumbers.length===0||i.value==="super_lotto638"||i.value==="daily539"?!1:ue.actualNumbers.length>6),ge=["\u5206\u6790\u65E5\u671F","\u9810\u6E2C\u65E5\u671F"];let ne=6;i.value==="super_lotto638"||i.value==="lotto649"?ne=6:i.value==="daily539"?ne=5:i.value==="lotto_hk"&&(ne=6);for(let ue=1;ue<=ne;ue++)ge.push(`\u865F\u78BC${ue}`);de&&ge.push("\u7279\u5225\u865F"),w.push(ge),x.value.forEach(ue=>{let Ee="",xe=!1;ue.predictResponse&&ue.predictResponse.draw_date?(Ee=ue.predictResponse.draw_date,xe=!!ue.predictResponse.period):Ee="\u5C1A\u672A\u958B\u734E";const Pe=[ue.date,Ee];let yr=[],dr;ue.actualNumbers&&ue.actualNumbers.length>0&&xe&&(te.value?yr=[...ue.actualNumbers].sort((Xe,or)=>Xe-or):ue.actualNumbers.length>ne?(yr=ue.actualNumbers.slice(0,-1).sort((Xe,or)=>Xe-or),dr=ue.actualNumbers[ue.actualNumbers.length-1]):yr=[...ue.actualNumbers].sort((Xe,or)=>Xe-or));for(let Xe=0;Xe<ne;Xe++)Xe<yr.length?Pe.push(yr[Xe].toString().padStart(2,"0")):!xe&&Ee!=="\u5C1A\u672A\u958B\u734E"?Pe.push("\u5C1A\u672A\u958B\u734E"):Pe.push("");de&&(dr!==void 0?Pe.push(dr.toString().padStart(2,"0")):!xe&&Ee!=="\u5C1A\u672A\u958B\u734E"?Pe.push("\u5C1A\u672A\u958B\u734E"):Pe.push("")),w.push(Pe)});const pe=cr.aoa_to_sheet(w),Ce=[{wch:12},{wch:12}];for(let ue=0;ue<ne+(de?1:0);ue++)Ce.push({wch:8});pe["!cols"]=Ce,cr.book_append_sheet(z,pe,"\u5BE6\u969B\u958B\u734E\u865F\u78BC")},Ae=z=>{const w=[],W=L(a.value),fe=X(i.value);w.push([`${W} - ${fe}`]),w.push([]);const de=["\u65E5\u671F","\u9810\u6E2C\u5C3E\u6578","\u5BE6\u969B\u5C3E\u6578","\u547D\u4E2D\u5C3E\u6578","\u547D\u4E2D\u6578\u91CF"];w.push(de),x.value.forEach(ne=>{const pe=(ne.predictNumbers||[]).slice(0,10),Ce=ne.actualNumbers||[],ue=ne.matches||[],Ee=pe.map(ze=>ze%10),xe=[...new Set(Ce.map(ze=>ze%10))],Pe=ue.map(ze=>ze%10),yr=Ge(Ee,Pe,!0),dr=gr(xe),Xe=gr(Pe),or=Math.max(yr.length,dr.length,Xe.length,1);for(let ze=0;ze<or;ze++){const Yr=[ze===0?ne.date:"",yr[ze]||"",dr[ze]||"",Xe[ze]||"",ze===0?Pe.length:""];w.push(Yr)}});const ge=cr.aoa_to_sheet(w);lr(ge,w.length),cr.book_append_sheet(z,ge,W)},Rr=z=>{oe(z),re(z),Oe(z),Te(z),Ae(z),je(z);const w=[],W=L(a.value),fe=X(i.value);w.push([`${W} - ${fe} \u7D9C\u5408\u7D71\u8A08`]),w.push([]);const de=["\u65E5\u671F","\u7248\u8DEF\u547D\u4E2D","\u5C3E\u6578\u547D\u4E2D","\u7E3D\u547D\u4E2D","\u547D\u4E2D\u7387"];w.push(de),x.value.forEach(ne=>{const pe=ne.ballFollowResults?me(Be(ne.ballFollowResults),ne.actualNumbers||[]).length:0,Ce=ne.tailResults?me(Be(ne.tailResults),ne.actualNumbers||[]).length:0,ue=pe+Ce,Ee=ue>0?(ue/((ne.actualNumbers||[]).length*2)*100).toFixed(2)+"%":"0%";w.push([ne.date,pe,Ce,ue,Ee])});const ge=cr.aoa_to_sheet(w);lr(ge,w.length),cr.book_append_sheet(z,ge,"\u7D9C\u5408\u7D71\u8A08")};return os(()=>{s.value=new Date().toISOString().split("T")[0]}),(z,w)=>(jr(),fn(ds,{class:"justify-center"},{default:qr(()=>[Se(O0,null,{default:qr(()=>[Se(Hn,null,{default:qr(()=>[w[43]||(w[43]=ae("div",{class:"text-h4 text-center q-mb-lg"},"\u5206\u6790\u5831\u8868",-1)),ae("div",ux,[ae("div",cx,[w[25]||(w[25]=ae("div",{class:"text-h6"},"\u5206\u6790\u65B9\u6CD5",-1)),ae("div",hx,[Se(Me,{outlined:"",dense:"",modelValue:a.value,"onUpdate:modelValue":w[0]||(w[0]=W=>a.value=W),options:r,"map-options":"","emit-value":"",class:"text-h6"},null,8,["modelValue"])])])]),ae("div",xx,[ae("div",dx,[w[26]||(w[26]=ae("div",{class:"text-h6 text-weight-bold"},"\u5F69\u7A2E\u9078\u64C7",-1)),ae("div",px,[Se(Me,{outlined:"",dense:"",modelValue:i.value,"onUpdate:modelValue":[w[1]||(w[1]=W=>i.value=W),K],options:n,"emit-value":"","map-options":""},null,8,["modelValue"])])]),ae("div",mx,[w[27]||(w[27]=ae("div",{class:"text-h6 text-weight-bold"},"\u53C3\u8003\u65E5\u671F",-1)),ae("div",vx,[Se(us,{outlined:"",dense:"",modelValue:s.value,"onUpdate:modelValue":w[2]||(w[2]=W=>s.value=W),type:"date",mask:"YYYY/MM/DD",max:E.value,class:"text-h6"},null,8,["modelValue","max"])])])]),a.value?(jr(),ln("div",gx,[ae("div",_x,[w[42]||(w[42]=ae("div",{class:"text-h6 text-weight-bold q-mb-md"},"\u53C3\u6578\u8A2D\u5B9A",-1)),a.value==="ball-follow"?(jr(),ln(Gn,{key:0},[ae("div",Ex,[w[28]||(w[28]=ae("div",{class:"col-12 text-h6"},"\u62D6\u724C\u7D44\u5408",-1)),ae("div",Tx,[Se(Me,{outlined:"",dense:"",modelValue:g.value.num1,"onUpdate:modelValue":w[3]||(w[3]=W=>g.value.num1=W),options:D,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",wx,[Se(Me,{outlined:"",dense:"",modelValue:g.value.num2,"onUpdate:modelValue":w[4]||(w[4]=W=>g.value.num2=W),options:D,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Ax,[Se(Me,{outlined:"",dense:"",modelValue:g.value.num3,"onUpdate:modelValue":w[5]||(w[5]=W=>g.value.num3=W),options:D,"emit-value":"","map-options":""},null,8,["modelValue"])])]),ae("div",Sx,[ae("div",Fx,[w[29]||(w[29]=ae("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),ae("div",Cx,[Se(Me,{outlined:"",dense:"",modelValue:g.value.periodNum,"onUpdate:modelValue":w[6]||(w[6]=W=>g.value.periodNum=W),options:q.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),ae("div",yx,[w[30]||(w[30]=ae("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),ae("div",Ox,[Se(Me,{outlined:"",dense:"",modelValue:g.value.maxRange,"onUpdate:modelValue":w[7]||(w[7]=W=>g.value.maxRange=W),options:ie.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),ae("div",Dx,[w[31]||(w[31]=ae("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),ae("div",Nx,[Se(Me,{outlined:"",dense:"",modelValue:g.value.aheadNum,"onUpdate:modelValue":w[8]||(w[8]=W=>g.value.aheadNum=W),options:N.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),ae("div",Rx,[ae("div",Ix,[w[32]||(w[32]=ae("div",{class:"text-h6"},"\u6E96\u78BA\u6B21\u6578",-1)),ae("div",kx,[Se(Me,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":w[9]||(w[9]=W=>d.value=W),options:p.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])])])],64)):st("",!0),a.value==="tail"?(jr(),ln(Gn,{key:1},[ae("div",Px,[w[33]||(w[33]=ae("div",{class:"col-12 text-h6"},"\u62D6\u724C\u7D44\u5408",-1)),ae("div",Lx,[Se(Me,{outlined:"",dense:"",modelValue:y.value.num1,"onUpdate:modelValue":w[10]||(w[10]=W=>y.value.num1=W),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Bx,[Se(Me,{outlined:"",dense:"",modelValue:y.value.num2,"onUpdate:modelValue":w[11]||(w[11]=W=>y.value.num2=W),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Mx,[Se(Me,{outlined:"",dense:"",modelValue:y.value.num3,"onUpdate:modelValue":w[12]||(w[12]=W=>y.value.num3=W),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])])]),ae("div",bx,[ae("div",Ux,[w[34]||(w[34]=ae("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),ae("div",Wx,[Se(Me,{outlined:"",dense:"",modelValue:y.value.periodNum,"onUpdate:modelValue":w[13]||(w[13]=W=>y.value.periodNum=W),options:q.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),ae("div",Vx,[w[35]||(w[35]=ae("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),ae("div",Hx,[Se(Me,{outlined:"",dense:"",modelValue:y.value.maxRange,"onUpdate:modelValue":w[14]||(w[14]=W=>y.value.maxRange=W),options:ie.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),ae("div",Gx,[w[36]||(w[36]=ae("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),ae("div",Xx,[Se(Me,{outlined:"",dense:"",modelValue:y.value.aheadNum,"onUpdate:modelValue":w[15]||(w[15]=W=>y.value.aheadNum=W),options:N.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):st("",!0),a.value==="pattern"?(jr(),ln(Gn,{key:2},[ae("div",zx,[w[37]||(w[37]=ae("div",{class:"col-12 text-h6"},"\u7248\u8DEF\u62D6\u724C\u7D44\u5408",-1)),ae("div",$x,[Se(Me,{outlined:"",dense:"",modelValue:F.value.comb1,"onUpdate:modelValue":w[16]||(w[16]=W=>F.value.comb1=W),options:D,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Kx,[Se(Me,{outlined:"",dense:"",modelValue:F.value.comb2,"onUpdate:modelValue":w[17]||(w[17]=W=>F.value.comb2=W),options:D,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Yx,[Se(Me,{outlined:"",dense:"",modelValue:F.value.comb3,"onUpdate:modelValue":w[18]||(w[18]=W=>F.value.comb3=W),options:D,"emit-value":"","map-options":""},null,8,["modelValue"])])]),ae("div",jx,[w[38]||(w[38]=ae("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),ae("div",qx,[Se(Me,{outlined:"",dense:"",modelValue:F.value.tailComb1,"onUpdate:modelValue":w[19]||(w[19]=W=>F.value.tailComb1=W),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Jx,[Se(Me,{outlined:"",dense:"",modelValue:F.value.tailComb2,"onUpdate:modelValue":w[20]||(w[20]=W=>F.value.tailComb2=W),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])]),ae("div",Zx,[Se(Me,{outlined:"",dense:"",modelValue:F.value.tailComb3,"onUpdate:modelValue":w[21]||(w[21]=W=>F.value.tailComb3=W),options:M,"emit-value":"","map-options":""},null,8,["modelValue"])])]),ae("div",Qx,[ae("div",e2,[w[39]||(w[39]=ae("div",{class:"text-h6"},"\u63A8\u7B97\u671F\u6578",-1)),ae("div",r2,[Se(Me,{outlined:"",dense:"",modelValue:F.value.period,"onUpdate:modelValue":w[22]||(w[22]=W=>F.value.period=W),options:V.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),ae("div",t2,[w[40]||(w[40]=ae("div",{class:"text-h6"},"\u6700\u5927\u5340\u9593",-1)),ae("div",n2,[Se(Me,{outlined:"",dense:"",modelValue:F.value.maxRange,"onUpdate:modelValue":w[23]||(w[23]=W=>F.value.maxRange=W),options:ie.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),ae("div",a2,[w[41]||(w[41]=ae("div",{class:"text-h6"},"\u9810\u6E2C\u671F\u6578",-1)),ae("div",i2,[Se(Me,{outlined:"",dense:"",modelValue:F.value.ahead,"onUpdate:modelValue":w[24]||(w[24]=W=>F.value.ahead=W),options:N.value,"emit-value":"","map-options":""},null,8,["modelValue","options"])])])])],64)):st("",!0)])])):st("",!0),Se(cs,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:qr(()=>[o.value?(jr(),fn(Xn,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",onClick:O,class:"text-h6 q-mr-md"})):st("",!0),Se(Xn,{type:"button",label:"\u7522\u751F\u5831\u8868",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:B,loading:o.value,disable:!h.value},{loading:qr(()=>[Se(hs)]),_:1},8,["loading","disable"])]),_:1})]),_:1}),o.value?(jr(),fn(Hn,{key:0},{default:qr(()=>[ae("div",s2,zn(u.value),1),Se(xs,{rounded:"",size:"md",value:l.value,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):st("",!0)]),_:1}),x.value.length>0?(jr(),fn(O0,{key:0,class:"q-mt-lg"},{default:qr(()=>[Se(Hn,null,{default:qr(()=>[w[44]||(w[44]=ae("div",{class:"text-h6 text-weight-bold q-mb-md"},"\u5206\u6790\u7D50\u679C",-1)),ae("div",f2,[ae("div",l2,[ae("div",o2," \u5206\u6790\u65B9\u6CD5\uFF1A"+zn(L(a.value)),1),ae("div",u2," \u5F69\u7A2E\uFF1A"+zn(X(i.value)),1)]),ae("div",c2,[Se(Xn,{color:"primary",icon:"download",label:`\u4E0B\u8F09 ${f.value.toUpperCase()} \u6A94\u6848`,onClick:ce,disable:x.value.length===0},null,8,["label","disable"])])])]),_:1})]),_:1})):st("",!0)]),_:1}))}});var F2=vs(h2,[["__scopeId","data-v-75878a06"]]);export{F2 as default};
