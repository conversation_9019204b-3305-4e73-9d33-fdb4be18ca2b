// 開獎結果
export interface DrawResult {
  period: string;
  numbers: number[];
}

// 參數設定
export interface AnalysisConfig {
  firstGroupSize: number; // 第一組要找幾個號碼
  secondGroupSize: number; // 第二組要找幾個號碼
  targetGroupSize: number; // 目標組要找幾個號碼
  maxRange: number; // 最大搜尋範圍(由第一組向後計算)
  lookAheadCount: number; // 目標組要往第二組後找幾期
}

export interface TailAnalysisConfig {
  firstGroupSize: number;
  targetGroupSize: number;
  maxRange: number;
  lookAheadCount: number;
}

// 分析結果
export interface StatResult {
  firstNumbers: number[]; // 第一組號碼
  secondNumbers: number[]; // 第二組號碼
  targetNumbers: number[]; // 預測號碼
  gap: number; // 第一組號碼與第二組號碼的間隔
  targetGap: number; // 第二組號碼與第三組號碼的間隔
  targetMatches: number; // 符合目標號碼的次數
  targetProbability: number; // 目標號碼出現機率
  rank: number;
  consecutiveHits: number; // 當前組合已經連續幾次命中，命中1次就算1，如果有中斷就清為0
}

export interface Occurrence {
  count: number;
  periods: OccurrenceDetail[];
  isPredict: boolean;
}

export interface TailStatResult {
  firstNumbers: number[];
  targetNumbers: number[];
  targetGap: number;
  targetMatches: number;
  targetProbability: number;
  rank: number;
  consecutiveHits: number; // 當前組合已經連續幾次命中，命中1次就算1，如果有中斷就清為0
}

// 分析結果的期號明細
export interface OccurrenceDetail {
  firstPeriod: string;
  secondPeriod: string;
  targetPeriod?: string;
}

// worker 訊息
export interface ProgressInfo {
  stage: 'preparation' | 'processing' | 'finalization' | 'complete';
  progress: number;
  total: number;
}

// 警告訊息
export interface WarningInfo {
  message: string;
  usedMB?: number;
  limitMB?: number;
  percentage?: number;
  complexity?: number;
  suggestion?: string;
}
