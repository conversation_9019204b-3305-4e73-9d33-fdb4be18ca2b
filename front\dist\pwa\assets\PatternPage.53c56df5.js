import{k as _e,r as v,b as we,c as Ce,w as pe,I as u,J as L,L as c,a2 as Z,M as n,a3 as re,P as s,O as f,az as r,aC as C,aA as y,A as b,aB as K,bf as B,be as F,N as q,a8 as ue,aD as Ee,a6 as Fe}from"./index.43a5c8e9.js";import{Q as V}from"./QSelect.5be6abd0.js";import{Q as ke,a as xe}from"./QItem.68221b4d.js";import{Q as he}from"./QSpinnerDots.70047834.js";import{Q as Ae}from"./QLinearProgress.46c3b050.js";import{Q as Se}from"./QPage.48be0cc6.js";import{u as qe,_ as De}from"./IndexPage.c697ef24.js";import{Q as Re,a as Ve,b as Be,c as Le,u as Me}from"./QTabPanels.53d33689.js";import{L as be}from"./lotto.b1d9e5c1.js";import{p as k}from"./padding.dd505b59.js";import{u as ye}from"./useLotteryAnalysis.9339f52c.js";import{Q as $e,a as ge,b as A,c as Te}from"./QTable.f6ea53e5.js";import{_ as Qe}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.d4e46236.js";import"./selection.ed72df40.js";import"./QPopupProxy.3bc9b060.js";import"./QResizeObserver.949ee671.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QList.ec342d80.js";const ze={class:"row q-gutter-y-md"},Pe={class:"col-12 col-sm-4 draw-title text-center"},je={class:"text-period"},Ue={class:"text-draw-date"},Ne={class:"col-12 col-sm-6 self-center"},Oe={class:"row justify-center"},He={key:0,class:"col-auto"},Ie={class:"row q-my-md q-gutter-sm items-center"},Ge={class:"col-sm-auto"},We={class:"row q-my-md q-gutter-sm items-center"},Je={class:"col-sm-auto"},Ze={class:"row q-col-gutter-xs"},Ke={class:"ball tail-number"},Xe={class:"row q-col-gutter-xs"},Ye={class:"ball tail-number"},et={class:"row q-col-gutter-xs"},tt={class:"ball tail-number"},lt={class:"row q-gutter-sm items-center q-mb-md"},st={class:"ball tail-number q-mx-xs"},at={class:"row q-gutter-sm items-center q-mb-md"},ot={class:"ball tail-number q-mx-xs"},ut={class:"row q-gutter-sm items-center"},rt={class:"ball tail-number q-mx-xs"},nt={class:"text-subtitle1"},it={class:"row justify-center"},dt={class:"row justify-center"},ct={class:"row justify-center"},mt={class:"row justify-center"},pt={class:"row justify-center"},vt={class:"row justify-center"},ft={class:"row justify-center"},bt={class:"row justify-center"},gt={class:"row justify-center"},_t={class:"row justify-center"},yt={class:"row no-wrap"},wt={key:0,class:"text-subtitle1 ball special-number"},Ct={class:"row no-wrap"},Et=_e({__name:"PatternResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},rdResults:{},tailRdResults:{}},setup(ve){const w=ve,x=v("1"),h=ye();let E=v(new Map);const te=[{name:"draw_date",label:"\u65E5\u671F",field:"draw_date",align:"center"},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"},{name:"draw_results",label:"\u734E\u865F",field:"draw_results",align:"center"},{name:"tail_set",label:"\u5C3E\u6578",field:"tail_set",align:"center"}];we(()=>{X()});const M=v(new Map),D=v(new Map),X=()=>{var a;E.value.clear(),U.value=[],N.value=[],W.value=[],M.value.clear();for(const e of w.drawResults){e.tails=new Map;for(let l=0;l<10;l++)e.tails.set(l,[]);let t=[...e.draw_number_size];!w.isSuperLotto&&e.special_number&&(t.push(e.special_number),t=t.sort((l,o)=>l-o));for(const l of t){const o=l%10;(a=e.tails.get(o))==null||a.push(l)}e.tailSet=h.getTailSet(e,w.isSuperLotto);for(const l of e.tailSet)M.value.set(l,(M.value.get(l)||0)+1)}M.value=P(M.value);for(const e of Array(10).keys())E.value.set(e,0);ne(),ie(),de(),G(),E.value=P(E.value),me()},Q=v(1),le=v([{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 1 \u6B21\u4EE5\u4E0A",value:1},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 2 \u6B21\u4EE5\u4E0A",value:2},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 3 \u6B21\u4EE5\u4E0A",value:3},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 4 \u6B21\u4EE5\u4E0A",value:4},{label:"\u5DF2\u9023\u7E8C\u62D6\u51FA 5 \u6B21\u4EE5\u4E0A",value:5}]),z=v(1),O=v(1),H=Ce(()=>Array.from({length:O.value},(a,e)=>({label:`\u5DF2\u9023\u7E8C\u62D6\u51FA ${e+1} \u6B21\u4EE5\u4E0A`,value:e+1})));pe([Q,z],()=>{X()});const ne=()=>{const a=v([]);D.value.clear(),a.value=w.rdResults.filter(o=>o.consecutiveHits>=Q.value);const e=new Map,t=Y(a.value);let l=0;for(const o of a.value){let i=new Set;for(const m of o.targetNumbers){const g=m%10,T=o.consecutiveHits,J=se(o,t.get(g)||0);e.set(g,(e.get(g)||0)+T+J),l+=o.consecutiveHits,i.add(g)}for(const m of i)D.value.set(m,(D.value.get(m)||0)+1)}D.value=P(D.value);for(const o of e.keys()){const i=e.get(o)||0;ae(o,i/l)}},R=v(new Map),ie=()=>{const a=v([]);R.value.clear(),a.value=w.tailRdResults.filter(l=>l.consecutiveHits>=z.value),O.value=1,O.value=Math.max(...a.value.map(l=>l.consecutiveHits));const e=Y(a.value);let t=0;for(const l of a.value)for(const o of l.targetNumbers){const i=l.consecutiveHits,m=se(l,e.get(o)||0);R.value.set(o,(R.value.get(o)||0)+i+m),t+=l.consecutiveHits}R.value=P(R.value);for(const l of R.value.keys()){const o=R.value.get(l)||0;ae(l,o/t)}},Y=a=>{const e=new Map,t=new Map;for(const l of a)for(const o of l.targetNumbers){const i=o%10;e.set(i,(e.get(i)||0)+o),t.set(i,(t.get(i)||0)+1)}for(const l of e.keys()){const o=t.get(l)||1;e.set(l,Number((e.get(l)||0)/o))}return e},se=(a,e)=>(a.consecutiveHits+5*e)/(a.consecutiveHits+5),de=()=>{const a=w.drawResults,e=a.slice(-10),t=ee(a),l=ce(e),o=new Map;t.forEach((m,g)=>{const T=l.get(g)||0;o.set(g,(m+T)/2)});const i=I(o);for(const m of o.keys()){const g=i.get(m)||0;ae(m,g)}},ee=a=>{const e=new Map;a.forEach(o=>{if(o.draw_number_size.forEach(i=>{const m=i%10;e.set(m,(e.get(m)||0)+1)}),!w.isSuperLotto&&o.special_number){const i=o.special_number%10;e.set(i,(e.get(i)||0)+1)}});const t=Math.max(...e.values()),l=new Map;return e.forEach((o,i)=>l.set(i,o/t)),l},ce=a=>{const e=new Map,t=new Map;for(let o=1;o<a.length;o++){const i=a[o-1].tailSet,m=a[o].tailSet;!i||!m||i.length===0||m.length===0||i.forEach(g=>{e.set(g,(e.get(g)||0)+1),m.includes(g)&&t.set(g,(t.get(g)||0)+1)})}const l=new Map;return e.forEach((o,i)=>{const m=t.get(i)||0;l.set(i,m/o)}),l},I=a=>{const e=Array.from(a.values()),t=Math.min(...e),l=Math.max(...e),o=new Map;return a.forEach((i,m)=>o.set(m,l===t?0:(i-t)/(l-t))),o},ae=(a,e)=>{E.value.set(a,(E.value.get(a)||0)+e)},P=a=>{const e=Array.from(a.entries());return e.sort((t,l)=>l[1]-t[1]),new Map(e)},j=v(3),G=()=>{for(const a of E.value.keys()){const e=E.value.get(a)||0;E.value.set(a,Number((e/j.value*100).toFixed(1)))}},U=v([]),N=v([]),W=v([]),me=()=>{U.value=[],N.value=[],W.value=[];const a=Array.from(D.value.entries()).map(l=>l[0]).slice(0,5),e=Array.from(R.value.entries()).map(l=>l[0]).slice(0,5),t=Array.from(E.value.entries()).map(l=>l[0]).slice(0,5);for(const l of a)e.includes(l)&&U.value.push(l);for(const l of a)t.includes(l)&&N.value.push(l);for(const l of e)t.includes(l)&&W.value.push(l);U.value.sort((l,o)=>l===0?1:o===0?-1:l-o),N.value.sort((l,o)=>l===0?1:o===0?-1:l-o),W.value.sort((l,o)=>l===0?1:o===0?-1:l-o)},S=a=>a&&Array.from(a).length===0?"#f8d7da":"",$=a=>{var e,t;return(t=(e=w.predictResult)==null?void 0:e.tailSet)!=null&&t.length?w.predictResult.tailSet.includes(a):!1};return(a,e)=>(u(),L(Z,{class:"q-mt-md"},{default:c(()=>[n(re,null,{default:c(()=>[n(Re,{modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=t=>x.value=t),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:c(()=>[n(Ve,{name:"1",label:"\u7248\u8DEF\u5206\u6790"})]),_:1},8,["modelValue"]),n(Be,{modelValue:x.value,"onUpdate:modelValue":e[3]||(e[3]=t=>x.value=t)},{default:c(()=>[n(Le,{name:"1"},{default:c(()=>[a.predictResult.period?(u(),L(Z,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:c(()=>[n(re,null,{default:c(()=>[s("div",ze,[s("div",Pe,[e[4]||(e[4]=s("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),s("div",je," \u7B2C "+f(a.predictResult.period)+" \u671F ",1),s("div",Ue," \u958B\u734E\u65E5\u671F\uFF1A"+f(a.predictResult.draw_date),1)]),s("div",Ne,[s("div",Oe,[(u(!0),r(y,null,C(a.predictResult.draw_number_size,t=>(u(),r("div",{key:t,class:"col-auto"},[(u(),r("div",{class:"ball",key:t},f(b(k)(t)),1))]))),128)),a.predictResult.special_number?(u(),r("div",He,[(u(),r("div",{class:"ball special-number",key:a.predictResult.special_number},f(b(k)(a.predictResult.special_number)),1))])):K("",!0)])])])]),_:1})]),_:1})):K("",!0),s("div",Ie,[e[5]||(e[5]=s("div",{class:"col-12 text-h6"},"\u7248\u8DEF\u5206\u6790\u7BE9\u9078",-1)),s("div",Ge,[n(V,{outlined:"",dense:"",modelValue:Q.value,"onUpdate:modelValue":e[1]||(e[1]=t=>Q.value=t),options:le.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),s("div",We,[e[6]||(e[6]=s("div",{class:"col-12 text-h6"},"\u5C3E\u6578\u5206\u6790\u7BE9\u9078",-1)),s("div",Je,[n(V,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":e[2]||(e[2]=t=>z.value=t),options:H.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),e[20]||(e[20]=s("div",{class:"row q-mb-sm"},[s("label",{class:"col text-h6 text-bold"}," \u62D6\u724C\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),n(Z,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:c(()=>[s("div",Ze,[(u(!0),r(y,null,C(D.value.entries(),([t])=>(u(),r("div",{class:F(["col-4 col-md-2 text-h6",{predict:$(t)}]),key:t},[s("span",Ke,f(t),1)],2))),128))])]),_:1}),e[21]||(e[21]=s("div",{class:"row q-mb-sm"},[s("label",{class:"col text-h6 text-bold"}," \u5C3E\u6578\uFF1A\u9810\u6E2C\u958B\u51FA\u6A5F\u7387 (\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F) ")],-1)),n(Z,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:c(()=>[s("div",Xe,[(u(!0),r(y,null,C(R.value.entries(),([t])=>(u(),r("div",{class:F(["col-4 col-md-2 text-h6",{predict:$(t)}]),key:t},[s("span",Ye,f(t),1)],2))),128))])]),_:1}),e[22]||(e[22]=s("div",{class:"row q-mb-sm"},[s("label",{class:"col text-h6 text-bold"}," \u7248\u8DEF\uFF1A\u5C3E\u6578\u9810\u6E2C\u958B\u51FA\u6A5F\u7387\uFF08\u7531\u9AD8\u81F3\u4F4E\u6392\u5E8F\uFF09 ")],-1)),n(Z,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:c(()=>[s("div",et,[(u(!0),r(y,null,C(b(E).entries(),([t])=>(u(),r("div",{class:F(["col-4 col-md-2 text-h6",{predict:$(t)}]),key:t},[s("span",tt,f(t),1)],2))),128))])]),_:1}),e[23]||(e[23]=s("div",{class:"row q-mb-sm"},[s("label",{class:"col text-h6 text-bold"}," \u7D9C\u5408\u6BD4\u5C0D\u9810\u6E2C\u5C3E\u6578 ")],-1)),n(Z,{flat:"",bordered:"",class:"q-mb-lg q-pa-sm appearance"},{default:c(()=>[s("div",lt,[e[7]||(e[7]=s("span",{class:"text-h6"},"\u62D6\u724C+\u5C3E\u6578\uFF1A",-1)),(u(!0),r(y,null,C(U.value.entries(),([,t])=>(u(),r("span",{key:t,class:F({predict:$(t)})},[s("span",st,f(t),1)],2))),128))]),s("div",at,[e[8]||(e[8]=s("span",{class:"text-h6"},"\u62D6\u724C+\u7248\u8DEF\uFF1A",-1)),(u(!0),r(y,null,C(N.value.entries(),([,t])=>(u(),r("span",{key:t,class:F({predict:$(t)})},[s("span",ot,f(t),1)],2))),128))]),s("div",ut,[e[9]||(e[9]=s("span",{class:"text-h6"},"\u5C3E\u6578+\u7248\u8DEF\uFF1A",-1)),(u(!0),r(y,null,C(W.value.entries(),([,t])=>(u(),r("span",{key:t,class:F({predict:$(t)})},[s("span",rt,f(t),1)],2))),128))])]),_:1}),n($e,{rows:a.drawResults,columns:te,"rows-per-page-options":[0],"hide-bottom":"","hide-pagination":"","virtual-scroll":"",separator:"cell",class:"sticky-virtscroll-table q-mt-lg"},{header:c(t=>[n(ge,{props:t,class:"bg-primary text-white"},{default:c(()=>[(u(!0),r(y,null,C(t.cols,l=>(u(),L(Te,{key:l.name,props:t},{default:c(()=>[q(f(l.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"])]),body:c(t=>[n(ge,{props:t},{default:c(()=>{var l,o,i,m,g,T,J,oe,_,fe;return[n(A,{key:"draw_date",props:t},{default:c(()=>[s("div",nt,f(t.row.draw_date),1)]),_:2},1032,["props"]),n(A,{key:"tail1",props:t,class:"fixed-col",style:B({backgroundColor:S((l=t.row.tails)==null?void 0:l.get(1))})},{default:c(()=>{var p;return[s("div",it,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(1),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d))+" ",1),e[10]||(e[10]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail2",props:t,class:"fixed-col",style:B({backgroundColor:S((o=t.row.tails)==null?void 0:o.get(2))})},{default:c(()=>{var p;return[s("div",dt,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(2),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[11]||(e[11]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail3",props:t,class:"fixed-col",style:B({backgroundColor:S((i=t.row.tails)==null?void 0:i.get(3))})},{default:c(()=>{var p;return[s("div",ct,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(3),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[12]||(e[12]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail4",props:t,class:"fixed-col",style:B({backgroundColor:S((m=t.row.tails)==null?void 0:m.get(4))})},{default:c(()=>{var p;return[s("div",mt,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(4),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[13]||(e[13]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail5",props:t,class:"fixed-col",style:B({backgroundColor:S((g=t.row.tails)==null?void 0:g.get(5))})},{default:c(()=>{var p;return[s("div",pt,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(5),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[14]||(e[14]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail6",props:t,class:"fixed-col",style:B({backgroundColor:S((T=t.row.tails)==null?void 0:T.get(6))})},{default:c(()=>{var p;return[s("div",vt,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(6),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[15]||(e[15]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail7",props:t,class:"fixed-col",style:B({backgroundColor:S((J=t.row.tails)==null?void 0:J.get(7))})},{default:c(()=>{var p;return[s("div",ft,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(7),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[16]||(e[16]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail8",props:t,class:"fixed-col",style:B({backgroundColor:S((oe=t.row.tails)==null?void 0:oe.get(8))})},{default:c(()=>{var p;return[s("div",bt,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(8),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[17]||(e[17]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail9",props:t,class:"fixed-col",style:B({backgroundColor:S((_=t.row.tails)==null?void 0:_.get(9))})},{default:c(()=>{var p;return[s("div",gt,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(9),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[18]||(e[18]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"tail0",props:t,class:"fixed-col",style:B({backgroundColor:S((fe=t.row.tails)==null?void 0:fe.get(0))})},{default:c(()=>{var p;return[s("div",_t,[(u(!0),r(y,null,C((p=t.row.tails)==null?void 0:p.get(0),d=>(u(),r("span",{key:d,class:F(["text-h6 ball col-6",{"special-number":d===t.row.special_number&&!a.isSuperLotto}])},[q(f(b(k)(d)),1),e[19]||(e[19]=s("br",null,null,-1))],2))),128))])]}),_:2},1032,["props","style"]),n(A,{key:"draw_results",props:t},{default:c(()=>[s("div",yt,[(u(!0),r(y,null,C(t.row.draw_number_size,p=>(u(),r("span",{key:p,class:"text-subtitle1 ball"},f(b(k)(p)),1))),128)),t.row.special_number?(u(),r("span",wt,f(b(k)(t.row.special_number)),1)):K("",!0)])]),_:2},1032,["props"]),n(A,{key:"tail_set",props:t},{default:c(()=>[s("div",Ct,[(u(!0),r(y,null,C(t.row.tailSet,p=>(u(),r("span",{key:p,class:"text-subtitle1 ball tail-number"},f(p),1))),128))])]),_:2},1032,["props"])]}),_:2},1032,["props"])]),_:1},8,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});var Ft=Qe(Et,[["__scopeId","data-v-439ea9a2"]]);const kt={class:"row lto-ref q-mb-sm"},xt={class:"col-12 col-sm-4 self-center text-h6"},ht={class:"col-12 col-sm-6 self-center text-subtitle1"},At={class:"row balls"},St={class:"ball"},qt={key:0,class:"col-auto"},Dt={class:"row q-mb-md"},Rt={class:"col"},Vt={key:1,class:"row q-mb-md"},Bt={class:"row q-mb-md"},Lt={class:"col-12 col-sm-4 q-pa-sm"},Mt={class:"col-12 col-sm-4 q-pa-sm"},$t={class:"col-12 col-sm-4 q-pa-sm"},Tt={class:"row q-mb-md"},Qt={class:"col-12 col-sm-4 q-pa-sm"},zt={class:"col-12 col-sm-4 q-pa-sm"},Pt={class:"col-12 col-sm-4 q-pa-sm"},jt={class:"row q-mb-md"},Ut={class:"col-12 col-sm-4"},Nt={class:"q-pa-sm"},Ot={class:"col-12 col-sm-4"},Ht={class:"q-pa-sm"},It={class:"col-12 col-sm-4"},Gt={class:"q-pa-sm"},Wt={class:"text-center q-mb-sm"},fl=_e({__name:"PatternPage",setup(ve){const w=qe(),x=v(w.getLotto),h=Me(),E=ye(),te=v(1),M=v(1),D=v(1),X=v(1),Q=v(1),le=v(1),z=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3}],O=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],H=v(!1),ne=()=>{H.value=!0},R=()=>{H.value=!1},ie=()=>{H.value=!1};pe(()=>w.getLotto,a=>{a&&(x.value=a)}),pe(()=>{var a;return(a=x.value)==null?void 0:a.period},()=>{j.value=[]});const Y=v(50);let se=v(Array.from({length:991},(a,e)=>({label:`${e+10}\u671F`,value:e+10})));const de=(a,e,t)=>{const l=parseInt(a,10);(l<10||l>1e3)&&t(),e(()=>{se.value=Array.from({length:991},(o,i)=>i+10).filter(o=>o.toString().startsWith(a)).map(o=>({label:`${o.toString()}\u671F`,value:o}))})},ee=v(20),ce=Array.from({length:21},(a,e)=>({label:`${e+10}\u671F`,value:e+10})),I=v(1),ae=Array.from({length:15},(a,e)=>({label:`\u4E0B${e+1}\u671F`,value:e+1})),P=v(!1),j=v([]),G=v({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),U=v([]),N=v([]),W=async()=>{var a,e,t;try{h.startCalculating(),P.value=w.isSuperLotto;const l=await be.getLottoList({draw_type:w.getDrawType,date_end:(e=(a=x.value)==null?void 0:a.draw_date)!=null?e:"",limit:Y.value});j.value=l.data.reverse();const o=await be.getLottoPredict({draw_type:w.getDrawType,draw_date:(t=w.getLotto)==null?void 0:t.draw_date,ahead_count:I.value});G.value=o.data,G.value.period&&(G.value.tailSet=E.getTailSet(G.value,P.value));const i=await me();U.value=i.data;const m=await S();N.value=m.data}catch(l){console.error("\u8A08\u7B97\u932F\u8AA4:",l)}finally{$()}},me=()=>{const a=j.value.map(l=>{const o=[...l.draw_number_size];return l.special_number&&!w.isSuperLotto&&o.push(l.special_number),{numbers:[...o],period:String(l.period)}});E.setResults(a),E.setConfig({firstGroupSize:te.value,secondGroupSize:M.value,targetGroupSize:D.value,maxRange:ee.value,lookAheadCount:I.value});let e=Date.now();const t=8;return E.analyzeWithProgress(async l=>{const o=Date.now();o-e>=t&&(await h.updateProgress(l),e=o)},l=>{h.addWarning(l)})},S=()=>{const a=j.value.map(l=>{const o=new Set;for(let m of l.draw_number_size)o.add(m%10);!w.isSuperLotto&&l.special_number&&o.add(l.special_number%10);const i=Array.from(o).sort((m,g)=>m===0?1:g===0?-1:m-g);return{period:String(l.period),numbers:[...i]}});E.init({firstGroupSize:X.value,secondGroupSize:Q.value,targetGroupSize:le.value,maxRange:ee.value,lookAheadCount:I.value},a);let e=Date.now();const t=8;return E.analyzeWithProgress(async l=>{const o=Date.now();o-e>=t&&(await h.updateProgress(l),e=o)},l=>{h.addWarning(l)})},$=()=>{h.stopCalculating(),E.stopAnalyzer()};return(a,e)=>(u(),L(Se,{class:"justify-center"},{default:c(()=>[n(Z,null,{default:c(()=>[n(re,null,{default:c(()=>{var t,l,o,i,m,g,T,J,oe;return[(t=b(w).getLotto)!=null&&t.draw_date?(u(),r(y,{key:0},[s("div",kt,[s("div",xt,[s("div",null,f(b(w).getDrawLabel),1),e[9]||(e[9]=s("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),s("span",null,f((l=x.value)==null?void 0:l.period),1),s("span",null,"\uFF08"+f((o=x.value)==null?void 0:o.draw_date)+"\uFF09",1)]),s("div",ht,[s("div",At,[(u(!0),r(y,null,C((i=x.value)==null?void 0:i.draw_number_size,_=>(u(),r("div",{class:"col-auto",key:_},[s("div",St,f(b(k)(_)),1)]))),128)),(m=x.value)!=null&&m.special_number?(u(),r("div",qt,[(u(),r("div",{class:"ball special-number",key:(g=x.value)==null?void 0:g.special_number},f(b(k)((T=x.value)==null?void 0:T.special_number)),1))])):K("",!0)])])]),s("div",Dt,[s("div",Rt,[H.value?(u(),L(ue,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:ie})):(u(),L(ue,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:ne}))])])],64)):(u(),r("div",Vt,e[10]||(e[10]=[s("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),n(Ee,{class:"q-mb-md"}),!H.value&&((J=b(w).getLotto)==null?void 0:J.draw_date)?(u(),r(y,{key:2},[e[17]||(e[17]=s("div",{class:"row q-mb-md"},[s("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u7D9C\u5408\u5206\u6790\u8A2D\u5B9A ")],-1)),s("div",Bt,[e[11]||(e[11]=s("div",{class:"col-12 text-h6 text-weight-bold"},"\u734E\u865F\u62D6\u724C\u7D44\u5408",-1)),s("div",Lt,[n(V,{outlined:"",dense:"",modelValue:te.value,"onUpdate:modelValue":e[0]||(e[0]=_=>te.value=_),options:z,"emit-value":"","map-options":""},null,8,["modelValue"])]),s("div",Mt,[n(V,{outlined:"",dense:"",modelValue:M.value,"onUpdate:modelValue":e[1]||(e[1]=_=>M.value=_),options:z,"emit-value":"","map-options":""},null,8,["modelValue"])]),s("div",$t,[n(V,{outlined:"",dense:"",modelValue:D.value,"onUpdate:modelValue":e[2]||(e[2]=_=>D.value=_),options:z,"emit-value":"","map-options":""},null,8,["modelValue"])])]),s("div",Tt,[e[12]||(e[12]=s("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),s("div",Qt,[n(V,{outlined:"",dense:"",modelValue:X.value,"onUpdate:modelValue":e[3]||(e[3]=_=>X.value=_),options:O,"emit-value":"","map-options":""},null,8,["modelValue"])]),s("div",zt,[n(V,{outlined:"",dense:"",modelValue:Q.value,"onUpdate:modelValue":e[4]||(e[4]=_=>Q.value=_),options:O,"emit-value":"","map-options":""},null,8,["modelValue"])]),s("div",Pt,[n(V,{outlined:"",dense:"",modelValue:le.value,"onUpdate:modelValue":e[5]||(e[5]=_=>le.value=_),options:O,"emit-value":"","map-options":""},null,8,["modelValue"])])]),s("div",jt,[s("div",Ut,[e[14]||(e[14]=s("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),s("div",Nt,[n(V,{outlined:"",dense:"",modelValue:Y.value,"onUpdate:modelValue":e[6]||(e[6]=_=>Y.value=_),options:b(se),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:de,"emit-value":"","map-options":""},{"no-option":c(()=>[n(ke,null,{default:c(()=>[n(xe,{class:"text-grey"},{default:c(()=>e[13]||(e[13]=[q(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),s("div",Ot,[e[15]||(e[15]=s("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),s("div",Ht,[n(V,{outlined:"",dense:"",modelValue:ee.value,"onUpdate:modelValue":e[7]||(e[7]=_=>ee.value=_),options:b(ce),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),s("div",It,[e[16]||(e[16]=s("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),s("div",Gt,[n(V,{outlined:"",dense:"",modelValue:I.value,"onUpdate:modelValue":e[8]||(e[8]=_=>I.value=_),options:b(ae),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),n(Fe,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:c(()=>[b(h).isCalculating?(u(),L(ue,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:$})):K("",!0),n(ue,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:W,loading:b(h).isCalculating},{loading:c(()=>[n(he)]),_:1},8,["loading"])]),_:1})],64)):(u(),L(De,{key:3,"draw-type-query":b(w).drawType,"date-query":((oe=x.value)==null?void 0:oe.draw_date)||"","is-select-ref":!0,onSelectRef:R},null,8,["draw-type-query","date-query"]))]}),_:1}),b(h).isCalculating?(u(),L(re,{key:0},{default:c(()=>[s("div",Wt,f(b(h).progressMessage),1),n(Ae,{rounded:"",size:"md",value:b(h).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):K("",!0)]),_:1}),!b(h).isCalculating&&j.value.length>0?(u(),L(Ft,{key:0,"is-super-lotto":P.value,"draw-results":j.value,"predict-result":G.value,"rd-results":U.value,"tail-rd-results":N.value},null,8,["is-super-lotto","draw-results","predict-result","rd-results","tail-rd-results"])):K("",!0)]),_:1}))}});export{fl as default};
