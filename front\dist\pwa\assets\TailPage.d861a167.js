import{k as ge,r as y,c as ke,b as Ce,w as oe,I as l,J as q,L as n,a2 as W,M as r,a3 as J,P as t,O as o,az as u,aC as b,aA as f,A as p,aB as P,bm as Fe,N as _,be as F,a8 as re,b6 as De,bf as Ee,a5 as qe,aD as xe,a6 as Be}from"./index.43a5c8e9.js";import{Q as le,b as ie}from"./QSelect.5be6abd0.js";import{Q as Ae,a as Pe}from"./QItem.68221b4d.js";import{Q as $e}from"./QSpinnerDots.70047834.js";import{Q as Ve}from"./QLinearProgress.46c3b050.js";import{Q as Se}from"./QPage.48be0cc6.js";import{Q as Ne,a as ve,b as Re,c as fe,u as Le}from"./QTabPanels.53d33689.js";import{u as he,_ as Qe}from"./IndexPage.c697ef24.js";import{L as _e}from"./lotto.b1d9e5c1.js";import{u as ze}from"./useLotteryAnalysis.9339f52c.js";import{p as w}from"./padding.dd505b59.js";import{Q as ye}from"./QPagination.0eb733af.js";import{Q as Te,a as we,b as M,c as Me}from"./QTable.f6ea53e5.js";import{u as Ie}from"./use-quasar.57b6b537.js";import{Q as Ue}from"./QSpace.c6e7a6da.js";import{_ as je}from"./plugin-vue_export-helper.21dcd24c.js";import"./position-engine.d4e46236.js";import"./selection.ed72df40.js";import"./QResizeObserver.949ee671.js";import"./touch.9135741d.js";import"./use-render-cache.3aae9b27.js";import"./QPopupProxy.3bc9b060.js";import"./QList.ec342d80.js";const Oe={class:"row q-gutter-y-md"},Ge={class:"col-12 col-sm-4 draw-title text-center"},He={class:"text-period"},We={class:"text-draw-date"},Je={class:"col-12 col-sm-6 self-center"},Ze={class:"row justify-center"},Ke={key:0,class:"col-auto"},Xe={class:"row q-my-md q-gutter-sm items-center"},Ye={class:"col-sm-auto"},et={class:"col-sm-auto"},tt={class:"row q-my-md"},lt={class:"col-auto text-h6"},st={class:"row q-col-gutter-sm"},ut={class:"row items-center"},at={class:"ball tail-number"},ot={key:0,class:"row q-my-md justify-center"},rt={class:"row q-col-gutter-md"},it={key:0,class:"col-12 q-my-sm"},nt={class:"row q-col-gutter-md items-center text-h6"},dt={class:"col-12 col-sm-4"},ct={class:"col-12 col-sm-6"},mt={class:"row q-col-gutter-md"},pt={class:"col-auto"},vt={class:"col-12 col-sm-2 text-center"},ft={key:1,class:"row justify-center"},gt={class:"row q-my-md"},bt={class:"row q-my-sm"},_t={class:"col text-h6 text-bold"},yt={class:"row q-gutter-xs"},wt={class:"row items-center"},kt={class:"text-h6"},ht={class:"row justify-center"},Ct={key:0,class:"col-auto"},Ft={class:"row items-center"},Dt={class:"col-12 q-mb-sm text-h6 text-center"},Et={class:"col-12 col"},qt={class:"row justify-center"},xt={key:0,class:"col-auto"},Bt={class:"col-12 q-mt-md"},At={class:"row q-gutter-xs justify-center"},Pt={class:"text-subtitle1",style:{"border-bottom":"1px solid black"}},$t=ge({__name:"TailFollowResult",props:{isSuperLotto:{type:Boolean},drawResults:{},predictResult:{},drawTailResults:{},rdResults:{},occurrenceResults:{},pageSize:{}},emits:["view-detail"],setup(ce){const k=ce,B=!Ie().platform.is.desktop,Q=y("1"),S=y(1),D=y({pageItems:[],targetNumAppearances:new Map,totalCount:0,totalPages:0}),h=y(1),ne=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],Z=y(new Map),d=y(1),g=y(1),A=ke(()=>Array.from({length:g.value},(i,a)=>({label:`\u5DF2\u9023\u7E8C\u62D6\u51FA ${a+1} \u6B21\u4EE5\u4E0A`,value:a+1}))),z=y("count"),I=y([{label:"\u6E96\u78BA\u6B21\u6578\u7D71\u8A08",value:"count"},{label:"\u9810\u6E2C\u7D44\u6578\u7D71\u8A08",value:"group"}]),j=[{name:"period",label:"\u671F\u6578",field:"period",align:"center"},{name:"draw_number_size",label:"\u958B\u734E\u865F\u78BC",field:"draw_number_size",align:"center",format:i=>i.join(" ")},{name:"tail1",label:"\u5C3E1",field:"tail1",align:"center"},{name:"tail2",label:"\u5C3E2",field:"tail2",align:"center"},{name:"tail3",label:"\u5C3E3",field:"tail3",align:"center"},{name:"tail4",label:"\u5C3E4",field:"tail4",align:"center"},{name:"tail5",label:"\u5C3E5",field:"tail5",align:"center"},{name:"tail6",label:"\u5C3E6",field:"tail6",align:"center"},{name:"tail7",label:"\u5C3E7",field:"tail7",align:"center"},{name:"tail8",label:"\u5C3E8",field:"tail8",align:"center"},{name:"tail9",label:"\u5C3E9",field:"tail9",align:"center"},{name:"tail0",label:"\u5C3E0",field:"tail0",align:"center"}],$=y(!1);Ce(()=>{$.value=!1,X(),T(),$.value=!0}),oe(()=>d.value,()=>{!$.value||T()}),oe(()=>z.value,()=>{!$.value||T()}),oe(()=>S.value,()=>{!$.value||G()}),oe(()=>h.value,()=>{O()});const O=()=>{const i=new Map;for(const a of k.drawTailResults){const e=v(a.numbers,h.value),s=Array.from(e);for(const L of s){let N="";for(let ue=0;ue<L.length;ue++)N+=`${L[ue]}`,ue<L.length-1&&(N+=", ");const de=i.get(N);de?i.set(N,de+1):i.set(N,1)}const c=Array.from(i.entries()).sort((L,N)=>N[1]-L[1]);Z.value=new Map(c)}},K=i=>{const a=v(k.predictResult.tailSet||[],h.value),e=Array.from(a),s=[];for(const c of e){let L="";for(let N=0;N<c.length;N++)L+=`${c[N]}`,N<c.length-1&&(L+=", ");s.push(L)}return s.includes(i)};function v(i,a){return C(i,a)}function*C(i,a,e=0,s=[]){if(s.length===a){yield[...s];return}for(let c=e;c<i.length;c++)s.push(i[c]),yield*C(i,a,c+1,s),s.pop()}const X=()=>{var i;S.value=1,h.value=1,O();for(let a of k.drawResults){a.tails=new Map;for(let s=0;s<10;s++)a.tails.set(s,[]);let e=[...a.draw_number_size];!k.isSuperLotto&&a.special_number&&(e.push(a.special_number),e=e.sort((s,c)=>s-c));for(const s of e){const c=s%10;(i=a.tails.get(c))==null||i.push(s)}}},R=y([]),T=()=>{R.value=[],S.value=1;const i=new Map;g.value=1;for(const e of k.rdResults)if(e.consecutiveHits>=d.value){if(R.value.push(e),e.consecutiveHits>g.value&&(g.value=e.consecutiveHits),z.value==="count")for(const c of e.targetNumbers){const L=e.consecutiveHits;i.set(c,(i.get(c)||0)+L)}else if(z.value==="group")for(const c of e.targetNumbers)i.set(c,(i.get(c)||0)+1)}const a=Array.from(i.entries()).sort((e,s)=>s[1]-e[1]);D.value.targetNumAppearances=new Map(a),G()},G=()=>{const i=(S.value-1)*k.pageSize,a=i+k.pageSize;D.value.pageItems=R.value.slice(i,a),D.value.totalCount=R.value.length,D.value.totalPages=Math.ceil(R.value.length/k.pageSize)};return(i,a)=>(l(),q(W,{class:"q-mt-md"},{default:n(()=>[r(J,null,{default:n(()=>[r(Ne,{modelValue:Q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value=e),dense:"",align:"justify",class:"text-h6","active-color":"primary","indicator-color":"primary"},{default:n(()=>[r(ve,{name:"1",label:"\u5206\u6790\u7D50\u679C"}),r(ve,{name:"2",label:"\u7D44\u5408\u7D71\u8A08\u7D50\u679C"}),r(ve,{name:"3",label:"\u958B\u734E\u7D50\u679C"})]),_:1},8,["modelValue"]),i.predictResult.period?(l(),q(W,{key:0,bordered:"",class:"ball-card full-width q-my-lg"},{default:n(()=>[r(J,null,{default:n(()=>[t("div",Oe,[t("div",Ge,[a[7]||(a[7]=t("div",{class:"text-h6"},"\u9810\u6E2C\u958B\u734E\u7D50\u679C",-1)),t("div",He,"\u7B2C "+o(i.predictResult.period)+" \u671F",1),t("div",We," \u958B\u734E\u65E5\u671F\uFF1A"+o(i.predictResult.draw_date),1)]),t("div",Je,[t("div",Ze,[(l(!0),u(f,null,b(i.predictResult.draw_number_size,e=>(l(),u("div",{key:e,class:"col-auto"},[(l(),u("div",{class:"ball",key:e},o(p(w)(e)),1))]))),128)),i.predictResult.special_number?(l(),u("div",Ke,[(l(),u("div",{class:"ball special-number",key:i.predictResult.special_number},o(p(w)(i.predictResult.special_number)),1))])):P("",!0)])])])]),_:1})]),_:1})):P("",!0),r(Re,{modelValue:Q.value,"onUpdate:modelValue":a[6]||(a[6]=e=>Q.value=e)},{default:n(()=>[r(fe,{name:"1"},{default:n(()=>[t("div",Xe,[a[8]||(a[8]=t("div",{class:"col-sm-auto text-h6"},"\u7BE9\u9078",-1)),t("div",Ye,[r(le,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":a[1]||(a[1]=e=>d.value=e),options:A.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])]),t("div",et,[r(le,{outlined:"",dense:"",modelValue:z.value,"onUpdate:modelValue":a[2]||(a[2]=e=>z.value=e),options:I.value,"map-options":"","emit-value":""},null,8,["modelValue","options"])])]),t("div",tt,[t("div",lt," \u5171 "+o(D.value.totalCount)+" \u7B46\u8CC7\u6599 ",1)]),a[11]||(a[11]=t("div",{class:"row q-my-sm"},[t("label",{class:"col text-h6 text-bold"},"\u9810\u6E2C\u7D50\u679C")],-1)),r(W,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:n(()=>[t("div",st,[(l(!0),u(f,null,b(D.value.targetNumAppearances.keys(),e=>{var s;return l(),u("div",{class:F(["col-4 col-md-2",{predict:(s=i.predictResult.tailSet)==null?void 0:s.includes(e)}]),key:e},[t("div",ut,[t("span",at,o(e),1),_(" ("+o(D.value.targetNumAppearances.get(e))+"\u6B21) ",1)])],2)}),128))])]),_:1}),D.value.totalPages>1?(l(),u("div",ot,[r(ye,{modelValue:S.value,"onUpdate:modelValue":a[3]||(a[3]=e=>S.value=e),max:D.value.totalPages,input:!0},null,8,["modelValue","max"])])):P("",!0),t("div",rt,[D.value.pageItems.length===0?(l(),u("div",it,[r(W,{flat:"",bordered:""},{default:n(()=>[r(J,null,{default:n(()=>a[9]||(a[9]=[t("div",{class:"text-h6 text-center"},"\u6C92\u6709\u7B26\u5408\u689D\u4EF6\u7684\u7D50\u679C",-1)])),_:1})]),_:1})])):P("",!0),(l(!0),u(f,null,b(D.value.pageItems,(e,s)=>(l(),u("div",{key:s,class:"col-12 q-my-sm"},[r(W,{flat:"",bordered:""},{default:n(()=>[r(J,null,{default:n(()=>[t("div",nt,[t("div",dt,[t("div",null,[a[10]||(a[10]=_(" \u958B\u51FA ")),(l(!0),u(f,null,b(e.firstNumbers,c=>(l(),q(ie,{key:c,color:"primary","text-color":"white",size:"lg",dense:""},{default:n(()=>[_(o(c)+"\u5C3E ",1)]),_:2},1024))),128)),_(" \u4E0B"+o(e.gap)+"\u671F\u958B ",1),(l(!0),u(f,null,b(e.secondNumbers,c=>(l(),q(ie,{key:c,color:"secondary","text-color":"white",size:"lg",dense:""},{default:n(()=>[_(o(c)+"\u5C3E ",1)]),_:2},1024))),128)),_(" \u518D\u4E0B"+o(e.targetGap)+"\u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),u(f,null,b(e.targetNumbers,c=>(l(),q(ie,{key:c,color:"accent","text-color":"white",size:"lg",dense:""},{default:n(()=>[_(o(c)+"\u5C3E ",1)]),_:2},1024))),128))])]),t("div",ct,[t("div",mt,[t("div",pt," \u5DF2\u9023\u7E8C\u62D6\u51FA"+o(e.consecutiveHits)+"\u6B21 ",1)])]),t("div",vt,[r(re,{dense:"",color:"primary",icon:"visibility",label:"\u6AA2\u8996\u8A73\u60C5",class:"text-subtitle1 text-bold",onClick:c=>i.$emit("view-detail",e)},null,8,["onClick"])])])]),_:2},1024)]),_:2},1024)]))),128))]),D.value.totalPages>1?(l(),u("div",ft,[r(ye,{modelValue:S.value,"onUpdate:modelValue":a[4]||(a[4]=e=>S.value=e),max:D.value.totalPages,input:!0},null,8,["modelValue","max"])])):P("",!0)]),_:1}),r(fe,{name:"2"},{default:n(()=>[t("div",gt,[a[12]||(a[12]=t("span",{class:"text-h6 q-mr-sm"}," \u5C3E\u6578\u7D44\u5408\uFF1A ",-1)),r(le,{modelValue:h.value,"onUpdate:modelValue":a[5]||(a[5]=e=>h.value=e),options:ne,"map-options":"","emit-value":"",outlined:"",dense:""},null,8,["modelValue"])]),t("div",bt,[t("label",_t," \u5C3E\u6578\u7D44\u5408\u51FA\u73FE\u6B21\u6578 ("+o(i.drawResults.length)+"\u671F) ",1)]),r(W,{flat:"",bordered:"",class:"q-pa-sm q-mb-md text-subtitle1 appearance"},{default:n(()=>[t("div",yt,[(l(!0),u(f,null,b(Z.value.keys(),e=>(l(),u("div",{class:F(["col-4 col-md-2",{predict:K(e)}]),key:e},[t("div",wt,[(l(!0),u(f,null,b(e.split(","),s=>(l(),u("span",{class:"ball tail-number",key:s},o(s),1))),128)),_(" ("+o(Z.value.get(e))+"\u6B21) ",1)])],2))),128))])]),_:1})]),_:1}),r(fe,{name:"3"},{default:n(()=>[r(Te,{rows:i.drawResults,columns:j,"rows-per-page-options":[0],"hide-bottom":"",separator:"cell",class:"q-mt-lg"},Fe({header:n(e=>[B?P("",!0):(l(),q(we,{key:0,props:e,class:"bg-primary text-white"},{default:n(()=>[(l(!0),u(f,null,b(e.cols,s=>(l(),q(Me,{key:s.name,props:e},{default:n(()=>[_(o(s.label),1)]),_:2},1032,["props"]))),128))]),_:2},1032,["props"]))]),_:2},[B?{name:"body",fn:n(e=>[(l(),q(W,{square:"",bordered:"",key:e.row.period},{default:n(()=>[r(J,{class:"q-px-none q-py-sm"},{default:n(()=>[t("div",Ft,[t("div",Dt,[t("span",null,o(e.row.period)+"\u671F ",1),a[24]||(a[24]=t("span",null," | ",-1)),t("span",null,o(e.row.drawDate),1)]),t("div",Et,[t("div",qt,[(l(!0),u(f,null,b(e.row.draw_number_size,s=>(l(),u("div",{key:s,class:"col-auto"},[(l(),u("div",{class:"ball",key:s},o(p(w)(s)),1))]))),128)),e.row.special_number?(l(),u("div",xt,[(l(),u("div",{class:"ball special-number",key:e.row.special_number},o(p(w)(e.row.special_number)),1))])):P("",!0)])]),t("div",Bt,[t("div",At,[(l(),u(f,null,b([1,2,3,4,5,6,7,8,9,0],s=>t("div",{class:"col-1 text-center",style:{border:"1px solid black"},key:s},[t("div",Pt," \u5C3E"+o(s),1),t("div",null,[(l(!0),u(f,null,b(e.row.tails.get(s),c=>(l(),u("span",{key:c,class:F(["text-h6",{"text-negative":c===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(c)),1),a[25]||(a[25]=t("br",null,null,-1))],2))),128))])])),64))])])])]),_:2},1024)]),_:2},1024))]),key:"1"}:{name:"body",fn:n(e=>[r(we,{props:e},{default:n(()=>[r(M,{key:"period",props:e},{default:n(()=>[t("div",kt,[_(o(e.row.period)+" ",1),a[13]||(a[13]=t("br",null,null,-1)),_(" "+o(e.row.draw_date),1)])]),_:2},1032,["props"]),r(M,{key:"draw_number_size",props:e},{default:n(()=>[t("div",ht,[(l(!0),u(f,null,b(e.row.draw_number_size,s=>(l(),u("div",{key:s,class:"col-auto"},[(l(),u("div",{class:"ball",key:s},o(p(w)(s)),1))]))),128)),e.row.special_number?(l(),u("div",Ct,[(l(),u("div",{class:"ball special-number",key:e.row.special_number},o(p(w)(e.row.special_number)),1))])):P("",!0)])]),_:2},1032,["props"]),r(M,{key:"tail1",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(1),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s))+" ",1),a[14]||(a[14]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail2",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(2),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[15]||(a[15]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail3",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(3),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[16]||(a[16]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail4",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(4),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[17]||(a[17]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail5",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(5),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[18]||(a[18]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail6",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(6),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[19]||(a[19]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail7",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(7),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[20]||(a[20]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail8",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(8),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[21]||(a[21]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail9",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(9),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[22]||(a[22]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"]),r(M,{key:"tail0",props:e},{default:n(()=>[(l(!0),u(f,null,b(e.row.tails.get(0),s=>(l(),u("span",{key:s,class:F(["text-h6",{"text-negative":s===e.row.special_number&&!i.isSuperLotto}])},[_(o(p(w)(s)),1),a[23]||(a[23]=t("br",null,null,-1))],2))),128))]),_:2},1032,["props"])]),_:2},1032,["props"])]),key:"0"}]),1032,["rows"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}))}});const Vt={class:"text-h6 q-mb-md"},St={class:"text-h6 q-mb-md"},Nt={class:"row text-h6 q-mb-md"},Rt={class:"col-auto"},Lt={class:"row balls"},Qt={key:0,class:"col-auto"},zt={key:0,class:"row text-h6 q-mb-md"},Tt={class:"col-auto"},Mt={class:"row balls"},It={key:0,class:"col-auto"},Ut={key:1,class:"row text-h6"},jt={class:"col-auto"},Ot={class:"row balls"},Gt={key:0,class:"col-auto"},Ht={key:2,class:"predict-section"},Wt={class:"row text-h6 q-mt-sm"},Jt={class:"col-auto"},Zt={key:0,class:"predict-period"},Kt={class:"predict-date"},Xt={class:"row balls"},Yt={key:0,class:"col-auto"},el={key:1,class:"col-auto pending-draw"},tl=ge({__name:"TailFollowDetail",props:{modelValue:{type:Boolean},results:{},selectedDetail:{},occurrences:{},predictResult:{}},emits:["update:modelValue"],setup(ce,{emit:k}){const x=ce,B=he(),Q=ke({get:()=>x.modelValue,set:d=>S("update:modelValue",d)}),S=k,D=d=>{if(!d)return"";const g=x.results.find(A=>A.period==d);return g==null?void 0:g.draw_date},h=d=>{if(!d)return{numbers:[],specialNumber:0};const g=x.results.find(A=>A.period==d);return{numbers:g==null?void 0:g.draw_number_size,specialNumber:g!=null&&g.special_number?Number(g.special_number):0}},ne=d=>{var O,K,v;if(!d)return[];const g=d.firstNumbers.join(",")+"-"+d.secondNumbers.join(",")+"-"+d.gap+"-"+d.targetGap,A=(K=(O=x.occurrences.get(g))==null?void 0:O.periods)!=null?K:[],z=A.filter(C=>C.targetPeriod===void 0||C.targetPeriod===null||C.targetPeriod.trim()===""),I=A.filter(C=>C.targetPeriod!==void 0&&C.targetPeriod!==null&&C.targetPeriod.trim()!==""),j=(v=d.consecutiveHits)!=null?v:0;let $=[];return j>0&&I.length>0&&($=I.sort((X,R)=>{const T=parseInt(X.targetPeriod),G=parseInt(R.targetPeriod);return isNaN(T)||isNaN(G)?X.targetPeriod.localeCompare(R.targetPeriod):G-T}).slice(0,j)),z.length>0&&($=[...z,...$]),$},Z=d=>d.targetPeriod===void 0;return(d,g)=>(l(),q(De,{modelValue:Q.value,"onUpdate:modelValue":g[1]||(g[1]=A=>Q.value=A)},{default:n(()=>[r(W,{style:{"max-width":"100%",width:"800px"}},{default:n(()=>[r(J,{class:"row items-center"},{default:n(()=>[g[2]||(g[2]=t("div",{class:"text-h6"},"\u8A73\u7D30\u8CC7\u6599",-1)),r(Ue),r(re,{icon:"close",flat:"",round:"",dense:"",onClick:g[0]||(g[0]=A=>Q.value=!1)})]),_:1}),r(J,{class:"q-pa-md"},{default:n(()=>{var A,z,I,j,$,O,K;return[t("div",Vt,[g[3]||(g[3]=_(" \u958B\u51FA ")),(l(!0),u(f,null,b((A=d.selectedDetail)==null?void 0:A.firstNumbers,v=>(l(),q(ie,{key:v,color:"primary","text-color":"white",class:"text-h6"},{default:n(()=>[_(o(v)+"\u5C3E ",1)]),_:2},1024))),128)),_(" \u4E0B"+o((z=d.selectedDetail)==null?void 0:z.gap)+"\u671F\u958B ",1),(l(!0),u(f,null,b((I=d.selectedDetail)==null?void 0:I.secondNumbers,v=>(l(),q(ie,{key:v,color:"secondary","text-color":"white",class:"text-h6"},{default:n(()=>[_(o(v)+"\u5C3E ",1)]),_:2},1024))),128)),_(" \u518D\u4E0B "+o((j=d.selectedDetail)==null?void 0:j.targetGap)+" \u671F\u9810\u6E2C\u62D6\u51FA ",1),(l(!0),u(f,null,b(($=d.selectedDetail)==null?void 0:$.targetNumbers,v=>(l(),q(ie,{key:v,color:"accent","text-color":"white",class:"text-h6"},{default:n(()=>[_(o(v)+"\u5C3E ",1)]),_:2},1024))),128))]),t("div",St," \u5DF2\u9023\u7E8C\u62D6\u51FA"+o((K=(O=d.selectedDetail)==null?void 0:O.consecutiveHits)!=null?K:0)+"\u6B21 ",1),t("div",null,[(l(!0),u(f,null,b(ne(d.selectedDetail),(v,C)=>(l(),q(W,{key:C,class:"q-mb-md",style:Ee({"background-color":Z(v)?"#e8f5e8":"#fefefe"})},{default:n(()=>[r(J,null,{default:n(()=>{var X,R,T,G,i,a;return[t("div",Nt,[t("div",Rt,[t("div",null,"\u7B2C "+o(v.firstPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o(D(v.firstPeriod)),1)]),t("div",Lt,[(l(!0),u(f,null,b(h(v.firstPeriod).numbers,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"first-num":(c=d.selectedDetail)==null?void 0:c.firstNumbers.includes(e%10)}])},o(p(w)(e)),3)])}),128)),h(v.firstPeriod).specialNumber?(l(),u("div",Qt,[t("div",{class:F(["ball special-number",{"first-num":((X=d.selectedDetail)==null?void 0:X.firstNumbers.includes(h(v.firstPeriod).specialNumber%10))&&!p(B).isSuperLotto}])},o(p(w)(h(v.firstPeriod).specialNumber)),3)])):P("",!0)])]),v.secondPeriod?(l(),u("div",zt,[t("div",Tt,[t("div",null,"\u7B2C "+o(v.secondPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o((R=D(v.secondPeriod))!=null?R:"\u672A\u958B\u734E"),1)]),t("div",Mt,[(l(!0),u(f,null,b(h(v.secondPeriod).numbers,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"second-num":(c=d.selectedDetail)==null?void 0:c.secondNumbers.includes(e%10)}])},o(p(w)(e)),3)])}),128)),h(v.secondPeriod).specialNumber?(l(),u("div",It,[t("div",{class:F(["ball special-number",{"second-num":((T=d.selectedDetail)==null?void 0:T.secondNumbers.includes(h(v.secondPeriod).specialNumber%10))&&!p(B).isSuperLotto}])},o(p(w)(h(v.secondPeriod).specialNumber)),3)])):P("",!0)])])):P("",!0),v.targetPeriod?(l(),u("div",Ut,[t("div",jt,[t("div",null,"\u7B2C "+o(v.targetPeriod)+" \u671F",1),t("div",null," \u958B\u734E\u65E5\u671F\uFF1A"+o((G=D(v.targetPeriod))!=null?G:"\u672A\u958B\u734E"),1)]),t("div",Ot,[(l(!0),u(f,null,b(h(v.targetPeriod).numbers,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"target-num":(c=d.selectedDetail)==null?void 0:c.targetNumbers.includes(e%10)}])},o(p(w)(e)),3)])}),128)),h(v.targetPeriod).specialNumber?(l(),u("div",Gt,[t("div",{class:F(["ball special-number",{"target-num":((i=d.selectedDetail)==null?void 0:i.targetNumbers.includes(h(v.targetPeriod).specialNumber%10))&&!p(B).isSuperLotto}])},o(p(w)(h(v.targetPeriod).specialNumber)),3)])):P("",!0)])])):(l(),u("div",Ht,[t("div",Wt,[t("div",Jt,[d.predictResult.period?(l(),u("div",Zt," \u7B2C "+o(d.predictResult.period)+" \u671F ",1)):P("",!0),t("div",Kt,[d.predictResult.period?(l(),u(f,{key:0},[_(" \u5BE6\u969B\u958B\u734E\u65E5\u671F\uFF1A"+o(d.predictResult.draw_date),1)],64)):(l(),u(f,{key:1},[_(" \u9810\u6E2C\u671F\u865F\uFF1A\u5C1A\u672A\u958B\u734E ")],64))])]),t("div",Xt,[d.predictResult.period?(l(),u(f,{key:0},[(l(!0),u(f,null,b(d.predictResult.draw_number_size,(e,s)=>{var c;return l(),u("div",{class:"col-auto",key:s},[t("div",{class:F(["ball",{"target-num":(c=d.selectedDetail)==null?void 0:c.targetNumbers.includes(e%10)}])},o(p(w)(e)),3)])}),128)),d.predictResult.special_number?(l(),u("div",Yt,[t("div",{class:F(["ball special-number",{"target-num":((a=d.selectedDetail)==null?void 0:a.targetNumbers.includes(d.predictResult.special_number%10))&&!p(B).isSuperLotto}])},o(p(w)(d.predictResult.special_number)),3)])):P("",!0)],64)):(l(),u("div",el,[r(qe,{name:"schedule",size:"lg"}),g[4]||(g[4]=t("span",null,"\u5C1A\u672A\u958B\u734E",-1))]))])])]))]}),_:2},1024)]),_:2},1032,["style"]))),128))])]}),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var ll=je(tl,[["__scopeId","data-v-164dfad4"]]);const sl={class:"row lto-ref q-mb-sm"},ul={class:"col-12 col-sm-4 self-center text-h6"},al={class:"col-12 col-sm-6 self-center text-subtitle1"},ol={class:"row balls"},rl={class:"ball"},il={key:0,class:"col-auto"},nl={class:"row q-mb-md"},dl={class:"col"},cl={key:1,class:"row q-mb-md"},ml={class:"row q-mb-md"},pl={class:"col-12 col-sm-4 q-pa-sm"},vl={class:"col-12 col-sm-4 q-pa-sm"},fl={class:"col-12 col-sm-4 q-pa-sm"},gl={class:"row q-mb-md"},bl={class:"col-12 col-sm-4"},_l={class:"q-pa-sm"},yl={class:"col-12 col-sm-4"},wl={class:"q-pa-sm"},kl={class:"col-12 col-sm-4"},hl={class:"q-pa-sm"},Cl={class:"text-center q-mb-sm"},Hl=ge({__name:"TailPage",setup(ce){const k=Le(),x=he(),B=y(x.getLotto),Q=ze(),S=y(!1),D=()=>{S.value=!0},h=()=>{S.value=!1},ne=()=>{S.value=!1};oe(()=>x.getLotto,V=>{V&&(B.value=V)}),oe(()=>{var V;return(V=B.value)==null?void 0:V.period},()=>{C.value=[]});const Z=[{label:"\u4E00\u661F\u7D44\u5408",value:1},{label:"\u4E8C\u661F\u7D44\u5408",value:2},{label:"\u4E09\u661F\u7D44\u5408",value:3},{label:"\u56DB\u661F\u7D44\u5408",value:4},{label:"\u4E94\u661F\u7D44\u5408",value:5}],d=y(1),g=y(1),A=y(1),z=Array.from({length:21},(V,m)=>({label:`${m+10}\u671F`,value:m+10})),I=y(20);let j=y(Array.from({length:991},(V,m)=>({label:`${m+10}\u671F`,value:m+10})));const $=y(50),O=(V,m,Y)=>{const ee=parseInt(V,10);(ee<10||ee>1e3)&&Y(),m(()=>{j.value=Array.from({length:991},(H,se)=>se+10).filter(H=>H.toString().startsWith(V)).map(H=>({label:`${H.toString()}\u671F`,value:H}))})},K=Array.from({length:15},(V,m)=>({label:`\u4E0B${m+1}\u671F`,value:m+1})),v=y(1),C=y([]),X=y("super_lotto638"),R=y([]),T=y(new Map),G=y(new Map),i=y({period:"",draw_date:"",draw_number_appear:[],draw_number_size:[],tails:new Map}),a=y([]),e=y(!1),s=async()=>{var V,m,Y;try{X.value=x.drawType,e.value=x.isSuperLotto,k.startCalculating(),c();const ee=await _e.getLottoList({draw_type:x.getDrawType,date_end:(m=(V=B.value)==null?void 0:V.draw_date)!=null?m:"",limit:$.value});C.value=ee.data;const H=await _e.getLottoPredict({draw_type:x.getDrawType,draw_date:(Y=x.getLotto)==null?void 0:Y.draw_date,ahead_count:v.value});i.value=H.data,i.value.period&&(i.value.tailSet=Q.getTailSet(i.value,e.value)),a.value=C.value.map(U=>{const te=new Set;for(let E of U.draw_number_size)te.add(E%10);U.special_number&&!x.isSuperLotto&&te.add(U.special_number%10);const pe=Array.from(te).sort((E,be)=>E===0?1:be===0?-1:E-be);return{period:String(U.period),numbers:[...pe]}}).reverse(),Q.init({firstGroupSize:d.value,secondGroupSize:g.value,targetGroupSize:A.value,maxRange:I.value,lookAheadCount:v.value},a.value);let se=Date.now();const me=8,ae=await Q.analyzeWithProgress(async U=>{const te=Date.now();te-se>=me&&(await k.updateProgress(U),se=te)},U=>{k.addWarning(U)});R.value=ae.data,T.value=ae.occurrences,G.value=ae.matchData}catch(ee){console.error(ee)}finally{L()}},c=()=>{C.value=[]},L=()=>{Q.stopAnalyzer(),k.stopCalculating()},N=y(!1),de=y(null),ue=V=>{de.value=V,N.value=!0};return(V,m)=>(l(),q(Se,{class:"justify-center"},{default:n(()=>[r(W,{class:"q-mx-auto q-py-sm"},{default:n(()=>[r(J,null,{default:n(()=>{var Y,ee,H,se,me,ae,U,te,pe;return[(Y=p(x).getLotto)!=null&&Y.draw_date?(l(),u(f,{key:0},[t("div",sl,[t("div",ul,[t("div",null,o(p(x).getDrawLabel),1),m[7]||(m[7]=t("span",null,"\u53C3\u8003\u671F\u865F\uFF1A",-1)),t("span",null,o((ee=B.value)==null?void 0:ee.period),1),t("span",null,"\uFF08"+o((H=B.value)==null?void 0:H.draw_date)+"\uFF09",1)]),t("div",al,[t("div",ol,[(l(!0),u(f,null,b((se=B.value)==null?void 0:se.draw_number_size,E=>(l(),u("div",{class:"col-auto",key:E},[t("div",rl,o(p(w)(E)),1)]))),128)),(me=B.value)!=null&&me.special_number?(l(),u("div",il,[(l(),u("div",{class:"ball special-number",key:(ae=B.value)==null?void 0:ae.special_number},o(p(w)((U=B.value)==null?void 0:U.special_number)),1))])):P("",!0)])])]),t("div",nl,[t("div",dl,[S.value?(l(),q(re,{key:1,type:"button",label:"\u53D6\u6D88\u9078\u64C7",color:"negative",class:"text-h6 q-ml-md",onClick:ne})):(l(),q(re,{key:0,type:"button",label:"\u91CD\u65B0\u9078\u64C7",color:"primary",class:"text-h6 q-ml-md",onClick:D}))])])],64)):(l(),u("div",cl,m[8]||(m[8]=[t("div",{class:"text-h6"},"\u203B\u8ACB\u9078\u64C7\u53C3\u8003\u671F\u865F",-1)]))),r(xe,{class:"q-mb-md"}),!S.value&&((te=p(x).getLotto)==null?void 0:te.draw_date)?(l(),u(f,{key:2},[m[14]||(m[14]=t("div",{class:"row q-mb-md"},[t("div",{class:"col-12 text-h5 text-weight-bolder text-center"}," \u5C3E\u6578\u5206\u6790\u8A2D\u5B9A ")],-1)),t("div",ml,[m[9]||(m[9]=t("div",{class:"col-12 text-h6 text-weight-bold"},"\u5C3E\u6578\u62D6\u724C\u7D44\u5408",-1)),t("div",pl,[r(le,{outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":m[0]||(m[0]=E=>d.value=E),options:Z,"emit-value":"","map-options":""},null,8,["modelValue"])]),t("div",vl,[r(le,{outlined:"",dense:"",modelValue:g.value,"onUpdate:modelValue":m[1]||(m[1]=E=>g.value=E),options:Z,"emit-value":"","map-options":""},null,8,["modelValue"])]),t("div",fl,[r(le,{outlined:"",dense:"",modelValue:A.value,"onUpdate:modelValue":m[2]||(m[2]=E=>A.value=E),options:Z,"emit-value":"","map-options":""},null,8,["modelValue"])])]),t("div",gl,[t("div",bl,[m[11]||(m[11]=t("div",{class:"text-h6 text-weight-bold"},"\u63A8\u7B97\u671F\u6578",-1)),t("div",_l,[r(le,{outlined:"",dense:"",modelValue:$.value,"onUpdate:modelValue":m[3]||(m[3]=E=>$.value=E),options:p(j),"input-debounce":"0","use-input":"","hide-selected":"","fill-input":"",onFilter:O,"emit-value":"","map-options":""},{"no-option":n(()=>[r(Ae,null,{default:n(()=>[r(Pe,{class:"text-grey"},{default:n(()=>m[10]||(m[10]=[_(" \u7121\u53EF\u7528\u9078\u9805 ")])),_:1})]),_:1})]),_:1},8,["modelValue","options"])])]),t("div",yl,[m[12]||(m[12]=t("div",{class:"text-h6 text-weight-bold"},"\u6700\u5927\u5340\u9593",-1)),t("div",wl,[r(le,{outlined:"",dense:"",modelValue:I.value,"onUpdate:modelValue":m[4]||(m[4]=E=>I.value=E),options:p(z),"emit-value":"","map-options":""},null,8,["modelValue","options"])])]),t("div",kl,[m[13]||(m[13]=t("div",{class:"text-h6 text-weight-bold"},"\u9810\u6E2C\u671F\u6578",-1)),t("div",hl,[r(le,{outlined:"",dense:"",modelValue:v.value,"onUpdate:modelValue":m[5]||(m[5]=E=>v.value=E),options:p(K),"emit-value":"","map-options":""},null,8,["modelValue","options"])])])]),r(Be,{align:"right",class:"q-my-lg q-py-none q-px-md"},{default:n(()=>[p(k).isCalculating?(l(),q(re,{key:0,type:"button",label:"\u4E2D\u65B7\u8A08\u7B97",color:"negative",class:"text-h6 q-mr-md",onClick:L})):P("",!0),r(re,{type:"button",label:"\u958B\u59CB\u8A08\u7B97",color:"positive",class:"text-h6 q-px-lg q-py-sm",onClick:s,loading:p(k).isCalculating},{loading:n(()=>[r($e)]),_:1},8,["loading"])]),_:1})],64)):(l(),q(Qe,{key:3,"draw-type-query":p(x).drawType,"date-query":((pe=B.value)==null?void 0:pe.draw_date)||"",isSelectRef:!0,onSelectRef:h},null,8,["draw-type-query","date-query"]))]}),_:1}),p(k).isCalculating?(l(),q(J,{key:0},{default:n(()=>[t("div",Cl,o(p(k).progressMessage),1),r(Ve,{rounded:"",size:"md",value:p(k).progress,"animation-speed":50,color:"primary",class:"q-mb-xs"},null,8,["value"])]),_:1})):P("",!0)]),_:1}),!p(k).isCalculating&&C.value.length>0?(l(),u(f,{key:0},[r($t,{"is-super-lotto":e.value,"draw-results":C.value,"predict-result":i.value,"draw-tail-results":a.value,"rd-results":R.value,"occurrence-results":T.value,"page-size":50,onViewDetail:ue},null,8,["is-super-lotto","draw-results","predict-result","draw-tail-results","rd-results","occurrence-results"]),r(ll,{modelValue:N.value,"onUpdate:modelValue":m[6]||(m[6]=Y=>N.value=Y),results:C.value,"predict-result":i.value,"selected-detail":de.value,occurrences:T.value},null,8,["modelValue","results","predict-result","selected-detail","occurrences"])],64)):P("",!0)]),_:1}))}});export{Hl as default};
