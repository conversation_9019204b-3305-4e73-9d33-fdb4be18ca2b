<template>
  <q-card class="q-mt-md">
    <q-card-section>
      <!-- Tabs -->
      <q-tabs
        v-model="tab"
        dense
        align="justify"
        class="text-h6"
        active-color="primary"
        indicator-color="primary"
      >
        <q-tab name="1" label="版路分析" />
      </q-tabs>

      <!-- Panels -->
      <q-tab-panels v-model="tab">
        <!-- 版路分析 -->
        <q-tab-panel name="1">
          <!-- 預測結果 -->
          <q-card
            bordered
            class="ball-card full-width q-my-lg"
            v-if="predictResult.period"
          >
            <q-card-section>
              <div class="row q-gutter-y-md">
                <div class="col-12 col-sm-4 draw-title text-center">
                  <div class="text-h6">預測開獎結果</div>
                  <div class="text-period">
                    第 {{ predictResult.period }} 期
                  </div>
                  <div class="text-draw-date">
                    開獎日期：{{ predictResult.draw_date }}
                  </div>
                </div>

                <div class="col-12 col-sm-6 self-center">
                  <div class="row justify-center">
                    <template
                      v-for="number in predictResult.draw_number_size"
                      :key="number"
                    >
                      <div class="col-auto">
                        <div class="ball" :key="number">
                          {{ paddingZero(number) }}
                        </div>
                      </div>
                    </template>
                    <div class="col-auto" v-if="predictResult.special_number">
                      <div
                        class="ball special-number"
                        :key="predictResult.special_number"
                      >
                        {{ paddingZero(predictResult.special_number) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- 篩選 -->
          <!-- 版路分析篩選 -->
          <div class="row q-my-md q-gutter-sm items-center">
            <div class="col-12 text-h6">版路分析篩選</div>
            <!-- 準確次數 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="accuracy"
                :options="accuracyOpts"
                map-options
                emit-value
              />
            </div>
            <!-- 準確率 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="accuracyRate"
                :options="accuracyRateOpts"
                map-options
                emit-value
              />
            </div>
          </div>
          <!-- 尾數篩選 -->
          <div class="row q-my-md q-gutter-sm items-center">
            <div class="col-12 text-h6">尾數分析篩選</div>
            <!-- 準確次數 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="tailAccuracy"
                :options="tailAccuracyOpts"
                map-options
                emit-value
              />
            </div>
            <!-- 準確率 -->
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="tailAccuracyRate"
                :options="tailAccuracyRateOpts"
                map-options
                emit-value
              />
            </div>
          </div>

          <!-- 統計方式 -->
          <!-- <div class="row q-mb-xl q-gutter-sm items-center">
            <div class="col-12 text-h6">統計方式</div>
            <div class="col-sm-auto">
              <q-select
                outlined
                dense
                v-model="statisticType"
                :options="statisticTypeOpts"
                map-options
                emit-value
              />
            </div>
          </div> -->

          <!-- 拖牌尾數 -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold"> 拖牌：尾數預測開出機率 (由高至低排序) </label>
          </div>
          <q-card flat bordered class="q-mb-lg q-pa-sm appearance">
            <div class="row q-col-gutter-xs">
              <div
                class="col-4 col-md-2 text-h6"
                v-for="[tailNumber] in rdTailAppearances.entries()"
                :key="tailNumber"
                :class="{
                  predict: isPredictTailNumber(tailNumber),
                }"
              >
                <span class="ball tail-number">
                  {{ tailNumber }}
                </span>
              </div>
            </div>
          </q-card>

          <!-- 尾數分析 -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold"> 尾數：預測開出機率 (由高至低排序) </label>
          </div>
          <q-card flat bordered class="q-mb-lg q-pa-sm appearance">
            <div class="row q-col-gutter-xs">
              <div
                class="col-4 col-md-2 text-h6"
                v-for="[tailNumber] in tailRdScoreMap.entries()"
                :key="tailNumber"
                :class="{
                  predict: isPredictTailNumber(tailNumber),
                }"
              >
                <span class="ball tail-number">
                  {{ tailNumber }}
                </span>
              </div>
            </div>
          </q-card>

          <!-- 版路尾數 -->
          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold">
              版路：尾數預測開出機率（由高至低排序）
            </label>
          </div>
          <q-card flat bordered class="q-mb-lg q-pa-sm appearance">
            <div class="row q-col-gutter-xs">
              <div
                class="col-4 col-md-2 text-h6"
                v-for="[tailNumber] in tailScoreMap.entries()"
                :key="tailNumber"
                :class="{
                  predict: isPredictTailNumber(tailNumber),
                }"
              >
                <span class="ball tail-number">
                  {{ tailNumber }}
                </span>
              </div>
            </div>
          </q-card>

          <div class="row q-mb-sm">
            <label class="col text-h6 text-bold"> 綜合比對預測尾數 </label>
          </div>
          <q-card flat bordered class="q-mb-lg q-pa-sm appearance">
            <!-- 拖牌+尾數 -->
            <div class="row q-gutter-sm items-center q-mb-md">
              <span class="text-h6">拖牌+尾數：</span>
              <span
                v-for="[, tailNumber] in tailMatchResults.entries()"
                :key="tailNumber"
                :class="{
                  predict: isPredictTailNumber(tailNumber),
                }"
              >
                <span class="ball tail-number q-mx-xs">
                  {{ tailNumber }}
                </span>
              </span>
            </div>
            <!-- 拖牌+版路 -->
            <div class="row q-gutter-sm items-center q-mb-md">
              <span class="text-h6">拖牌+版路：</span>
              <span
                v-for="[, tailNumber] in tailMatchResults2.entries()"
                :key="tailNumber"
                :class="{
                  predict: isPredictTailNumber(tailNumber),
                }"
              >
                <span class="ball tail-number q-mx-xs">
                  {{ tailNumber }}
                </span>
              </span>
            </div>
            <!-- 尾數+版路 -->
            <div class="row q-gutter-sm items-center">
              <span class="text-h6">尾數+版路：</span>
              <span
                v-for="[, tailNumber] in tailMatchResults3.entries()"
                :key="tailNumber"
                :class="{
                  predict: isPredictTailNumber(tailNumber),
                }"
              >
                <span class="ball tail-number q-mx-xs">
                  {{ tailNumber }}
                </span>
              </span>
            </div>
          </q-card>

          <q-table
            :rows="drawResults"
            :columns="columns"
            :rows-per-page-options="[0]"
            hide-bottom
            hide-pagination
            virtual-scroll
            separator="cell"
            class="sticky-virtscroll-table q-mt-lg"
          >
            <!-- Header -->
            <template v-slot:header="props">
              <q-tr :props="props" class="bg-primary text-white">
                <q-th v-for="col in props.cols" :key="col.name" :props="props">
                  {{ col.label }}
                </q-th>
              </q-tr>
            </template>
            <!-- Body -->
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td key="draw_date" :props="props">
                  <div class="text-subtitle1">
                    {{ props.row.draw_date }}
                  </div>
                </q-td>
                <q-td
                  key="tail1"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(1)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(1)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }} <br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail2"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(2)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(2)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail3"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(3)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(3)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail4"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(4)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(4)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail5"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(5)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(5)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail6"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(6)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(6)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail7"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(7)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(7)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail8"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(8)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(8)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail9"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(9)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(9)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>
                <q-td
                  key="tail0"
                  :props="props"
                  class="fixed-col"
                  :style="{
                    backgroundColor: emptyTailBg(props.row.tails?.get(0)),
                  }"
                >
                  <div class="row justify-center">
                    <span
                      v-for="number in props.row.tails?.get(0)"
                      :key="number"
                      class="text-h6 ball col-6"
                      :class="{
                        'special-number':
                          number === props.row.special_number && !isSuperLotto,
                      }"
                    >
                      {{ paddingZero(number) }}<br />
                    </span>
                  </div>
                </q-td>

                <q-td key="draw_results" :props="props">
                  <div class="row no-wrap">
                    <span
                      v-for="number in props.row.draw_number_size"
                      :key="number"
                      class="text-subtitle1 ball"
                    >
                      {{ paddingZero(number) }}
                    </span>
                    <span
                      v-if="props.row.special_number"
                      class="text-subtitle1 ball special-number"
                    >
                      {{ paddingZero(props.row.special_number) }}
                    </span>
                  </div>
                </q-td>

                <q-td key="tail_set" :props="props">
                  <div class="row no-wrap">
                    <span
                      v-for="tail in props.row.tailSet"
                      :key="tail"
                      class="text-subtitle1 ball tail-number"
                    >
                      {{ tail }}
                    </span>
                  </div>
                </q-td>
              </q-tr>
            </template>
          </q-table>
        </q-tab-panel>
      </q-tab-panels>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { LottoItem } from '@/api/modules/lotto';
import { StatResult } from '@/models/types';
import { useLotteryAnalysis } from '@/composables/useLotteryAnalysis';
import { paddingZero } from '@/utils';

const props = defineProps<{
  isSuperLotto: boolean;
  drawResults: LottoItem[];
  predictResult: LottoItem;
  rdResults: StatResult[];
  tailRdResults: StatResult[];
}>();

const tab = ref('1');

const analysis = useLotteryAnalysis();
let tailScoreMap = ref(new Map<number, number>());

// 版路分析
const columns = [
  {
    name: 'draw_date',
    label: '日期',
    field: 'draw_date',
    align: 'center' as const,
  },
  {
    name: 'tail1',
    label: '尾1',
    field: 'tail1',
    align: 'center' as const,
  },
  {
    name: 'tail2',
    label: '尾2',
    field: 'tail2',
    align: 'center' as const,
  },
  {
    name: 'tail3',
    label: '尾3',
    field: 'tail3',
    align: 'center' as const,
  },
  {
    name: 'tail4',
    label: '尾4',
    field: 'tail4',
    align: 'center' as const,
  },
  {
    name: 'tail5',
    label: '尾5',
    field: 'tail5',
    align: 'center' as const,
  },
  {
    name: 'tail6',
    label: '尾6',
    field: 'tail6',
    align: 'center' as const,
  },
  {
    name: 'tail7',
    label: '尾7',
    field: 'tail7',
    align: 'center' as const,
  },
  {
    name: 'tail8',
    label: '尾8',
    field: 'tail8',
    align: 'center' as const,
  },
  {
    name: 'tail9',
    label: '尾9',
    field: 'tail9',
    align: 'center' as const,
  },
  {
    name: 'tail0',
    label: '尾0',
    field: 'tail0',
    align: 'center' as const,
  },
  {
    name: 'draw_results',
    label: '獎號',
    field: 'draw_results',
    align: 'center' as const,
  },
  {
    name: 'tail_set',
    label: '尾數',
    field: 'tail_set',
    align: 'center' as const,
  },
];

onMounted(() => {
  doCalculating();
});

const tailAppearances = ref(new Map<number, number>()); // 尾數統計次數
const rdTailAppearances = ref(new Map<number, number>()); // 獎號拖牌尾數
const doCalculating = () => {
  // 重置
  tailScoreMap.value.clear();
  tailMatchResults.value = [];
  tailMatchResults2.value = [];
  tailMatchResults3.value = [];

  // 開獎號碼尾數初始
  tailAppearances.value.clear();
  for (const result of props.drawResults) {
    result.tails = new Map<number, number[]>();

    for (let i = 0; i < 10; i++) {
      result.tails.set(i, []);
    }

    let numbers = [...result.draw_number_size];
    if (!props.isSuperLotto && result.special_number) {
      numbers.push(result.special_number);
      numbers = numbers.sort((a, b) => a - b);
    }

    for (const number of numbers) {
      const tail = number % 10;
      result.tails.get(tail)?.push(number);
    }

    result.tailSet = analysis.getTailSet(result, props.isSuperLotto);

    for (const tail of result.tailSet) {
      tailAppearances.value.set(tail, (tailAppearances.value.get(tail) || 0) + 1);
    }
  }

  // 排序
  tailAppearances.value = sortMap(tailAppearances.value);

  // 尾數分數初始
  for (const i of Array(10).keys()) {
    tailScoreMap.value.set(i, 0);
  }

  // 計算獎號拖牌尾數
  doRdScore();
  doTailRdScore();
  doAppearScore();

  formatScore();
  tailScoreMap.value = sortMap(tailScoreMap.value);

  // 尾數配對
  doTailMatch();
};

const accuracy = ref(1);
const accuracyOpts = ref([
  { label: '已連續命中 1 次以上', value: 1 },
  { label: '已連續命中 2 次以上', value: 2 },
  { label: '已連續命中 3 次以上', value: 3 },
  { label: '已連續命中 4 次以上', value: 4 },
  { label: '已連續命中 5 次以上', value: 5 },
]);
const accuracyRate = ref(0.5);
const accuracyRateOpts = ref([
  { label: '準確率 100%', value: 1 },
  { label: '準確率 90% 以上', value: 0.9 },
  { label: '準確率 80% 以上', value: 0.8 },
  { label: '準確率 70% 以上', value: 0.7 },
  { label: '準確率 60% 以上', value: 0.6 },
  { label: '準確率 50% 以上', value: 0.5 },
]);

const tailAccuracy = ref(1);
const maxTailAccuracy = ref(1);
const tailAccuracyOpts = computed(() => {
  return Array.from({ length: maxTailAccuracy.value }, (_, i) => ({
    label: `已連續命中 ${i + 1} 次以上`,
    value: i + 1,
  }));
});
const tailAccuracyRate = ref(0.5);
const tailAccuracyRateOpts = ref([
  { label: '準確率 100%', value: 1 },
  { label: '準確率 90% 以上', value: 0.9 },
  { label: '準確率 80% 以上', value: 0.8 },
  { label: '準確率 70% 以上', value: 0.7 },
  { label: '準確率 60% 以上', value: 0.6 },
  { label: '準確率 50% 以上', value: 0.5 },
]);

// const statisticType = ref('count');
// const statisticTypeOpts = ref([
//   { label: '準確次數統計', value: 'count' },
//   { label: '預測組數統計', value: 'group' },
// ]);

watch([accuracy, accuracyRate, tailAccuracy, tailAccuracyRate], () => {
  doCalculating();
});



// 獎號拖牌
const doRdScore = () => {
  const results = ref<StatResult[]>([]);

  rdTailAppearances.value.clear();

  results.value = props.rdResults.filter(
    (item) => item.consecutiveHits >= accuracy.value
  );

  const rdScoreMap = new Map<number, number>();
  const globalAverage = computeGlobalAverage(results.value);
  let totalCount = 0;

  // 計算獎號尾數出現次數
  for (const result of results.value) {
    let tailSet = new Set<number>();
    for (const number of result.targetNumbers) {
      const tailNumber = number % 10;
      const score = result.consecutiveHits;
      const adjusted = bayesianAdjust(
        result,
        globalAverage.get(tailNumber) || 0
      );

      rdScoreMap.set(
        tailNumber,
        (rdScoreMap.get(tailNumber) || 0) + score + adjusted
      );
      totalCount += result.consecutiveHits;

      tailSet.add(tailNumber);
    }

    for (const tailNumber of tailSet) {
      rdTailAppearances.value.set(
        tailNumber,
        (rdTailAppearances.value.get(tailNumber) || 0) + 1
      );
    }
  }

  // 排序
  rdTailAppearances.value = sortMap(rdTailAppearances.value);

  // 計算分數
  for (const tailNumber of rdScoreMap.keys()) {
    const score = rdScoreMap.get(tailNumber) || 0;
    addScore(tailNumber, score / totalCount);
  }
};

// 尾數拖牌
const tailRdScoreMap = ref(new Map<number, number>());
const doTailRdScore = () => {
  const results = ref<StatResult[]>([]);

  tailRdScoreMap.value.clear();

  results.value = props.tailRdResults.filter(
    (item) => item.consecutiveHits >= tailAccuracy.value
  );

  maxTailAccuracy.value = 1;
  maxTailAccuracy.value = Math.max(
    ...results.value.map((result) => result.consecutiveHits)
  );

  const globalAverage = computeGlobalAverage(results.value);
  let totalCount = 0;

  // 計算尾數出現次數
  for (const result of results.value) {
    for (const tailNumber of result.targetNumbers) {
      const score = result.consecutiveHits;
      const adjusted = bayesianAdjust(
        result,
        globalAverage.get(tailNumber) || 0
      );

      tailRdScoreMap.value.set(
        tailNumber,
        (tailRdScoreMap.value.get(tailNumber) || 0) + score + adjusted
      );
      totalCount += result.consecutiveHits;
    }
  }

  // 排序
  tailRdScoreMap.value = sortMap(tailRdScoreMap.value);

  // 計算分數
  for (const tailNumber of tailRdScoreMap.value.keys()) {
    const score = tailRdScoreMap.value.get(tailNumber) || 0;
    addScore(tailNumber, score / totalCount);
  }
};

// 計算全局機率平均值
const computeGlobalAverage = (results: StatResult[]) => {
  const globalAverage = new Map<number, number>();
  const totalCount = new Map<number, number>();

  // 統計
  for (const result of results) {
    for (const number of result.targetNumbers) {
      const tail = number % 10;
      globalAverage.set(tail, (globalAverage.get(tail) || 0) + number);
      totalCount.set(tail, (totalCount.get(tail) || 0) + 1);
    }
  }

  // 計算平均值
  for (const tail of globalAverage.keys()) {
    const count = totalCount.get(tail) || 1; // 防止除以0
    globalAverage.set(tail, Number((globalAverage.get(tail) || 0) / count));
  }

  return globalAverage;
};

// 貝葉斯調整
const bayesianAdjust = (result: StatResult, globalAvg: number) => {
  const k = 5; // 平滑係數

  return (
    (result.consecutiveHits + k * globalAvg) /
    (result.consecutiveHits + k)
  );
};

// 尾數出現機率計算
const doAppearScore = () => {
  const past = props.drawResults;
  const recent10 = past.slice(-10);

  const freqScores = calcTailFreqScores(past);
  const markovScores = calcTailMarkovScores(recent10);

  const scores = new Map<number, number>();
  // 將頻率分數和馬可夫鏈分數合併
  freqScores.forEach((f, n) => {
    const m = markovScores.get(n) || 0;
    scores.set(n, (f + m) / 2); // 平均
  });

  // 正規化分數
  const normalizedScores = normalizeScores(scores);

  // 計算分數
  for (const tailNumber of scores.keys()) {
    const score = normalizedScores.get(tailNumber) || 0;
    addScore(tailNumber, score);
  }
};

// 1️⃣ 尾數號碼開出頻率計算，並正規化到0-1
const calcTailFreqScores = (draws: LottoItem[]): Map<number, number> => {
  const freq = new Map<number, number>();
  draws.forEach((draw) => {
    draw.draw_number_size.forEach((number) => {
      const tailNumber = number % 10;
      freq.set(tailNumber, (freq.get(tailNumber) || 0) + 1);
    });

    if (!props.isSuperLotto && draw.special_number) {
      const tailNumber = draw.special_number % 10;
      freq.set(tailNumber, (freq.get(tailNumber) || 0) + 1);
    }
  });

  const max = Math.max(...freq.values());
  const scores = new Map<number, number>();

  freq.forEach((count, number) => scores.set(number, count / max));

  return scores;
};

// 2️⃣ 建立馬可夫鏈轉移矩陣（尾數連莊機率），計算近 10 期
const calcTailMarkovScores = (recent: LottoItem[]): Map<number, number> => {
  const prevCount = new Map<number, number>();
  const transCount = new Map<number, number>();

  for (let i = 1; i < recent.length; i++) {
    const prev = recent[i - 1].tailSet;
    const curr = recent[i].tailSet;

    if (!prev || !curr) continue;
    if (prev.length === 0 || curr.length === 0) continue;

    prev.forEach((n) => {
      prevCount.set(n, (prevCount.get(n) || 0) + 1);
      if (curr.includes(n)) {
        transCount.set(n, (transCount.get(n) || 0) + 1);
      }
    });
  }

  const scores = new Map<number, number>();
  prevCount.forEach((pc, n) => {
    const tc = transCount.get(n) || 0;
    scores.set(n, tc / pc); // 條件機率
  });
  return scores;
};

// 3️⃣ 正規化分數 → normalize
const normalizeScores = (scores: Map<number, number>): Map<number, number> => {
  const vals = Array.from(scores.values());
  const min = Math.min(...vals),
    max = Math.max(...vals);
  const norm = new Map<number, number>();
  scores.forEach((v, n) =>
    norm.set(n, max === min ? 0 : (v - min) / (max - min))
  );
  return norm;
};

const addScore = (tailNumber: number, score: number) => {
  tailScoreMap.value.set(
    tailNumber,
    (tailScoreMap.value.get(tailNumber) || 0) + score
  );
};

const sortMap = (map: Map<number, number>) => {
  const entries = Array.from(map.entries());
  entries.sort((a, b) => b[1] - a[1]);
  return new Map(entries);
};

const pMethodCount = ref(3);
const formatScore = () => {
  for (const tailNumber of tailScoreMap.value.keys()) {
    const score = tailScoreMap.value.get(tailNumber) || 0;
    tailScoreMap.value.set(
      tailNumber,
      Number(((score / pMethodCount.value) * 100).toFixed(1))
    );
  }
};

const tailMatchResults = ref<number[]>([]); // 拖牌 + 尾數
const tailMatchResults2 = ref<number[]>([]); // 拖牌 + 版路
const tailMatchResults3 = ref<number[]>([]); // 尾數 + 版路
const doTailMatch = () => {
  tailMatchResults.value = [];
  tailMatchResults2.value = [];
  tailMatchResults3.value = [];

  const rdTails = Array.from(rdTailAppearances.value.entries())
    .map((entry) => entry[0])
    .slice(0, 5);

  const tails = Array.from(tailRdScoreMap.value.entries())
    .map((entry) => entry[0])
    .slice(0, 5);

  const patterns = Array.from(tailScoreMap.value.entries())
    .map((entry) => entry[0])
    .slice(0, 5);

  // 拖牌 + 尾數
  for (const tailNumber of rdTails) {
    if (tails.includes(tailNumber)) {
      tailMatchResults.value.push(tailNumber);
    }
  }

  // 拖牌 + 版路
  for (const tailNumber of rdTails) {
    if (patterns.includes(tailNumber)) {
      tailMatchResults2.value.push(tailNumber);
    }
  }

  // 尾數 + 版路
  for (const tailNumber of tails) {
    if (patterns.includes(tailNumber)) {
      tailMatchResults3.value.push(tailNumber);
    }
  }

  // 排序 (0排在最後面，其他最小排前面)
  tailMatchResults.value.sort((a, b) => (a === 0 ? 1 : b === 0 ? -1 : a - b));
  tailMatchResults2.value.sort((a, b) => (a === 0 ? 1 : b === 0 ? -1 : a - b));
  tailMatchResults3.value.sort((a, b) => (a === 0 ? 1 : b === 0 ? -1 : a - b));
};

const emptyTailBg = (tails: Iterable<number>) => {
  if (!tails) return '';

  const tailArray = Array.from(tails);

  return tailArray.length === 0 ? '#f8d7da' : '';
};

const isPredictTailNumber = (tailNumber: number) => {
  if (!props.predictResult?.tailSet?.length) return false;

  return props.predictResult.tailSet.includes(tailNumber);
};
</script>

<style>
.q-page-container {
  width: 100%;
}
</style>

<style lang="scss" scoped>
.sticky-virtscroll-table {
  width: max-content;
  max-width: 100%;

  td:first-child {
    background-color: #fff;
  }

  th:first-child,
  td:first-child {
    position: sticky;
    left: 0;
    z-index: 1;
  }

  /* prevent scrolling behind sticky top row on focus */
  tbody {
    /* height of all previous header rows */
    scroll-margin-top: 48px;
  }

  td.fixed-col {
    padding: 4px;
    width: 7rem;
    min-width: 7rem;
    height: 5rem;
    min-height: 5rem;
  }

  th, td {
    border: 1px solid black;
  }

  th:nth-child(1),
  td:nth-child(1),
  th:nth-child(11),
  td:nth-child(11) {
    border-right: 2px solid black;
  }

  td:nth-child(12),
  td:nth-child(13) {
    .ball {
      width: 3rem;
      height: 3rem;
      font-size: 1.3rem;
    }
  }
}

.ball {
  margin-top: 4px;
  margin-bottom: 4px;

  &.text-h6 {
    width: 2.1rem;
    height: 2.1rem;
    font-size: 1.1rem;
  }

  &.text-subtitle1 {
    width: 2rem;
    height: 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .q-card > .q-card__section {
    padding: 0.1rem;
  }

  .q-tab-panel {
    padding: 0.5rem;
  }

  .sticky-virtscroll-table {
    th {
      padding: 0;
      font-size: 0.5rem;
    }

    td:first-child {
      padding: 4px;

      .text-subtitle1 {
        font-size: 0.7rem;
      }
    }

    td.fixed-col {
      padding: 1px;
      width: 1.5rem;
      min-width: 1.5rem;
      height: 1.5rem;
      min-height: 1.5rem;

      .ball {
        margin-right: 2px;
        margin-top: 2px;
        margin-bottom: 2px;
      }
    }

    .ball {
      &.text-h6,
      &.text-subtitle1 {
        width: 0.8rem;
        height: 0.8rem;
        font-size: 0.4rem;
      }
    }

    td:nth-child(12),
    td:nth-child(13) {
      .ball {
        width: 1.8rem;
        height: 1.8rem;
        font-size: 0.8rem;
      }
    }
  }
}
</style>
